import { z } from 'zod';

// More permissive part schemas
const textPartSchema = z.object({
  type: z.string().optional(),
  text: z.string().optional(),
}).passthrough();

// File part schema supporting the requested MIME types
const filePartSchema = z.object({
  type: z.string().optional(),
  mediaType: z.string().optional(),
  name: z.string().optional(),
  url: z.string().optional(),
}).passthrough();

// Union of acceptable parts - more permissive
const partSchema = z.any();

// Message schema - very permissive
const messageSchema = z.object({
  id: z.string().optional(),
  createdAt: z.any().optional(),
  role: z.string().optional(),
  content: z.any().optional(),
  parts: z.array(z.any()).optional(),
  experimental_attachments: z.array(z.any()).optional(),
}).passthrough();

const baseBody = z.object({
  id: z.string().optional(),
  selectedChatModel: z.string().optional(),
  selectedVisibilityType: z.string().optional(),
  timezone: z.string().optional(),
}).passthrough();

export const postRequestBodySchema = z.any();

export type PostRequestBody = z.infer<typeof postRequestBodySchema>;
