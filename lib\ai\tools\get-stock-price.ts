import { tool } from 'ai';
import { z } from 'zod';

export const getStockPrice = tool({
  description: 'Get the current stock price and price change for a given ticker symbol',
  inputSchema: z.object({
    ticker: z
      .string()
      .describe('Stock ticker symbol (e.g. "AAPL" for Apple, "MSFT" for Microsoft)'),
  }),
  execute: async ({ ticker }) => {
    // Validate the ticker
    if (!ticker || typeof ticker !== 'string' || ticker.trim() === '') {
      return { error: 'Please provide a valid stock symbol' };
    }

    // Clean the ticker (remove spaces, convert to uppercase)
    const cleanTicker = ticker.trim().toUpperCase();

    // Return the data needed to display the stock price
    // The frontend component will use this data to display the price widget with TradingView
    return {
      ticker: cleanTicker,
      timestamp: new Date().toISOString(),
    };
  },
});
