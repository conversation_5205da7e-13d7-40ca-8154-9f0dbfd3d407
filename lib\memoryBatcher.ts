import { MemoryClient } from 'mem0ai';
import { memoryConfig } from './memoryConfig';
import { setupMemoryMock } from '../scripts/mock-memory';
import fs from 'node:fs';
import path from 'node:path';
import zlib from 'node:zlib'; // Importation de la bibliothèque pour la compression
import { entries as safeEntries } from '@/lib/safe-entries';

// Déterminer si nous sommes en environnement de test
const isPlaywrightTest = process.env.PLAYWRIGHT === 'True';

// Configurations
const BATCH_INTERVAL_MS = 500; // Réduit de 3000ms à 500ms
const MAX_BATCH_SIZE = 10;
const MAX_ATTEMPTS = 3;
const BACKOFF_BASE = 1000;
const BACKOFF_MAX = 10000;
const MAX_QUEUE_SIZE = 1000;
const QUEUE_PERSISTENCE_FILE = path.join(__dirname, 'memoryQueue.json');
const ENABLE_PERSISTENCE = process.env.MEMORY_PERSIST === 'true';
const DEBUG = process.env.MEMORY_DEBUG === 'true';

// Initialisation client
const client = isPlaywrightTest
  ? setupMemoryMock()
  : memoryConfig.isValid()
    ? new MemoryClient({ apiKey: memoryConfig.apiKey })
    : null;

let flushTimer: NodeJS.Timeout | null = null;
let currentInterval = BATCH_INTERVAL_MS;
let isFlushing = false;

const queue: {
  data: string;
  userId: string;
  metadata: Record<string, any>;
}[] = loadQueueFromDisk();

// Stocker les labels actifs
const activeTimers = new Set<string>();

// Fonction pour démarrer un timer
function startTimer(label: string) {
  activeTimers.add(label);
  console.time(label);
}

// Fonction pour terminer un timer
function endTimer(label: string) {
  if (activeTimers.has(label)) {
    console.timeEnd(label);
    activeTimers.delete(label);
  }
}

// Fonction de débogage
function logDebug(...args: any[]) {
  if (DEBUG) console.log('[MEMORY DEBUG]', ...args);
}

// Charger la queue depuis le disque (persistance optionnelle)
function loadQueueFromDisk() {
  if (ENABLE_PERSISTENCE && fs.existsSync(QUEUE_PERSISTENCE_FILE)) {
    try {
      const data = fs.readFileSync(QUEUE_PERSISTENCE_FILE, 'utf8');
      return JSON.parse(data);
    } catch (err) {
      console.error('❌ Failed to load memory queue from disk:', err);
    }
  }
  return [];
}

// Sauvegarder la queue sur le disque
function saveQueueToDisk() {
  if (ENABLE_PERSISTENCE) {
    try {
      fs.writeFileSync(QUEUE_PERSISTENCE_FILE, JSON.stringify(queue, null, 2));
      logDebug('Queue saved to disk');
    } catch (err) {
      console.error('❌ Failed to save memory queue to disk:', err);
    }
  }
}

// Fonction pour compresser les données avant de les envoyer
function compressData(data: string): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    // Utiliser un niveau de compression plus faible pour plus de rapidité
    zlib.gzip(data, { level: 1 }, (err, compressedData) => {
      if (err) reject(err);
      resolve(compressedData);
    });
  });
}

// Fonction pour envoyer à Mem0
async function sendToMemory(
  data: string,
  userId: string,
  metadata: Record<string, any>,
  categories?: string[],
): Promise<boolean> {
  if (!client || !memoryConfig.isValid()) return false;

  // Test environment, return immediately true
  if (isPlaywrightTest) return true;

  const startTime = Date.now();

  try {
    // Log de début de l'opération
    logDebug(`Starting memory send for user ${userId}`, {
      messageId: metadata?.messageId,
    });

    // Format des messages pour l'API
    let messages = [];

    try {
      // Essayer de parser le contenu s'il est au format JSON
      const parsedContent = JSON.parse(data);

      // Si le contenu est déjà un tableau de messages, l'utiliser directement
      if (Array.isArray(parsedContent)) {
        messages = parsedContent;
      }
      // Si c'est un objet avec une propriété 'messages', utiliser cette propriété
      else if (
        parsedContent.messages &&
        Array.isArray(parsedContent.messages)
      ) {
        messages = parsedContent.messages;
      }
      // Sinon, traiter comme un message unique
      else {
        messages = [
          {
            role: 'user',
            content: parsedContent,
          },
        ];
      }
    } catch (e) {
      // Si le parsing échoue, traiter comme un message simple
      messages = [
        {
          role: 'user',
          content: data,
        },
      ];
    }

    // Configuration du timeout dynamique
    const BASE_TIMEOUT_MS = 30000; // Increased from 15000 to 30000 (30s)
    const TIMEOUT_PER_ITEM_MS = 1000; // 1s per item
    const MAX_TIMEOUT_MS = 120000; // Increased from 60000 to 120000 (2 minutes)

    // Calcul du timeout en fonction de la taille du lot
    const batchSize = messages.length;
    const TIMEOUT_MS = Math.min(
      BASE_TIMEOUT_MS + batchSize * TIMEOUT_PER_ITEM_MS,
      MAX_TIMEOUT_MS,
    );

    logDebug('Using dynamic timeout', {
      batchSize,
      calculatedTimeout: TIMEOUT_MS,
      maxTimeout: MAX_TIMEOUT_MS,
    });

    // Ne compresser que les messages volumineux (> 1KB)
    let compressedData: Buffer | null = null;
    if (data.length > 1024) {
      compressedData = await compressData(data);
      logDebug('Data compressed', {
        originalSize: data.length,
        compressedSize: compressedData.length,
        ratio: `${((compressedData.length / data.length) * 100).toFixed(2)}%`,
      });
    } else {
      logDebug('Skipping compression for small message', { size: data.length });
    }

    let timeoutId: NodeJS.Timeout | null = null;

    // Fonction pour créer la promesse de timeout
    const createTimeoutPromise = (timeoutMs: number) => {
      return new Promise<boolean>((_, reject) => {
        if (timeoutId) clearTimeout(timeoutId);

        timeoutId = setTimeout(() => {
          if (!requestState.isCompleted) {
            requestState.timeout();
            const duration = Date.now() - startTime;
            const error = new Error(
              `Memory send timed out after ${duration}ms (timeout: ${timeoutMs}ms)`,
            );
            error.name = 'MemorySendTimeout';
            logDebug(`Memory send timed out after ${duration}ms`, {
              timeoutMs,
              messageId: metadata?.messageId,
              duration,
            });
            reject(error);
          }
        }, timeoutMs);
      });
    };

    const timeoutPromise = createTimeoutPromise(TIMEOUT_MS);

    // Préparer les métadonnées en nettoyant les valeurs undefined
    const cleanMetadata: Record<string, any> = { ...metadata };
    Object.keys(cleanMetadata).forEach((key) => {
      if (cleanMetadata[key] === undefined) {
        delete cleanMetadata[key];
      }
    });

    // Préparer les options selon la documentation de l'API
    const options: any = {
      // Champs requis
      user_id: userId,
      org_id: memoryConfig.orgId,
      project_id: memoryConfig.projectId,

      // Métadonnées optionnelles
      ...(Object.keys(cleanMetadata).length > 0 && { metadata: cleanMetadata }),

      // Catégories si fournies
      ...(categories && categories.length > 0 && { categories }),
    };

    // Log the data being sent for debugging
    if (process.env.MEMORY_DEBUG === 'true') {
      console.log('📤 Sending to memory:', {
        messages: messages.map((m: { role: string; content: any }) => ({
          ...m,
          content:
            typeof m.content === 'string'
              ? m.content.length > 100
                ? `${m.content.substring(0, 100)}...`
                : m.content
              : '[Object]',
        })),
        options: {
          ...options,
          // Don't log sensitive data
          user_id: '***',
          metadata: {
            ...(options.metadata || {}),
            user_id: '***',
          },
        },
      });
    }

    // Fonction pour envoyer avec réessai
    const sendWithRetry = async (maxRetries = 2, baseDelay = 1000) => {
      let attempt = 0;
      let lastError: any = null;

      while (attempt < maxRetries) {
        attempt++;
        const attemptStartTime = Date.now();

        try {
          logDebug(
            `Sending request to mem0ai API (attempt ${attempt}/${maxRetries})`,
            {
              messageId: metadata?.messageId,
              attempt,
              maxRetries,
              options: {
                ...options,
                user_id: '***',
                metadata: { ...options.metadata, user_id: '***' },
              },
            },
          );

          const response = await client.add(messages, options);
          const duration = Date.now() - startTime;

          logDebug(
            `✅ Memory saved successfully in ${duration}ms (attempt ${attempt})`,
            {
              messageId: metadata?.messageId,
              attempt,
              duration,
              response: response,
            },
          );

          if (process.env.MEMORY_DEBUG === 'true') {
            console.log('✅ Memory saved successfully:', {
              duration: `${duration}ms`,
              attempt,
              response: response,
              messageId: metadata?.messageId,
            });
          }

          return true;
        } catch (error: any) {
          lastError = error;
          const attemptDuration = Date.now() - attemptStartTime;
          const isLastAttempt = attempt >= maxRetries;
          const retryable = isRetryableError(error);

          const errorDetails = {
            error: error.message,
            status: error.status,
            code: error.code,
            attempt,
            maxRetries,
            duration: attemptDuration,
            retryable,
            willRetry: !isLastAttempt && retryable,
            stack:
              process.env.NODE_ENV === 'development' ? error.stack : undefined,
            response: error.response?.data,
            requestData: {
              messages: messages,
              options: {
                ...options,
                user_id: '***',
                metadata: { ...options.metadata, user_id: '***' },
              },
            },
          };

          logDebug(`❌ Memory API error (attempt ${attempt})`, errorDetails);
          console.error(
            `❌ Memory API error (attempt ${attempt}):`,
            errorDetails,
          );

          // Ne pas réessayer si c'est la dernière tentative ou si l'erreur n'est pas réessayable
          if (isLastAttempt || !retryable) {
            throw error;
          }

          // Attendre avant de réessayer (backoff exponentiel)
          const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), 30000); // Max 30s
          logDebug(`Retrying in ${delay}ms...`, { attempt, delay });
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }

      // Ne devrait pas arriver ici, mais au cas où
      throw lastError || new Error('Unknown error in sendWithRetry');
    };

    // Fonction utilitaire pour déterminer si une erreur est réessayable
    const isRetryableError = (error: any): boolean => {
      // Ne pas réessayer pour les erreurs 4xx (sauf 429 - Too Many Requests)
      if (
        error.status &&
        error.status >= 400 &&
        error.status < 500 &&
        error.status !== 429
      ) {
        return false;
      }

      // Réessayer pour les erreurs réseau et timeouts
      if (
        error.code === 'ECONNABORTED' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout') ||
        error.message.includes('Network Error')
      ) {
        return true;
      }

      // Réessayer pour les erreurs 5xx et 429
      return error.status && (error.status >= 500 || error.status === 429);
    };

    // État de la requête
    const requestState = {
      hasTimedOut: false,
      isCompleted: false,
      complete() {
        this.isCompleted = true;
        if (timeoutId) clearTimeout(timeoutId);
      },
      timeout() {
        this.hasTimedOut = true;
      },
    };

    // Fonction pour créer la promesse d'envoi
    const createSendPromise = async () => {
      try {
        const result = await sendWithRetry();
        if (!requestState.hasTimedOut) {
          requestState.complete();
          return result;
        }
        // Si on arrive ici, la requête a réussi mais après le timeout
        logDebug('Request completed after timeout', {
          messageId: metadata?.messageId,
          duration: Date.now() - startTime,
          batchSize: messages.length,
        });
        return result; // On retourne quand même le résultat pour le log
      } catch (error) {
        if (!requestState.hasTimedOut) {
          requestState.complete();
          throw error;
        }
        // Ne rien faire si on a déjà timeouté
        return false;
      }
    };

    try {
      // Créer une nouvelle promesse de timeout avec le bon délai
      const currentTimeoutPromise = createTimeoutPromise(TIMEOUT_MS);

      // Créer la promesse d'envoi
      const sendPromise = createSendPromise();

      // Exécuter la course entre l'envoi et le timeout
      return await Promise.race([sendPromise, currentTimeoutPromise]);
    } catch (error) {
      if (timeoutId && !requestState.isCompleted) {
        clearTimeout(timeoutId);
      }
      throw error;
    }
  } catch (err: any) {
    const duration = Date.now() - startTime;
    const errorMessage = err instanceof Error ? err.message : String(err);
    const errorDetails = {
      error: errorMessage,
      status: err?.status,
      code: err?.code,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString(),
      userId: userId,
      messageId: metadata?.messageId,
      chatId: metadata?.chatId,
      responseData: err?.response?.data,
      metadata: metadata ? JSON.stringify(metadata).substring(0, 200) : 'none',
      stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
    };

    logDebug('❌ Memory send failed', errorDetails);
    console.error('❌ Memory send failed:', errorDetails);

    return false;
  }
}

// Fonction pour vider la queue
async function flushQueue() {
  if (queue.length === 0 || !client || !memoryConfig.isValid() || isFlushing)
    return;

  isFlushing = true;
  const batch = queue.splice(0, MAX_BATCH_SIZE);
  const batchId = new Date().toISOString();
  const timestamp = new Date().toISOString();

  const timerLabel = `memory_batch_${batchId}`;
  console.time(timerLabel);

  try {
    for (let attempt = 1; attempt <= MAX_ATTEMPTS; attempt++) {
      try {
        await Promise.all(
          batch.map(({ data, userId, metadata }) => {
            // Extraire les catégories des métadonnées si elles existent
            let categories: string[] | undefined;

            // Créer une copie des métadonnées sans la propriété _categories
            const { _categories, ...metadataWithoutCategories } = metadata;
            const metadataCopy = { ...metadataWithoutCategories, timestamp };

            if (_categories) {
              categories = _categories;
            }

            return sendToMemory(data, userId, metadataCopy, categories);
          }),
        );

        console.timeEnd(timerLabel);
        console.log(
          `✅ Flushed ${batch.length} memory entries (attempt ${attempt})`,
        );
        return;
      } catch (err) {
        console.error(
          `❌ Failed to flush memory batch (attempt ${attempt}/${MAX_ATTEMPTS}):`,
          err,
        );

        if (attempt < MAX_ATTEMPTS) {
          const delay = Math.min(BACKOFF_BASE * 2 ** attempt, BACKOFF_MAX);
          logDebug(`Retrying in ${delay}ms...`);
          await new Promise((resolve) => setTimeout(resolve, delay));
        } else {
          console.error('All retry attempts failed, requeuing batch');
          queue.unshift(...batch);
          saveQueueToDisk();
        }
      }
    }
  } finally {
    isFlushing = false;
    // Remove the duplicate console.timeEnd call here
  }
}

// Planification du flush
function scheduleFlush() {
  if (!flushTimer) {
    flushTimer = setTimeout(() => {
      flushTimer = null;
      flushQueue();

      if (queue.length > 0) {
        currentInterval = Math.max(500, currentInterval / 2);
        scheduleFlush();
      } else {
        currentInterval = BATCH_INTERVAL_MS;
      }
    }, currentInterval);
  }
}

// Fonction pour ajouter un élément dans la mémoire
export function enqueueMemory({
  data,
  userId,
  metadata,
  categories,
  priority = false,
}: {
  data: string;
  userId: string;
  metadata: Record<string, any>;
  categories?: string[];
  priority?: boolean;
}) {
  if (!data || !userId) {
    console.error('Invalid memory entry: missing data or userId');
    return;
  }

  if (!memoryConfig.isValid() || !client) {
    console.error('Memory client not properly configured.');
    return;
  }

  if (typeof metadata !== 'object' || metadata === null) {
    console.error('Invalid metadata format');
    return;
  }

  try {
    metadata = JSON.parse(JSON.stringify(metadata));
  } catch (err) {
    console.error('Metadata serialization failed:', err);
    return;
  }

  // Envoi immédiat pour les éléments prioritaires
  if (priority) {
    sendToMemory(data, userId, metadata, categories)
      .then((success) => {
        if (!success) {
          // En cas d'échec, ajouter à la queue normale
          addToQueue(data, userId, metadata, categories);
        }
      })
      .catch(() => {
        addToQueue(data, userId, metadata, categories);
      });
    return;
  }

  addToQueue(data, userId, metadata, categories);
}

// Fonction auxiliaire pour ajouter à la queue
function addToQueue(
  data: string,
  userId: string,
  metadata: Record<string, any>,
  categories?: string[],
) {
  if (queue.length >= MAX_QUEUE_SIZE) {
    console.warn('⚠️ Queue is full, dropping oldest entry');
    queue.shift();
  }

  // Ajouter les catégories aux métadonnées pour les stocker dans la queue
  if (categories && categories.length > 0) {
    metadata._categories = categories;
  }

  queue.push({ data, userId, metadata });
  saveQueueToDisk();

  if (queue.length >= MAX_BATCH_SIZE) {
    if (!isFlushing) flushQueue();
    if (flushTimer) {
      clearTimeout(flushTimer);
      flushTimer = null;
    }
  } else {
    scheduleFlush();
  }
}

// Fonction pour ajouter une entrée combinée dans la mémoire
export function enqueueCombinedMemory({
  entries,
  userId,
}: {
  entries: { data: string; action: string; metadata?: Record<string, any> }[];
  userId: string;
}) {
  if (!entries.length || !userId || !memoryConfig.isValid()) {
    console.error('Invalid combined memory entry');
    return;
  }

  const combinedData = entries.map((entry) => entry.data).join('\n\n---\n\n');

  const combinedMetadata: Record<string, any> = {
    actions: entries.map((entry) => entry.action),
    timestamp: new Date().toISOString(),
  };

  entries.forEach((entry, index) => {
    if (entry.metadata && typeof entry.metadata === 'object') {
      safeEntries(entry.metadata).forEach(([key, value]) => {
        combinedMetadata[`${key}_${index}`] = value;
      });
    }
  });

  enqueueMemory({
    data: combinedData,
    userId,
    metadata: combinedMetadata,
  });
}

// Obtenir l'état de la queue
export function getQueueStatus() {
  return {
    length: queue.length,
    isFlushing,
    flushTimerActive: !!flushTimer,
  };
}
