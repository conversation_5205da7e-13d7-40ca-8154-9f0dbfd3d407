import { NextResponse } from 'next/server';

// Simple mapper to our frontend-friendly shape
function mapPlaceDetails(result: any) {
  const reviews = Array.isArray(result.reviews)
    ? result.reviews.map((r: any) => ({
        author_name: r.author_name,
        rating: typeof r.rating === 'number' ? r.rating : undefined,
        text: r.text,
        relative_time_description: r.relative_time_description,
        time_description: r.time_description,
      }))
    : undefined;

  const photos = Array.isArray(result.photos)
    ? result.photos.slice(0, 6).map((p: any) => ({
        photo_reference: p.photo_reference,
        width: p.width,
        height: p.height,
        // Compose a photo URL using the ref; the frontend may proxy/validate
        url: p.photo_reference
          ? `https://maps.googleapis.com/maps/api/place/photo?maxwidth=800&photo_reference=${encodeURIComponent(
              p.photo_reference,
            )}&key=${process.env.GOOGLE_PLACES_API_KEY}`
          : undefined,
      }))
    : undefined;

  return {
    rating: typeof result.rating === 'number' ? result.rating : undefined,
    reviews_count: typeof result.user_ratings_total === 'number' ? result.user_ratings_total : undefined,
    reviews,
    opening_hours: result.opening_hours?.weekday_text,
    price_level: result.price_level,
    formatted_address: result.formatted_address,
    website: result.website,
    phone: result.international_phone_number || result.formatted_phone_number,
    photos,
  } as const;
}

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const placeId = searchParams.get('place_id');

    if (!placeId) {
      return NextResponse.json({ error: 'place_id is required' }, { status: 400 });
    }

    const apiKey = process.env.GOOGLE_PLACES_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'GOOGLE_PLACES_API_KEY is not configured on the server' },
        { status: 500 },
      );
    }

    const fields = [
      'rating',
      'user_ratings_total',
      'reviews',
      'opening_hours/weekday_text',
      'price_level',
      'formatted_address',
      'website',
      'international_phone_number',
      'formatted_phone_number',
      'photos',
    ].join(',');

    const url = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${encodeURIComponent(
      placeId,
    )}&fields=${encodeURIComponent(fields)}&language=fr&key=${apiKey}`;

    const res = await fetch(url, { cache: 'no-store' });
    const data = await res.json();

    if (!res.ok || data.status !== 'OK') {
      return NextResponse.json(
        { error: 'Places Details request failed', status: data.status, message: data.error_message },
        { status: 502 },
      );
    }

    const mapped = mapPlaceDetails(data.result || {});
    return NextResponse.json({ place_id: placeId, details: mapped }, { status: 200 });
  } catch (err) {
    const msg = err instanceof Error ? err.message : String(err);
    return NextResponse.json({ error: 'Unexpected error', message: msg }, { status: 500 });
  }
}
