import React, { useEffect, useCallback, useRef, useMemo, memo } from 'react';
import { Streamdown } from 'streamdown';

interface MarkdownProps {
  /** Contenu Markdown à rendre */
  children: string;
  /** Classes CSS additionnelles */
  className?: string;
  /** Activer le proxy d'images (défaut: true) */
  enableImageProxy?: boolean;
  /** URL de base pour le proxy d'images */
  proxyBaseUrl?: string;
  /** Callback appelé en cas d'erreur de chargement d'image */
  onImageError?: (originalSrc: string, error: Event) => void;
  /** Convertir les liens inline en citations numérotées avec tooltip (défaut: false) */
  enableCitations?: boolean;
}

/**
 * Composant pour rendre du Markdown avec gestion avancée des images et styles optimisés
 */
export const Markdown = memo<MarkdownProps>(
  ({
    children,
    className = '',
    enableImageProxy = true,
    proxyBaseUrl = '/api/proxy-image',
    onImageError,
    enableCitations = false,
  }) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const processedImages = useRef(new Set<string>());

    // Compteur global pour les groupId (persiste entre les appels)
    const groupIdCounter = useRef(0);

    /**
     * Convertit les liens HTML en citations groupées par proximité
     */
    const convertLinksToCitations = useCallback(() => {
      if (!enableCitations || !containerRef.current) return;

      const links = Array.from(
        containerRef.current.querySelectorAll('a[href^="http"]'),
      ) as HTMLAnchorElement[];

      // Grouper les liens par paragraphe (<p> tag)
      const groups: HTMLAnchorElement[][] = [];
      const paragraphGroups = new Map<HTMLElement, HTMLAnchorElement[]>();

      links.forEach((link) => {
        if (link.dataset.citationProcessed) return;

        // Trouver le paragraphe <p> parent
        let paragraph: HTMLElement | null = link.parentElement;
        while (
          paragraph &&
          paragraph !== containerRef.current &&
          paragraph.tagName !== 'P'
        ) {
          paragraph = paragraph.parentElement;
        }

        // Si pas de <p>, utiliser le parent direct
        if (!paragraph || paragraph === containerRef.current) {
          paragraph = link.parentElement as HTMLElement;
        }

        // Grouper par paragraphe
        if (!paragraphGroups.has(paragraph)) {
          paragraphGroups.set(paragraph, []);
        }
        const group = paragraphGroups.get(paragraph);
        if (group) {
          group.push(link);
        }
      });

      // Convertir en array
      paragraphGroups.forEach((group) => {
        if (group.length > 0) {
          groups.push(group);
        }
      });

      console.log(
        '📚 Groups by paragraph:',
        groups.length,
        'groups -',
        groups.map((g) => `${g.length} links`),
      );

      // Traiter chaque groupe
      groups.forEach((group) => {
        // Créer une Map des URLs uniques avec leurs infos
        const uniqueUrlsMap = new Map<
          string,
          { url: string; title: string; sourceName: string }
        >();

        group.forEach((link) => {
          const url = link.href;
          const title = link.textContent || '';

          // Extraire le nom de domaine
          let sourceName = '';
          try {
            const urlObj = new URL(url);
            sourceName = urlObj.hostname.replace('www.', '');
            if (sourceName.length > 20) {
              sourceName = `${sourceName.substring(0, 17)}...`;
            }
          } catch {
            sourceName = 'Source';
          }

          // Ajouter à la map si pas déjà présent
          if (!uniqueUrlsMap.has(url)) {
            uniqueUrlsMap.set(url, { url, title, sourceName });
          }
        });

        const uniqueCount = uniqueUrlsMap.size;
        const firstSource = Array.from(uniqueUrlsMap.values())[0];
        const displaySourceName = firstSource?.sourceName || 'Source';

        // Utiliser un groupId unique et persistant
        const currentGroupId = groupIdCounter.current++;

        console.log('📊 Citation badge:', {
          displaySourceName,
          uniqueCount,
          totalLinksInGroup: group.length,
          groupId: currentGroupId,
          uniqueUrls: Array.from(uniqueUrlsMap.keys()),
        });

        // Transformer chaque lien du groupe
        group.forEach((link, index) => {
          const url = link.href;
          const sourceInfo = uniqueUrlsMap.get(url);

          // Marquer comme traité
          link.dataset.citationProcessed = 'true';
          link.dataset.citationUrl = url;
          link.dataset.citationTitle = sourceInfo?.title || '';
          link.dataset.sourceName = sourceInfo?.sourceName || '';
          link.dataset.groupId = String(currentGroupId);
          link.dataset.groupUniqueCount = String(uniqueCount);
          link.className = 'citation-ref';
          link.style.textDecoration = 'none';
          link.style.color = 'inherit';

          // Afficher seulement le premier lien du groupe
          if (index === 0) {
            if (uniqueCount > 1) {
              link.textContent = `${displaySourceName} +${uniqueCount - 1}`;
            } else {
              link.textContent = displaySourceName;
            }
          } else {
            // Cacher les autres liens du groupe
            link.style.display = 'none';
          }

          // Empêcher la navigation
          link.onclick = (e) => {
            e.preventDefault();
            return false;
          };
        });
      });
    }, [enableCitations]);

    /**
     * Gère le proxy des images avec fallback et gestion d'erreur améliorée
     */
    const handleImageProxy = useCallback(() => {
      if (!enableImageProxy || !containerRef.current) return;

      try {
        const images = containerRef.current.querySelectorAll('img');

        images.forEach((imgElement) => {
          const originalSrc = imgElement.src;

          // Éviter de traiter les images déjà proxifiées ou traitées
          if (
            originalSrc.includes(proxyBaseUrl) ||
            processedImages.current.has(originalSrc)
          )
            return;

          // Marquer comme traitée
          processedImages.current.add(originalSrc);

          // Améliorer l'accessibilité
          if (!imgElement.alt) {
            imgElement.alt = 'Image';
          }
          imgElement.setAttribute('loading', 'lazy');

          // Gestionnaire d'erreur amélioré
          const handleError = (event: Event) => {
            if (!imgElement.src.includes(proxyBaseUrl)) {
              let cleanUrl = originalSrc;

              try {
                if (cleanUrl.includes('%')) {
                  cleanUrl = decodeURIComponent(cleanUrl);
                }
              } catch (decodeError) {
                console.warn('Failed to decode URL:', originalSrc, decodeError);
                cleanUrl = originalSrc;
              }

              const proxyUrl = `${proxyBaseUrl}?url=${encodeURIComponent(cleanUrl)}`;
              imgElement.src = proxyUrl;
            } else {
              // Si même le proxy échoue, afficher une image de fallback
              imgElement.style.display = 'none';
              console.warn(
                'Image failed to load even through proxy:',
                originalSrc,
              );
            }

            // Appeler le callback d'erreur si fourni
            onImageError?.(originalSrc, event);
          };

          imgElement.addEventListener('error', handleError, { once: true });

          // Nettoyer l'event listener si l'image se charge correctement
          imgElement.addEventListener(
            'load',
            () => {
              imgElement.removeEventListener('error', handleError);
            },
            { once: true },
          );
        });
      } catch (error) {
        console.error('Error in handleImageProxy:', error);
      }
    }, [enableImageProxy, proxyBaseUrl, onImageError]);

    /**
     * Gère les tooltips pour les citations (cliquables et persistants)
     */
    const handleCitationTooltips = useCallback(() => {
      if (!enableCitations || !containerRef.current) return;

      const citations = containerRef.current.querySelectorAll('.citation-ref');
      let activeTooltip: HTMLElement | null = null;
      let hideTooltipTimeout: NodeJS.Timeout | null = null;

      const removeTooltip = () => {
        if (activeTooltip) {
          activeTooltip.remove();
          activeTooltip = null;
        }
        if (hideTooltipTimeout) {
          clearTimeout(hideTooltipTimeout);
          hideTooltipTimeout = null;
        }
      };

      citations.forEach((citation) => {
        const citationEl = citation as HTMLElement;
        const groupId = citationEl.dataset.groupId || '';

        const showTooltip = () => {
          // Annuler le timeout de fermeture si existant
          if (hideTooltipTimeout) {
            clearTimeout(hideTooltipTimeout);
            hideTooltipTimeout = null;
          }

          // Ne pas recréer si déjà actif
          if (activeTooltip) return;

          // Trouver tous les liens du même groupe (y compris les cachés)
          const allCitationsInGroup = Array.from(
            containerRef.current?.querySelectorAll('.citation-ref') || [],
          ).filter((c) => {
            const el = c as HTMLElement;
            return el.dataset.groupId === groupId;
          }) as HTMLElement[];

          console.log('🔍 Tooltip - Citations found:', {
            groupId,
            totalCitationsInGroup: allCitationsInGroup.length,
            citationUrls: allCitationsInGroup.map(
              (c) => (c as HTMLElement).dataset.citationUrl,
            ),
          });

          // Obtenir les URLs uniques
          const uniqueSources = new Map<
            string,
            { url: string; title: string; sourceName: string }
          >();
          allCitationsInGroup.forEach((cite) => {
            const url = cite.dataset.citationUrl || '';
            if (!uniqueSources.has(url)) {
              uniqueSources.set(url, {
                url,
                title: cite.dataset.citationTitle || '',
                sourceName: cite.dataset.sourceName || '',
              });
            }
          });

          const sources = Array.from(uniqueSources.values());

          console.log('🔍 Tooltip - Unique sources:', {
            uniqueSourcesCount: sources.length,
            sourceNames: sources.map((s) => s.sourceName),
            allUrlsFound: allCitationsInGroup.map((c) => c.dataset.citationUrl),
            uniqueUrlsInMap: Array.from(uniqueSources.keys()),
          });

          // Trouver l'index de la source correspondant au badge cliqué
          const clickedUrl = citationEl.dataset.citationUrl || '';
          let currentSourceIndex = sources.findIndex(
            (s) => s.url === clickedUrl,
          );
          if (currentSourceIndex === -1) currentSourceIndex = 0;

          // Créer le tooltip
          const tooltip = document.createElement('div');
          tooltip.className = 'citation-tooltip visible';

          // Navigation avec flèches (si plusieurs sources)
          if (sources.length > 1) {
            const navContainer = document.createElement('div');
            navContainer.className = 'citation-tooltip-nav';

            const buttonsContainer = document.createElement('div');
            buttonsContainer.className = 'citation-tooltip-nav-buttons';

            const prevButton = document.createElement('button');
            prevButton.className = 'citation-tooltip-nav-btn';
            prevButton.innerHTML = '←';
            prevButton.onclick = (e) => {
              e.stopPropagation();
              currentSourceIndex =
                currentSourceIndex > 0
                  ? currentSourceIndex - 1
                  : sources.length - 1;
              updateTooltipContent();
            };

            const nextButton = document.createElement('button');
            nextButton.className = 'citation-tooltip-nav-btn';
            nextButton.innerHTML = '→';
            nextButton.onclick = (e) => {
              e.stopPropagation();
              currentSourceIndex =
                currentSourceIndex < sources.length - 1
                  ? currentSourceIndex + 1
                  : 0;
              updateTooltipContent();
            };

            buttonsContainer.appendChild(prevButton);
            buttonsContainer.appendChild(nextButton);

            const counter = document.createElement('div');
            counter.className = 'citation-tooltip-counter';
            counter.textContent = `${currentSourceIndex + 1}/${sources.length}`;

            navContainer.appendChild(buttonsContainer);
            navContainer.appendChild(counter);
            tooltip.appendChild(navContainer);
          }

          // Conteneur pour le contenu de la source
          const contentContainer = document.createElement('div');
          contentContainer.className = 'citation-tooltip-content';
          tooltip.appendChild(contentContainer);

          // Fonction pour mettre à jour le contenu
          const updateTooltipContent = () => {
            const source = sources[currentSourceIndex];
            contentContainer.innerHTML = '';

            // Icône et nom de la source
            const sourceHeader = document.createElement('div');
            sourceHeader.className = 'citation-tooltip-source-header';

            // Utiliser le favicon du site
            const sourceIcon = document.createElement('img');
            sourceIcon.className = 'citation-tooltip-source-icon';

            // Extraire le domaine pour le favicon
            try {
              const urlObj = new URL(source.url);
              const domain = urlObj.hostname;
              // Utiliser Google's favicon service
              sourceIcon.src = `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
              sourceIcon.alt = source.sourceName;
              sourceIcon.onerror = () => {
                // Fallback si le favicon ne charge pas
                sourceIcon.style.display = 'none';
                const fallbackIcon = document.createElement('div');
                fallbackIcon.className =
                  'citation-tooltip-source-icon-fallback';
                fallbackIcon.textContent = '🌐';
                sourceIcon.parentElement?.insertBefore(
                  fallbackIcon,
                  sourceIcon,
                );
              };
            } catch {
              sourceIcon.src =
                'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><text y="20" font-size="20">🌐</text></svg>';
            }

            const sourceName = document.createElement('div');
            sourceName.className = 'citation-tooltip-source-name';
            sourceName.textContent = source.sourceName;

            sourceHeader.appendChild(sourceIcon);
            sourceHeader.appendChild(sourceName);
            contentContainer.appendChild(sourceHeader);

            // Titre de l'article
            const linkTitle = document.createElement('div');
            linkTitle.className = 'citation-tooltip-link-title';
            linkTitle.textContent =
              source.title || `Source ${currentSourceIndex + 1}`;
            contentContainer.appendChild(linkTitle);

            // URL cliquable
            const linkUrl = document.createElement('a');
            linkUrl.className = 'citation-tooltip-url';
            linkUrl.href = source.url;
            linkUrl.target = '_blank';
            linkUrl.rel = 'noopener noreferrer';
            linkUrl.textContent = source.url;
            contentContainer.appendChild(linkUrl);

            // Mettre à jour le compteur
            if (sources.length > 1) {
              const counter = tooltip.querySelector(
                '.citation-tooltip-counter',
              ) as HTMLElement;
              if (counter) {
                counter.textContent = `${currentSourceIndex + 1}/${sources.length}`;
              }
            }
          };

          // Afficher la première source
          updateTooltipContent();
          document.body.appendChild(tooltip);

          // Positionner le tooltip
          const rect = citationEl.getBoundingClientRect();
          const tooltipRect = tooltip.getBoundingClientRect();

          const top = rect.bottom + window.scrollY + 8;
          let left =
            rect.left + window.scrollX - tooltipRect.width / 2 + rect.width / 2;

          // Ajuster si le tooltip dépasse à droite
          if (left + tooltipRect.width > window.innerWidth) {
            left = window.innerWidth - tooltipRect.width - 16;
          }

          // Ajuster si le tooltip dépasse à gauche
          if (left < 16) {
            left = 16;
          }

          tooltip.style.top = `${top}px`;
          tooltip.style.left = `${left}px`;

          activeTooltip = tooltip;

          // Garder le tooltip ouvert quand la souris est dessus
          tooltip.addEventListener('mouseenter', () => {
            if (hideTooltipTimeout) {
              clearTimeout(hideTooltipTimeout);
              hideTooltipTimeout = null;
            }
          });

          tooltip.addEventListener('mouseleave', () => {
            hideTooltipTimeout = setTimeout(removeTooltip, 300);
          });
        };

        citationEl.addEventListener('mouseenter', showTooltip);

        citationEl.addEventListener('mouseleave', () => {
          // Délai avant de fermer pour permettre de passer sur le tooltip
          hideTooltipTimeout = setTimeout(removeTooltip, 300);
        });
      });
    }, [enableCitations]);

    /**
     * Observe les changements dans le DOM pour traiter les nouvelles images et citations
     */
    useEffect(() => {
      if (!containerRef.current) return;

      const processedImagesRef = processedImages.current;

      const observer = new MutationObserver((mutations) => {
        let hasNewImages = false;
        let hasNewLinks = false;

        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as Element;
                if (element.tagName === 'IMG' || element.querySelector('img')) {
                  hasNewImages = true;
                }
                if (
                  element.tagName === 'A' ||
                  element.querySelector('a[href^="http"]')
                ) {
                  hasNewLinks = true;
                }
              }
            });
          }
        });

        if (hasNewImages) {
          setTimeout(handleImageProxy, 50);
        }
        if (hasNewLinks && enableCitations) {
          setTimeout(() => {
            convertLinksToCitations();
            handleCitationTooltips();
          }, 50);
        }
      });

      observer.observe(containerRef.current, {
        childList: true,
        subtree: true,
      });

      // Traitement initial
      handleImageProxy();
      if (enableCitations) {
        setTimeout(() => {
          convertLinksToCitations();
          handleCitationTooltips();
        }, 100);
      }

      return () => {
        observer.disconnect();
        processedImagesRef.clear();
      };
    }, [
      handleImageProxy,
      handleCitationTooltips,
      convertLinksToCitations,
      enableCitations,
    ]);

    /**
     * Styles CSS optimisés et mémorisés
     */
    const styles = useMemo(
      () => ({
        __html: `
      .prose-container {
        max-width: 100% !important;
        overflow-wrap: break-word !important;
        word-wrap: break-word !important;
        word-break: break-word !important;
        hyphens: auto !important;
        overflow-x: hidden !important;
      }
      
      /* Style pour les paragraphes et éléments de texte */
      .prose-container p, 
      .prose-container li, 
      .prose-container span, 
      .prose-container div:not(.code-block) {
        overflow-wrap: break-word !important;
        word-wrap: break-word !important;
        word-break: break-word !important;
        hyphens: auto !important;
        max-width: 100% !important;
      }
      
      /* Style spécifique pour les URLs longues */
      .prose-container a {
        overflow-wrap: break-word !important;
        word-break: break-all !important;
        display: inline-block;
        max-width: 100% !important;
      }
      
      .prose-container pre {
        overflow-x: auto;
        max-width: 100%;
        width: 100%;
        border-radius: 0.5rem;
        background-color: #1f2937;
        padding: 1rem;
        margin: 1rem 0;
        box-sizing: border-box;
        word-wrap: break-word;
        overflow-wrap: break-word;
        position: relative;
      }
      
      .prose-container code {
        background-color: #374151;
        color: #f9fafb;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        word-break: break-words;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }
      
      .prose-container pre code {
        background-color: transparent;
        padding: 0;
        color: #f9fafb;
        white-space: pre-wrap;
        overflow-x: auto;
        display: block;
        word-break: break-all;
        overflow-wrap: anywhere;
        max-width: 100%;
      }
      
      /* Syntax highlighting amélioré */
      .prose-container pre code .hljs-keyword { color: #c678dd; font-weight: 500; }
      .prose-container pre code .hljs-string { color: #98c379; }
      .prose-container pre code .hljs-number { color: #d19a66; }
      .prose-container pre code .hljs-comment { color: #5c6370; font-style: italic; opacity: 0.8; }
      .prose-container pre code .hljs-function { color: #61afef; font-weight: 500; }
      .prose-container pre code .hljs-variable { color: #e06c75; }
      .prose-container pre code .hljs-built_in { color: #e5c07b; font-weight: 500; }
      .prose-container pre code .hljs-tag { color: #e06c75; }
      .prose-container pre code .hljs-attr { color: #d19a66; }
      .prose-container pre code .hljs-attribute { color: #d19a66; }
      .prose-container pre code .hljs-title { color: #61afef; font-weight: 600; }
      .prose-container pre code .hljs-type { color: #e5c07b; }
      .prose-container pre code .hljs-literal { color: #56b6c2; }
      .prose-container pre code .hljs-meta { color: #abb2bf; }
      .prose-container pre code .hljs-selector-tag { color: #e06c75; }
      .prose-container pre code .hljs-selector-class { color: #e5c07b; }
      .prose-container pre code .hljs-selector-id { color: #61afef; }
      
      /* Images avec gestion d'erreur et performance améliorées */
      .prose-container img {
        max-width: 100%;
        height: auto;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        margin: 1rem 0;
        display: block;
        transition: opacity 0.3s ease, transform 0.2s ease;
      }
      
      .prose-container img:hover {
        transform: scale(1.02);
        box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15), 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }
      
      /* Placeholder pour images en cours de chargement */
      .prose-container img[src*="${proxyBaseUrl}"] {
        background: linear-gradient(45deg, #374151, #4b5563);
        background-size: 400% 400%;
        animation: gradientShift 2s ease-in-out infinite alternate;
        min-height: 200px;
      }
      
      @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        100% { background-position: 100% 50%; }
      }
      
      /* Tableaux améliorés */
      .prose-container table {
        width: 100%;
        border-collapse: collapse;
        margin: 1.5rem 0;
        background-color: #1f2937;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }
      
      .prose-container th {
        background-color: #374151;
        color: #f9fafb;
        padding: 1rem 0.75rem;
        text-align: left;
        font-weight: 600;
        border-bottom: 2px solid #4b5563;
        position: relative;
      }
      
      .prose-container td {
        padding: 0.75rem;
        border-bottom: 1px solid #374151;
        color: #d1d5db;
        transition: background-color 0.2s ease;
      }
      
      .prose-container tr:hover td {
        background-color: #374151;
      }
      
      .prose-container tr:last-child td {
        border-bottom: none;
      }
      
      /* Titres avec hiérarchie visuelle améliorée */
      .prose-container h1, .prose-container h2, .prose-container h3, 
      .prose-container h4, .prose-container h5, .prose-container h6 {
        color: #f9fafb;
        font-weight: 600;
        margin-top: 2rem;
        margin-bottom: 1rem;
        line-height: 1.25;
        scroll-margin-top: 2rem;
      }
      
      .prose-container h1 { 
        font-size: 2rem; 
        border-bottom: 2px solid #4b5563; 
        padding-bottom: 0.5rem;
      }
      .prose-container h2 { 
        font-size: 1.5rem; 
        color: #e5e7eb;
      }
      .prose-container h3 { 
        font-size: 1.25rem; 
        color: #d1d5db;
      }
      
      /* Listes améliorées */
      .prose-container ul, .prose-container ol {
        padding-left: 1.5rem;
        margin: 1rem 0;
      }
      
      .prose-container li {
        margin: 0.5rem 0;
        color: #d1d5db;
        line-height: 1.6;
      }
      
      /* Blockquotes */
      .prose-container blockquote {
        border-left: 4px solid #4b5563;
        padding-left: 1rem;
        margin: 1.5rem 0;
        font-style: italic;
        color: #9ca3af;
        background-color: #1f2937;
        border-radius: 0 0.25rem 0.25rem 0;
        padding: 1rem;
      }
      
      /* Liens */
      .prose-container a {
        color: #60a5fa;
        text-decoration: none;
        transition: color 0.2s ease;
      }
      
      .prose-container a:hover {
        color: #93c5fd;
        text-decoration: underline;
      }
      
      /* Citations avec nom de source */
      .prose-container .citation-ref {
        display: inline-block;
        background-color: #374151;
        color: #9ca3af;
        padding: 0.125rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.7rem;
        font-weight: 500;
        cursor: pointer;
        margin: 0 0.25rem;
        transition: all 0.2s ease;
        position: relative;
        white-space: nowrap;
        border: 1px solid #4b5563;
      }
      
      .prose-container .citation-ref:hover {
        background-color: #4b5563;
        color: #e5e7eb;
        border-color: #60a5fa;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
      
      /* Tooltip pour les citations (carrousel) */
      .citation-tooltip {
        position: absolute;
        background-color: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 0;
        min-width: 320px;
        max-width: 400px;
        z-index: 1000;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
        pointer-events: auto;
        opacity: 0;
        transition: opacity 0.2s ease;
        overflow: hidden;
      }
      
      .citation-tooltip.visible {
        opacity: 1;
      }
      
      /* Navigation avec flèches */
      .citation-tooltip-nav {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.5rem 0.75rem;
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
      }
      
      .citation-tooltip-nav-buttons {
        display: flex;
        align-items: center;
        gap: 0.25rem;
      }
      
      .citation-tooltip-nav-btn {
        background: none;
        border: none;
        color: #6b7280;
        font-size: 1.25rem;
        cursor: pointer;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .citation-tooltip-nav-btn:hover {
        background-color: #e5e7eb;
        color: #111827;
      }
      
      .citation-tooltip-counter {
        color: #6b7280;
        font-size: 0.875rem;
        font-weight: 500;
      }
      
      /* Contenu de la source */
      .citation-tooltip-content {
        padding: 1rem;
      }
      
      .citation-tooltip-source-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
      }
      
      .citation-tooltip-source-icon {
        width: 20px;
        height: 20px;
        border-radius: 0.25rem;
        object-fit: contain;
      }
      
      .citation-tooltip-source-icon-fallback {
        font-size: 1.25rem;
        line-height: 1;
      }
      
      .citation-tooltip-source-name {
        color: #111827;
        font-size: 0.875rem;
        font-weight: 600;
      }
      
      .citation-tooltip-link-title {
        color: #111827;
        font-size: 0.9375rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
        line-height: 1.4;
      }
      
      .citation-tooltip-url {
        color: #2563eb;
        font-size: 0.8125rem;
        word-break: break-all;
        display: block;
        line-height: 1.4;
        text-decoration: none;
      }
      
      .citation-tooltip-url:hover {
        color: #1d4ed8;
        text-decoration: underline;
      }
      
      /* Responsive design */
      @media (max-width: 768px) {
        .prose-container {
          padding: 0.5rem;
        }
        
        .prose-container pre {
          padding: 0.75rem;
          border-radius: 0.25rem;
        }
        
        .prose-container h1 { font-size: 1.75rem; }
        .prose-container h2 { font-size: 1.5rem; }
        
        .prose-container table {
          font-size: 0.875rem;
        }
        
        .prose-container th, .prose-container td {
          padding: 0.5rem;
        }
      }
    `,
      }),
      [proxyBaseUrl],
    );

    return (
      <div
        ref={containerRef}
        className={`prose prose-invert max-w-full break-words prose-container ${className}`}
        role="article"
        aria-label="Contenu Markdown"
      >
        <style dangerouslySetInnerHTML={styles} />
        <Streamdown>{children}</Streamdown>
      </div>
    );
  },
);

// Définir le displayName pour le debugging
Markdown.displayName = 'Markdown';
