'use client';

import { useState, useEffect } from 'react';
import type { Place } from '@/lib/types';
import PlaceCard from './place-card';
import { ChevronUp, ChevronDown, ToggleLeft, ToggleRight } from 'lucide-react';
import PlaceCardsCarousel from './place-cards-carousel';
import { enrichPlaceWithContextualData } from '@/lib/places/contextual-reviews';

interface EnhancedLocationDisplayProps {
  places: Place[];
  selectedPlaceId?: string | null;
  onLocationClick?: (place: Place) => void;
  useElevator?: boolean;
  onToggleMode?: (useElevator: boolean) => void;
}

// Adaptateur pour convertir notre type Place vers le type PlaceCard
const adaptPlaceForCard = (place: Place) => {
  // Générer des horaires d'exemple si pas disponibles
  const generateSampleHours = () => {
    const days = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ];
    return days.map((day) => {
      if (day === 'Sunday') {
        return `${day}: 10:00 AM – 6:00 PM`;
      } else if (day === 'Saturday') {
        return `${day}: 9:00 AM – 8:00 PM`;
      } else {
        return `${day}: 8:00 AM – 9:00 PM`;
      }
    });
  };

  return {
    name: place.title,
    location: {
      lat: place.latitude,
      lng: place.longitude,
    },
    place_id: place.cid,
    vicinity: place.address,
    rating: place.rating,
    photos: place.images?.map((img) => ({
      photo_reference: img.url,
      width: 400,
      height: 300,
      url: img.url,
      caption: img.description,
    })),
    phone: place.phoneNumber,
    website: place.website,
    type: place.category,
    // Ajouter des données enrichies
    reviews_count: undefined,
    // Ne pas injecter d'avis factices; laisser undefined s'il n'y a pas de vrais avis
    reviews: undefined,
    price_level: place.category?.toLowerCase().includes('restaurant')
      ? Math.floor(Math.random() * 3) + 1
      : undefined,
    description: undefined,
    is_closed: undefined,
    is_open: Math.random() > 0.3, // 70% de chance d'être ouvert
    next_open_close: undefined,
    cuisine: place.category?.toLowerCase().includes('restaurant')
      ? place.category
      : undefined,
    source: 'Serper',
    hours: generateSampleHours(),
    opening_hours: generateSampleHours(),
    distance: undefined,
    bearing: undefined,
    timezone: 'Europe/Paris', // Timezone par défaut pour la France
  };
};

const EnhancedLocationDisplay: React.FC<EnhancedLocationDisplayProps> = ({
  places,
  selectedPlaceId,
  onLocationClick,
  useElevator = true,
  onToggleMode,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Sélectionner automatiquement la première location au chargement
  useEffect(() => {
    if (useElevator && places.length > 0 && onLocationClick) {
      onLocationClick(places[0]);
    }
  }, [useElevator, places, onLocationClick]);

  // Synchroniser l'index de l'ascenseur avec la location sélectionnée depuis la carte
  useEffect(() => {
    if (useElevator && selectedPlaceId && places.length > 0) {
      const selectedIndex = places.findIndex(
        (place) => place.cid === selectedPlaceId,
      );
      if (selectedIndex !== -1 && selectedIndex !== currentIndex) {
        setCurrentIndex(selectedIndex);
      }
    }
  }, [selectedPlaceId, places, useElevator, currentIndex]);

  // Mode ascenseur avec PlaceCard
  if (useElevator && places.length > 1) {
    const currentPlace = places[currentIndex];
    const base = adaptPlaceForCard(currentPlace);
    const contextualData = enrichPlaceWithContextualData(currentPlace);
    const adaptedPlace = {
      ...base,
      ...contextualData,
    };

    const goUp = () => {
      if (currentIndex > 0) {
        const newIndex = currentIndex - 1;
        setCurrentIndex(newIndex);
        onLocationClick?.(places[newIndex]);
      }
    };

    const goDown = () => {
      if (currentIndex < places.length - 1) {
        const newIndex = currentIndex + 1;
        setCurrentIndex(newIndex);
        onLocationClick?.(places[newIndex]);
      }
    };

    const goToIndex = (index: number) => {
      setCurrentIndex(index);
      onLocationClick?.(places[index]);
    };

    return (
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-t-none rounded-b-lg p-2 sm:p-3 mt-0 max-w-full overflow-hidden">
        {/* Header avec navigation et toggle */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <h2 className="text-xs sm:text-sm font-semibold text-black dark:text-white">
              Navigation ({currentIndex + 1}/{places.length})
            </h2>

            {/* Contrôles de navigation compacts */}
            <div className="flex items-center gap-1">
              <button
                type="button"
                onClick={goUp}
                disabled={currentIndex === 0}
                className="p-1 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                aria-label="Location précédente"
              >
                <ChevronUp className="w-3 h-3 text-gray-700 dark:text-gray-200" />
              </button>

              <button
                type="button"
                onClick={goDown}
                disabled={currentIndex === places.length - 1}
                className="p-1 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                aria-label="Location suivante"
              >
                <ChevronDown className="w-3 h-3 text-gray-700 dark:text-gray-200" />
              </button>
            </div>
          </div>

          {onToggleMode && (
            <button
              type="button"
              onClick={() => onToggleMode(false)}
              className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors flex-shrink-0"
              title="Passer au mode liste"
            >
              <ToggleRight className="w-3 h-3 sm:w-4 sm:h-4 text-blue-500" />
              <span className="hidden xs:inline">Liste</span>
            </button>
          )}
        </div>

        {/* PlaceCard pour la location courante */}
        <PlaceCard
          place={adaptedPlace}
          onClick={() => onLocationClick?.(currentPlace)}
          isSelected={true}
          variant="overlay"
          showHours={true}
          className="mb-3"
        />

        {/* Navigation par points */}
        <div className="flex gap-1 justify-center flex-wrap">
          {places.slice(0, 8).map((place, idx) => (
            <button
              key={`nav-dot-${place.cid}`}
              type="button"
              onClick={() => goToIndex(idx)}
              className={`w-2 h-2 rounded-full transition-all duration-200 ${
                idx === currentIndex
                  ? 'bg-blue-500 scale-125'
                  : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
              }`}
              aria-label={`Aller à ${place.title}`}
              title={place.title}
            />
          ))}
          {/* Show remaining dots on larger screens */}
          <div className="hidden xs:flex gap-1 flex-wrap">
            {places.slice(8).map((place, idx) => (
              <button
                key={`nav-dot-${place.cid}`}
                type="button"
                onClick={() => goToIndex(idx + 8)}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${
                  idx + 8 === currentIndex
                    ? 'bg-blue-500 scale-125'
                    : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                }`}
                aria-label={`Aller à ${place.title}`}
                title={place.title}
              />
            ))}
          </div>
          {places.length > 8 && (
            <span className="text-xs text-gray-500 dark:text-gray-400 ml-1 xs:hidden">
              +{places.length - 8}
            </span>
          )}
        </div>
      </div>
    );
  }

  // Mode liste avec carousel horizontal
  return (
    <div
      className="bg-white dark:bg-gray-800 shadow-lg rounded-t-none rounded-b-lg p-2 sm:p-3 lg:p-4 mt-0 w-full"
      style={{
        maxWidth: '100%',
        contain: 'layout style',
        isolation: 'isolate',
        overflow: 'hidden',
      }}
    >
      {/* Header avec toggle */}
      <div className="flex items-center justify-between mb-3 sm:mb-4">
        <h2 className="text-xs sm:text-sm font-semibold text-black dark:text-white truncate">
          Location Details ({places.length})
        </h2>
        <div className="flex items-center gap-1 sm:gap-2 lg:gap-3 flex-shrink-0">
          {places.length > 1 && onToggleMode && (
            <button
              type="button"
              onClick={() => onToggleMode(true)}
              className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              title="Passer au mode ascenseur"
            >
              <ToggleLeft className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
            </button>
          )}
        </div>
      </div>

      {/* Carousel horizontal des locations */}
      <div className="w-full">
        <PlaceCardsCarousel
          places={places}
          selectedPlaceId={selectedPlaceId}
          onLocationClick={onLocationClick}
          showHours={true}
        />
      </div>
    </div>
  );
};

export default EnhancedLocationDisplay;
