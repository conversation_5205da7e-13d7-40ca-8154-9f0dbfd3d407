'use client';

import React from 'react';

interface WeatherColorScaleProps {
  layer: string;
}

const WeatherColorScale: React.FC<WeatherColorScaleProps> = ({ layer }) => {
  const getScaleData = (layerType: string) => {
    switch (layerType) {
      case 'precipitation_new':
        return {
          title: 'Précipitations, mm/h',
          values: [
            '0',
            '0.5',
            '1',
            '2',
            '4',
            '6',
            '7',
            '10',
            '12',
            '14',
            '16',
            '24',
            '32',
            '50',
          ],
          colors: [
            '#ffffff00', // transparent
            '#c6e48b', // light green
            '#7bc96f', // green
            '#239a3b', // dark green
            '#196127', // darker green
            '#0052cc', // blue
            '#0066ff', // bright blue
            '#0080ff', // light blue
            '#00aaff', // cyan
            '#00ccff', // light cyan
            '#ffaa00', // orange
            '#ff8800', // dark orange
            '#ff4400', // red-orange
            '#ff0000', // red
          ],
        };

      case 'pressure_new':
        return {
          title: 'Pression, hPa',
          values: [
            '950',
            '960',
            '980',
            '1000',
            '1010',
            '1020',
            '1030',
            '1040',
            '1070',
          ],
          colors: [
            '#8b0000', // dark red
            '#ff4500', // orange red
            '#ffa500', // orange
            '#ffff00', // yellow
            '#90ee90', // light green
            '#00ff00', // green
            '#00ffff', // cyan
            '#0080ff', // blue
            '#0000ff', // dark blue
          ],
        };

      case 'clouds_new':
        return {
          title: 'Nuages, %',
          values: [
            '0',
            '10',
            '20',
            '30',
            '40',
            '50',
            '60',
            '70',
            '80',
            '90',
            '100',
          ],
          colors: [
            '#ffffff00', // transparent
            '#f0f0f0', // very light gray
            '#e0e0e0', // light gray
            '#d0d0d0', // gray
            '#c0c0c0', // medium gray
            '#b0b0b0', // darker gray
            '#a0a0a0', // dark gray
            '#909090', // darker gray
            '#808080', // gray
            '#606060', // dark gray
            '#404040', // very dark gray
          ],
        };

      case 'temp_new':
        return {
          title: 'Température, °C',
          values: [
            '-40',
            '-30',
            '-20',
            '-10',
            '0',
            '10',
            '20',
            '30',
            '40',
            '50',
          ],
          colors: [
            '#800080', // purple
            '#0000ff', // blue
            '#0080ff', // light blue
            '#00ffff', // cyan
            '#00ff00', // green
            '#ffff00', // yellow
            '#ffa500', // orange
            '#ff4500', // red-orange
            '#ff0000', // red
            '#8b0000', // dark red
          ],
        };

      case 'wind_new':
        return {
          title: 'Vitesse du vent, m/s',
          values: ['0', '2', '4', '6', '8', '10', '12', '14', '16', '18', '20'],
          colors: [
            '#ffffff00', // transparent
            '#e6f3ff', // very light blue
            '#cce7ff', // light blue
            '#99d6ff', // blue
            '#66c2ff', // medium blue
            '#33adff', // darker blue
            '#0099ff', // blue
            '#0080cc', // dark blue
            '#006699', // darker blue
            '#004d66', // very dark blue
            '#003333', // darkest blue
          ],
        };

      default:
        return null;
    }
  };

  const scaleData = getScaleData(layer);

  if (!scaleData) {
    return null;
  }

  return (
    <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border border-gray-300 dark:border-gray-600 rounded-md p-3 pb-4 shadow-lg w-auto max-w-full">
      <div className="text-xs font-medium text-gray-800 dark:text-gray-200 mb-1.5 text-left">
        {scaleData.title}
      </div>

      {/* Color gradient bar with values */}
      <div className="relative">
        <div className="flex h-5 rounded-sm overflow-hidden border border-gray-400 dark:border-gray-500 mb-1">
          {scaleData.colors.map((color, index) => (
            <div
              key={color}
              className="flex-1 min-w-[12px]"
              style={{ backgroundColor: color }}
              title={`${scaleData.values[index]} ${scaleData.title.split(', ')[1]}`}
            />
          ))}
        </div>

        {/* Value labels positioned above the gradient */}
        <div className="flex justify-between mt-1 px-0.5 space-x-0.5">
          {scaleData.values.map((value, index) => {
            // Show only key values to avoid overcrowding
            const showValue =
              index === 0 ||
              index === Math.floor(scaleData.values.length / 3) ||
              index === Math.floor((scaleData.values.length * 2) / 3) ||
              index === scaleData.values.length - 1;

            return showValue ? (
              <span
                key={value}
                className="text-[10px] font-medium text-gray-900 dark:text-gray-100 font-sans whitespace-nowrap bg-white/70 dark:bg-black/50 px-0.5 rounded"
                style={{
                  position: 'absolute',
                  top: 'calc(100% + 1px)',
                  left: `${(index / (scaleData.values.length - 1)) * 100}%`,
                  transform: 'translateX(-50%)',
                }}
              >
                {value}
              </span>
            ) : null;
          })}
        </div>
      </div>
    </div>
  );
};

export default WeatherColorScale;
