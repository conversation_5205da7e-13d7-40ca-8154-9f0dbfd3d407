'use client';

import React, { useEffect, useRef, memo } from 'react';

interface StockFinancialsProps {
  symbol: string;
  className?: string;
}

export function StockFinancials({
  symbol,
  className = '',
}: StockFinancialsProps) {
  const container = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!container.current) return;

    const currentContainer = container.current;

    // Clear previous script if it exists
    currentContainer.innerHTML = '';

    const script = document.createElement('script');
    script.src =
      'https://s3.tradingview.com/external-embedding/embed-widget-financials.js';
    script.async = true;
    script.innerHTML = JSON.stringify({
      isTransparent: true,
      largeChartUrl: '',
      displayMode: 'regular',
      width: '100%',
      height: '100%',
      colorTheme: 'light',
      symbol: symbol,
      locale: 'en', // Changed from "fr" to "en" for English locale
    });

    currentContainer.appendChild(script);

    return () => {
      if (currentContainer) {
        currentContainer.innerHTML = '';
      }
    };
  }, [symbol]);

  return (
    <div className={`h-[500px] w-full ${className}`}>
      <div className="tradingview-widget-container h-full" ref={container}>
        <div className="tradingview-widget-container__widget" />
        <div className="tradingview-widget-copyright">
          <a
            href="https://www.tradingview.com/"
            rel="noopener nofollow"
            target="_blank"
            className="text-xs text-muted-foreground hover:underline"
          >
            Track all markets on TradingView
          </a>
        </div>
      </div>
    </div>
  );
}

export default memo(StockFinancials);
