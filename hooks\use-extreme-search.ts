/**
 * Hook React pour gérer l'état de la recherche extrême
 */

import { useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

export interface ExtremeSearchState {
  isExtreme: boolean;
  usageCount: number;
  usageLimit: number;
  isLoading: boolean;
  canUseExtreme: boolean;
}

export interface UseExtremeSearchOptions {
  userId?: string;
  isProUser?: boolean;
  onToggle?: (isExtreme: boolean) => void;
  usageLimit?: number;
}

export interface UseExtremeSearchReturn extends ExtremeSearchState {
  toggleExtreme: () => void;
  setIsExtreme: (value: boolean) => void;
  refreshUsage: () => Promise<void>;
  checkCanUse: () => boolean;
}

// ============================================================================
// CONSTANTES
// ============================================================================

const DEFAULT_USAGE_LIMIT = 10;
const STORAGE_KEY = 'extreme-search-mode';

// ============================================================================
// HOOK PRINCIPAL
// ============================================================================

export function useExtremeSearch(
  options: UseExtremeSearchOptions = {},
): UseExtremeSearchReturn {
  const {
    userId,
    isProUser = false,
    onToggle,
    usageLimit = DEFAULT_USAGE_LIMIT,
  } = options;

  const [isExtreme, setIsExtremeState] = useState<boolean>(false);
  const [usageCount, setUsageCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const effectiveLimit = isProUser ? Number.POSITIVE_INFINITY : usageLimit;
  const canUseExtreme = usageCount < effectiveLimit;

  const refreshUsage = useCallback(async () => {
    if (!userId) {
      setUsageCount(0);
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/extreme-search/usage?userId=${userId}`,
      );

      if (!response.ok) {
        throw new Error("Échec de récupération de l'utilisation");
      }

      const data = await response.json();
      setUsageCount(data.count || 0);
    } catch (error) {
      console.error("Erreur lors de la récupération de l'utilisation:", error);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  const checkCanUse = useCallback((): boolean => {
    if (isProUser) return true;

    if (usageCount >= effectiveLimit) {
      toast.error('Limite atteinte', {
        description: `Vous avez atteint la limite de ${effectiveLimit} recherches extrêmes ce mois-ci.`,
      });
      return false;
    }

    return true;
  }, [isProUser, usageCount, effectiveLimit]);

  const setIsExtreme = useCallback(
    (value: boolean) => {
      if (value && !checkCanUse()) {
        return;
      }

      setIsExtremeState(value);

      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(value));
      } catch (error) {
        console.error('Erreur lors de la sauvegarde du mode extreme:', error);
      }

      if (onToggle) {
        onToggle(value);
      }
    },
    [checkCanUse, onToggle],
  );

  const toggleExtreme = useCallback(() => {
    setIsExtreme(!isExtreme);
  }, [isExtreme, setIsExtreme]);

  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const value = JSON.parse(stored);
        setIsExtremeState(value);
      }
    } catch (error) {
      console.error('Erreur lors du chargement du mode extreme:', error);
    }
  }, []);

  useEffect(() => {
    if (userId) {
      refreshUsage();
    }
  }, [userId, refreshUsage]);

  useEffect(() => {
    if (isExtreme && !canUseExtreme) {
      setIsExtremeState(false);
      toast.warning('Mode Extreme désactivé', {
        description: 'Vous avez atteint votre limite mensuelle.',
      });
    }
  }, [isExtreme, canUseExtreme]);

  return {
    isExtreme,
    usageCount,
    usageLimit: effectiveLimit,
    isLoading,
    canUseExtreme,
    toggleExtreme,
    setIsExtreme,
    refreshUsage,
    checkCanUse,
  };
}

// ============================================================================
// HOOK POUR LE TRACKING
// ============================================================================

export function useTrackExtremeSearch() {
  const trackUsage = useCallback(async (userId: string) => {
    try {
      const response = await fetch('/api/extreme-search/track', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        throw new Error('Échec du tracking');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Erreur lors du tracking de l'utilisation:", error);
      throw error;
    }
  }, []);

  return { trackUsage };
}

// ============================================================================
// HOOK POUR LES STATISTIQUES
// ============================================================================

export interface ExtremeSearchStats {
  currentMonth: number;
  lastMonth: number;
  total: number;
  remainingThisMonth: number;
}

export function useExtremeSearchStats(userId?: string) {
  const [stats, setStats] = useState<ExtremeSearchStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchStats = useCallback(async () => {
    if (!userId) {
      setStats(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/extreme-search/stats?userId=${userId}`,
      );

      if (!response.ok) {
        throw new Error('Échec de récupération des statistiques');
      }

      const data = await response.json();
      setStats(data);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Erreur inconnue');
      setError(error);
      console.error('Erreur lors de la récupération des stats:', error);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    isLoading,
    error,
    refetch: fetchStats,
  };
}

export default useExtremeSearch;
