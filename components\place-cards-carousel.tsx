'use client';

import React, { useState, useRef, useEffect, useCallback, memo } from 'react';
import { cn } from '@/lib/utils';
import PlaceCard from './place-card';
import { enrichPlaceWithContextualData } from '@/lib/places/contextual-reviews';
import type { Place } from '@/lib/types';
import './chat/carousel-responsive-fix.css';

interface PlaceCardsCarouselProps {
  places: Place[];
  selectedPlaceId?: string | null;
  onLocationClick?: (place: Place) => void;
  showHours?: boolean;
}

// Adaptateur pour convertir notre type Place vers le type PlaceCard
const adaptPlaceForCard = (place: Place) => {
  // Générer des horaires d'exemple si pas disponibles
  const generateSampleHours = () => {
    const days = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ];
    return days.map((day) => {
      if (day === 'Sunday') {
        return `${day}: 10:00 AM – 6:00 PM`;
      } else if (day === 'Saturday') {
        return `${day}: 9:00 AM – 8:00 PM`;
      } else {
        return `${day}: 8:00 AM – 9:00 PM`;
      }
    });
  };

  return {
    name: place.title,
    location: {
      lat: place.latitude,
      lng: place.longitude,
    },
    place_id: place.cid,
    vicinity: place.address,
    rating: place.rating,
    photos: place.images?.map((img) => ({
      photo_reference: img.url,
      width: 400,
      height: 300,
      url: img.url,
      caption: img.description,
    })),
    phone: place.phoneNumber,
    website: place.website,
    type: place.category,
    reviews_count: undefined,
    reviews: undefined,
    price_level: place.category?.toLowerCase().includes('restaurant')
      ? Math.floor(Math.random() * 3) + 1
      : undefined,
    description: undefined,
    is_closed: undefined,
    is_open: Math.random() > 0.3, // 70% de chance d'être ouvert
    next_open_close: undefined,
    cuisine: place.category?.toLowerCase().includes('restaurant')
      ? place.category
      : undefined,
    source: 'Serper',
    hours: generateSampleHours(),
    opening_hours: generateSampleHours(),
    distance: undefined,
    bearing: undefined,
    timezone: 'Europe/Paris', // Timezone par défaut pour la France
  };
};

const PlaceCardsCarousel = memo<PlaceCardsCarouselProps>(
  ({ places, selectedPlaceId, onLocationClick, showHours = true }) => {
    const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
    const [activeIndex, setActiveIndex] = useState<number | null>(null);
    const listContainerRef = useRef<HTMLDivElement | null>(null);
    const scrollDebounceRef = useRef<number | null>(null);
    const isDraggingRef = useRef<boolean>(false);
    const dragStartXRef = useRef<number>(0);
    const dragStartScrollRef = useRef<number>(0);
    const dragMovedRef = useRef<boolean>(false);

    // Synchroniser l'index avec la sélection externe (clics sur marqueurs)
    useEffect(() => {
      if (selectedPlaceId && places.length > 0) {
        const foundIndex = places.findIndex(
          (place) => place.cid === selectedPlaceId,
        );
        if (foundIndex !== -1 && foundIndex !== selectedIndex) {
          console.log(
            `Carousel: Synchronizing with map selection - index ${foundIndex}`,
          );
          setSelectedIndex(foundIndex);
          setActiveIndex(foundIndex);

          // Scroll automatiquement vers la carte sélectionnée avec un délai
          setTimeout(() => {
            const id = places[foundIndex]?.cid || `idx-${foundIndex}`;
            const el = document.getElementById(`place-card-${id}`);
            if (el && listContainerRef.current) {
              el.scrollIntoView({
                behavior: 'smooth',
                inline: 'center',
                block: 'nearest',
              });
            }
          }, 100);
        }
      }
    }, [selectedPlaceId, places, selectedIndex]);

    const commitIndexForMap = useCallback(
      (nextIndex: number) => {
        if (places.length === 0) return;
        const clamped = Math.max(0, Math.min(places.length - 1, nextIndex));
        const next = places[clamped];

        // Éviter les boucles infinies en vérifiant si l'index a vraiment changé
        if (clamped !== selectedIndex) {
          console.log(`Carousel: Committing index ${clamped} to map`);
          setSelectedIndex(clamped);
          if (onLocationClick) {
            onLocationClick(next);
          }
        }
      },
      [places, onLocationClick, selectedIndex],
    );

    const selectByIndex = useCallback(
      (nextIndex: number) => {
        if (places.length === 0) return;
        const clamped = Math.max(0, Math.min(places.length - 1, nextIndex));
        const next = places[clamped];

        console.log(`Carousel: Selecting index ${clamped} by user interaction`);
        setSelectedIndex(clamped);
        setActiveIndex(clamped);

        if (onLocationClick) {
          onLocationClick(next);
        }
      },
      [places, onLocationClick],
    );

    const handleListScroll = useCallback(() => {
      if (!listContainerRef.current || places.length === 0) return;
      const container = listContainerRef.current;
      const containerCenter = container.scrollLeft + container.clientWidth / 2;
      const children = Array.from(container.children) as HTMLElement[];
      let closestIdx = 0;
      let smallestDist = Number.MAX_SAFE_INTEGER;
      children.forEach((child, i) => {
        const childCenter = child.offsetLeft + child.clientWidth / 2;
        const dist = Math.abs(childCenter - containerCenter);
        if (dist < smallestDist) {
          smallestDist = dist;
          closestIdx = i;
        }
      });
      // Immediate visual highlight during scroll
      setActiveIndex(closestIdx);
      // Debounce commit to allow momentum to settle; lowers jank
      if (scrollDebounceRef.current)
        window.clearTimeout(scrollDebounceRef.current);
      scrollDebounceRef.current = window.setTimeout(
        () => commitIndexForMap(closestIdx),
        90,
      );
    }, [commitIndexForMap, places.length]);

    const handleMouseDown = useCallback(
      (e: React.MouseEvent<HTMLDivElement>) => {
        if (!listContainerRef.current) return;
        e.preventDefault();
        isDraggingRef.current = true;
        dragMovedRef.current = false;
        dragStartXRef.current = e.clientX;
        dragStartScrollRef.current = listContainerRef.current.scrollLeft;

        const handleMouseMove = (ev: MouseEvent) => {
          if (!isDraggingRef.current || !listContainerRef.current) return;
          ev.preventDefault();
          const dx = ev.clientX - dragStartXRef.current;
          if (Math.abs(dx) > 3) dragMovedRef.current = true;
          listContainerRef.current.scrollLeft = dragStartScrollRef.current - dx;
        };

        const handleMouseUp = () => {
          isDraggingRef.current = false;
          document.removeEventListener('mousemove', handleMouseMove);
          document.removeEventListener('mouseup', handleMouseUp);
        };

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
      },
      [],
    );

    const handleTouchStart = useCallback(
      (e: React.TouchEvent<HTMLDivElement>) => {
        if (!listContainerRef.current) return;
        const touch = e.touches[0];
        isDraggingRef.current = true;
        dragMovedRef.current = false;
        dragStartXRef.current = touch.clientX;
        dragStartScrollRef.current = listContainerRef.current.scrollLeft;
      },
      [],
    );

    const handleTouchMove = useCallback(
      (e: React.TouchEvent<HTMLDivElement>) => {
        if (!isDraggingRef.current || !listContainerRef.current) return;
        const touch = e.touches[0];
        const dx = touch.clientX - dragStartXRef.current;
        if (Math.abs(dx) > 3) dragMovedRef.current = true;
        listContainerRef.current.scrollLeft = dragStartScrollRef.current - dx;
      },
      [],
    );

    const handleTouchEnd = useCallback(() => {
      isDraggingRef.current = false;
    }, []);

    const handleClickCapture = useCallback(
      (e: React.MouseEvent<HTMLDivElement>) => {
        if (dragMovedRef.current) {
          e.preventDefault();
          e.stopPropagation();
          dragMovedRef.current = false;
        }
      },
      [],
    );

    // Auto-scroll to selected card (amélioration pour éviter les conflits)
    useEffect(() => {
      if (selectedIndex === null || isDraggingRef.current) return;

      // Délai pour éviter les conflits avec les interactions utilisateur
      const scrollTimeout = setTimeout(() => {
        const id = places[selectedIndex]?.cid || `idx-${selectedIndex}`;
        const el = document.getElementById(`place-card-${id}`);
        if (el && listContainerRef.current && !isDraggingRef.current) {
          console.log(`Carousel: Auto-scrolling to card ${selectedIndex}`);
          el.scrollIntoView({
            behavior: 'smooth',
            inline: 'center',
            block: 'nearest',
          });
        }
      }, 50);

      return () => clearTimeout(scrollTimeout);
    }, [selectedIndex, places]);

    // Ensure there is a default selection
    useEffect(() => {
      if (selectedIndex === null && places.length > 0) {
        setActiveIndex(0);
        commitIndexForMap(0);
      }
    }, [places, selectedIndex, commitIndexForMap]);

    if (places.length === 0) {
      return null;
    }

    return (
      <div className="place-cards-carousel-container relative w-full">
        {/* Right fade overlay to hide peeking next card (doesn't cover scrollbar) */}
        <div className="pointer-events-none absolute top-0 right-0 bottom-6 sm:bottom-7 md:bottom-8 w-8 sm:w-10 md:w-12 bg-gradient-to-l from-white dark:from-gray-800 to-transparent z-10" />
        {/* Horizontal scrolling container */}
        <div
          ref={listContainerRef}
          className={cn(
            'place-cards-carousel-scroll flex gap-3 overflow-x-auto px-2 py-2 scrollbar-thin w-full [scrollbar-width:thin] [-ms-overflow-style:none] overscroll-x-contain cursor-grab active:cursor-grabbing select-none snap-x snap-mandatory [scroll-padding-inline:16px] scroll-smooth',
            'focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-primary/50',
            'text-left', // Ensure text alignment is preserved
          )}
          onScroll={handleListScroll}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onClickCapture={handleClickCapture}
          role="list"
          aria-label="Carrousel de lieux à proximité"
        >
          {places.map((place, index) => {
            const isSel = (activeIndex ?? selectedIndex) === index;
            const id = place.cid || `idx-${index}`;
            const base = adaptPlaceForCard(place);
            const contextualData = enrichPlaceWithContextualData(place);
            const adaptedPlace = {
              ...base,
              ...contextualData,
            };

            return (
              <div
                key={id}
                id={`place-card-${id}`}
                data-pager-index={index}
                className="place-cards-carousel-item basis-full min-w-full shrink-0 snap-center"
                role="listitem"
              >
                <div className="relative max-w-full">
                  <div className="absolute -top-2 -left-2 z-20">
                    <div
                      className={cn(
                        'h-6 w-6 rounded-full flex items-center justify-center text-xs font-semibold shadow-md border',
                        isSel
                          ? 'bg-black text-white dark:bg-white dark:text-black border-neutral-200 dark:border-neutral-800'
                          : 'bg-white text-neutral-900 dark:bg-neutral-900 dark:text-neutral-100 border-neutral-200 dark:border-neutral-800',
                      )}
                      aria-hidden="true"
                    >
                      {index + 1}
                    </div>
                  </div>
                  <PlaceCard
                    place={adaptedPlace}
                    onClick={() => selectByIndex(index)}
                    isSelected={isSel}
                    variant="overlay"
                    showHours={showHours}
                    className="place-card min-h-[240px] max-w-full"
                  />
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  },
);

PlaceCardsCarousel.displayName = 'PlaceCardsCarousel';

export default PlaceCardsCarousel;
