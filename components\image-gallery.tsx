'use client';

import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import Image from 'next/image';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface ImageType {
  url: string;
  description?: string;
  searchQuery?: string;
}

interface ImageGalleryProps {
  images: (ImageType | string)[];
  query?: string;
  maxPreviewImages?: number;
  initiallyExpanded?: boolean;
}

export function ImageGallery({
  images,
  query,
  maxPreviewImages = 5, // 1 grande + 4 petites
  initiallyExpanded = true,
}: ImageGalleryProps) {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [imageStatuses, setImageStatuses] = useState<
    Record<string, 'loading' | 'loaded' | 'error'>
  >({});
  const [showAllSources, setShowAllSources] = useState(false);
  const [filteredImages, setFilteredImages] = useState<ImageType[]>([]);
  const [imagesLoaded, setImagesLoaded] = useState(false);
  const [isExpanded, setIsExpanded] = useState(initiallyExpanded);

  // Convertir toutes les images en format uniforme
  const normalizedImages: ImageType[] = useMemo(
    () => images.map((img) => (typeof img === 'string' ? { url: img } : img)),
    [images],
  );

  const imageUrlsRef = useRef<string[]>([]);

  // Fonction pour générer une clé unique à partir d'une URL d'image
  const generateImageKey = (url: string | undefined, index: number) => {
    if (!url) {
      return `img-undefined-${index}`;
    }

    try {
      const urlParts = url.split('/');
      const fileName = urlParts[urlParts.length - 1].split('?')[0];
      return `img-${fileName.replace(/[^a-zA-Z0-9]/g, '')}-${index}`;
    } catch (error) {
      console.error("Erreur lors de la génération de la clé d'image:", error);
      return `img-fallback-${index}`;
    }
  };

  const currentImageUrls = useMemo(
    () => normalizedImages.map((img) => img.url),
    [normalizedImages],
  );

  // Fonction pour obtenir l'URL correcte d'une image
  const getImageSrc = useCallback((imageUrl: string) => {
    if (!imageUrl) return '';

    // Si l'URL contient déjà le proxy, la retourner telle quelle
    if (imageUrl.includes('/api/proxy-image')) {
      return imageUrl;
    }

    // Sinon, retourner l'URL directe
    return imageUrl;
  }, []);

  const handleImageLoad = useCallback((imageUrl: string, index: number) => {
    const key = generateImageKey(imageUrl, index);
    setImageStatuses((prev) => {
      if (prev[key] === 'loaded') return prev;
      return { ...prev, [key]: 'loaded' };
    });
  }, []);

  const handleImageError = useCallback((imageUrl: string, index: number) => {
    const key = generateImageKey(imageUrl, index);
    console.warn(`Image failed to load: ${imageUrl}`);
    setImageStatuses((prev) => {
      if (prev[key] === 'error') return prev;
      return { ...prev, [key]: 'error' };
    });
  }, []);

  // Précharger les images
  useEffect(() => {
    const previousImageUrls = imageUrlsRef.current;

    if (
      previousImageUrls.length === currentImageUrls.length &&
      previousImageUrls.every(
        (url: string, i: number) => url === currentImageUrls[i],
      )
    ) {
      return;
    }

    imageUrlsRef.current = currentImageUrls;

    const initialStatuses: Record<string, 'loading' | 'loaded' | 'error'> = {};

    normalizedImages.forEach((image, index) => {
      const key = generateImageKey(image.url, index);
      initialStatuses[key] = 'loading';
    });

    setImageStatuses(initialStatuses);
    setFilteredImages(normalizedImages);
    setImagesLoaded(false);

    const successfullyLoadedImages: ImageType[] = [];
    let loadedCount = 0;
    const totalImages = normalizedImages.length;

    // Fonction améliorée pour précharger une image
    const preloadImage = (image: ImageType, index: number): void => {
      const url = image.url;
      const key = generateImageKey(url, index);
      const img = new window.Image();

      const markAsLoaded = (loadedUrl: string) => {
        setImageStatuses((prev) => ({
          ...prev,
          [key]: 'loaded',
        }));

        successfullyLoadedImages.push({
          ...image,
          url: loadedUrl,
        });

        loadedCount++;
        if (loadedCount === totalImages) {
          if (successfullyLoadedImages.length > 0) {
            setFilteredImages(successfullyLoadedImages);
          }
          setImagesLoaded(true);
        }
      };

      const markAsError = () => {
        setImageStatuses((prev) => ({
          ...prev,
          [key]: 'error',
        }));

        loadedCount++;
        if (loadedCount === totalImages) {
          if (successfullyLoadedImages.length > 0) {
            setFilteredImages(successfullyLoadedImages);
          }
          setImagesLoaded(true);
        }
      };

      img.onload = (): void => {
        markAsLoaded(url);
      };

      img.onerror = (): void => {
        // Essayer avec le proxy si l'image ne se charge pas
        if (!url.includes('/api/proxy-image')) {
          let cleanUrl = url;

          // Décoder l'URL si elle est encodée
          try {
            if (cleanUrl.includes('%')) {
              cleanUrl = decodeURIComponent(cleanUrl);
            }
          } catch (e) {
            // Échec silencieux du décodage
            cleanUrl = url;
          }

          const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(cleanUrl)}`;
          const proxyImg = new window.Image();

          proxyImg.onload = (): void => {
            markAsLoaded(proxyUrl);
          };

          proxyImg.onerror = (): void => {
            // Échec silencieux - ne pas polluer la console
            markAsError();
          };

          proxyImg.src = proxyUrl;
        } else {
          // Échec silencieux pour les images proxy
          markAsError();
        }
      };

      img.src = url;
    };

    // Précharger toutes les images
    normalizedImages.forEach((image, index) => {
      preloadImage(image, index);
    });
  }, [currentImageUrls, normalizedImages]);

  if (!images || images.length === 0) {
    return <div className="text-muted-foreground">Aucune image trouvée</div>;
  }

  if (imagesLoaded && filteredImages.length === 0) {
    return (
      <div className="text-muted-foreground">
        Aucune image n&apos;a pu être chargée
      </div>
    );
  }

  const generateSources = () => {
    const searchTerm = query || 'San Diego';

    return [
      {
        name: `${searchTerm} Photos | Photos by Ron Niebrugge`,
        url: `https://wildnatureimages.com/${encodeURIComponent(searchTerm.toLowerCase())}`,
        icon: 'W',
      },
      {
        name: `${searchTerm} Pictures, Images and Stock Photos`,
        url: `https://istockphoto.com/search?q=${encodeURIComponent(searchTerm)}`,
        icon: 'iS',
      },
      {
        name: `Historical Photo Gallery | City of ${searchTerm} Official Website`,
        url: `https://sandiego.gov/photos?q=${encodeURIComponent(searchTerm)}`,
        icon: '🔥',
      },
      {
        name: `${searchTerm} Travel Images | Unsplash`,
        url: `https://unsplash.com/s/photos/${encodeURIComponent(searchTerm.toLowerCase())}`,
        icon: 'U',
      },
      {
        name: `${searchTerm} Photography | Flickr`,
        url: `https://www.flickr.com/search/?text=${encodeURIComponent(searchTerm)}`,
        icon: 'F',
      },
      {
        name: `${searchTerm} Stock Photos | Shutterstock`,
        url: `https://www.shutterstock.com/search/${encodeURIComponent(searchTerm)}`,
        icon: 'S',
      },
      {
        name: `${searchTerm} Images | Getty Images`,
        url: `https://www.gettyimages.com/photos/${encodeURIComponent(searchTerm.toLowerCase())}`,
        icon: 'G',
      },
      {
        name: `${searchTerm} Free Photos | Pexels`,
        url: `https://www.pexels.com/search/${encodeURIComponent(searchTerm.toLowerCase())}`,
        icon: 'P',
      },
      {
        name: `${searchTerm} Images | Adobe Stock`,
        url: `https://stock.adobe.com/search?k=${encodeURIComponent(searchTerm)}`,
        icon: 'A',
      },
      {
        name: `${searchTerm} Free Images | Pixabay`,
        url: `https://pixabay.com/images/search/${encodeURIComponent(searchTerm.toLowerCase())}`,
        icon: 'Px',
      },
    ];
  };

  const sources = generateSources();

  return (
    <div className="rounded-lg bg-gray-950 text-white p-2 sm:p-4 mb-4 w-full max-w-full overflow-hidden">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-2 gap-1 sm:gap-0">
        <div className="flex items-center">
          <svg
            className="size-3 mr-1 shrink-0"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M21 21L16.65 16.65"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="text-xs sm:text-sm">Images</span>
        </div>
        <div className="flex items-center">
          <svg
            className="size-3 mr-1 text-green-500 shrink-0"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M20 6L9 17L4 12"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="text-xs">
            {imagesLoaded ? filteredImages.length : normalizedImages.length}{' '}
            résultats
          </span>
          <button
            type="button"
            onClick={() => setIsExpanded(!isExpanded)}
            className="ml-1 p-0 bg-transparent border-0 cursor-pointer flex items-center justify-center"
            aria-label={
              isExpanded ? 'Rétracter les images' : 'Déployer les images'
            }
          >
            <svg
              className={`size-3 shrink-0 transition-transform duration-200 ${isExpanded ? '' : 'rotate-180'}`}
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 9L12 15L18 9"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Layout Hybride: 1 grande image + grille de petites images */}
      {isExpanded && (
        <div className="flex gap-2 mb-2 h-[160px]">
          {/* Grande image à gauche */}
          {filteredImages[0] && (
            <button
              key={generateImageKey(filteredImages[0].url, 0)}
              type="button"
              className="flex-shrink-0 w-[340px] h-full cursor-pointer relative p-0 border-0 bg-transparent text-left rounded-lg overflow-hidden"
              onClick={() => {
                setSelectedIndex(0);
                setDialogOpen(true);
              }}
              aria-label="Voir l'image 1"
            >
              {/* Placeholder de chargement */}
              {filteredImages[0].url &&
                imageStatuses[generateImageKey(filteredImages[0].url, 0)] ===
                  'loading' && (
                  <div className="size-full flex items-center justify-center bg-gray-800">
                    <div className="animate-pulse size-6 rounded-full bg-gray-700" />
                  </div>
                )}

              {/* Image */}
              <Image
                src={getImageSrc(filteredImages[0].url)}
                alt={filteredImages[0].description || 'Image 1'}
                fill
                className={`object-cover transition-opacity duration-300 ${
                  filteredImages[0].url &&
                  imageStatuses[generateImageKey(filteredImages[0].url, 0)] ===
                    'loading'
                    ? 'opacity-0'
                    : 'opacity-100'
                }`}
                onLoad={() => {
                  if (filteredImages[0].url) {
                    handleImageLoad(filteredImages[0].url, 0);
                  }
                }}
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  const currentSrc = target.src;

                  if (
                    filteredImages[0].url &&
                    !currentSrc.includes('/api/proxy-image')
                  ) {
                    let cleanUrl = filteredImages[0].url;
                    try {
                      if (cleanUrl.includes('%')) {
                        cleanUrl = decodeURIComponent(cleanUrl);
                      }
                    } catch (e) {
                      cleanUrl = filteredImages[0].url;
                    }

                    const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(cleanUrl)}`;
                    target.src = proxyUrl;
                  } else {
                    if (filteredImages[0].url) {
                      handleImageError(filteredImages[0].url, 0);
                    }
                  }
                }}
                sizes="340px"
              />
            </button>
          )}

          {/* Grille de petites images à droite (2×2) */}
          <div className="grid grid-cols-2 grid-rows-2 gap-2 flex-1 h-full">
            {filteredImages.slice(1, 5).map((image, idx) => {
              const index = idx + 1; // Ajuster l'index car on commence à 1
              const isLast = idx === 3; // Dernière des 4 petites images (index 3 dans le slice)
              const hasMore = filteredImages.length > 5; // Plus de 5 images au total (1 grande + 4 petites)

              return (
                <button
                  key={generateImageKey(image.url, index)}
                  type="button"
                  className="w-full h-full cursor-pointer relative p-0 border-0 bg-transparent text-left rounded-lg overflow-hidden"
                  onClick={() => {
                    setSelectedIndex(index);
                    setDialogOpen(true);
                  }}
                  aria-label={`Voir l'image ${index + 1}`}
                >
                  {/* Placeholder de chargement */}
                  {image.url &&
                    imageStatuses[generateImageKey(image.url, index)] ===
                      'loading' && (
                      <div className="size-full flex items-center justify-center bg-gray-800">
                        <div className="animate-pulse size-6 rounded-full bg-gray-700" />
                      </div>
                    )}

                  {/* Image avec gestion d'erreur améliorée */}
                  <Image
                    src={getImageSrc(image.url)}
                    alt={image.description || `Contenu ${index + 1}`}
                    fill
                    className={`object-cover transition-opacity duration-300 ${
                      image.url &&
                      imageStatuses[generateImageKey(image.url, index)] ===
                        'loading'
                        ? 'opacity-0'
                        : 'opacity-100'
                    }`}
                    onLoad={() => {
                      if (image.url) {
                        handleImageLoad(image.url, index);
                      }
                    }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      const currentSrc = target.src;

                      // Si l'image n'utilise pas encore le proxy, essayer avec le proxy
                      if (
                        image.url &&
                        !currentSrc.includes('/api/proxy-image')
                      ) {
                        let cleanUrl = image.url;
                        try {
                          if (cleanUrl.includes('%')) {
                            cleanUrl = decodeURIComponent(cleanUrl);
                          }
                        } catch (e) {
                          cleanUrl = image.url;
                        }

                        const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(cleanUrl)}`;
                        target.src = proxyUrl;
                      } else {
                        // L'image a échoué même avec le proxy - échec silencieux
                        if (image.url) {
                          handleImageError(image.url, index);
                        }
                      }
                    }}
                  />

                  {/* Overlay "+X more" sur la dernière image */}
                  {isLast && hasMore && (
                    <div className="absolute inset-0 bg-black/60 flex items-center justify-center text-white">
                      <div className="flex flex-col items-center">
                        <div className="text-sm font-medium">
                          +{filteredImages.length - 5} more
                        </div>
                      </div>
                    </div>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Dialog modal */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="w-[calc(100vw-2rem)] sm:max-w-3xl max-h-[90vh] overflow-hidden bg-gray-900 text-white border-gray-700 p-2 sm:p-4">
          <DialogHeader className="py-1 px-0">
            <DialogTitle className="text-sm sm:text-base">
              {query || 'Images'}
            </DialogTitle>
            {query && (
              <DialogDescription className="text-xs text-gray-300">
                {query}
              </DialogDescription>
            )}
          </DialogHeader>
          <div className="py-1 flex flex-col h-full overflow-hidden">
            <div className="relative grow">
              {/* Image principale */}
              <div className="flex items-center justify-center h-full max-h-[50vh] bg-gray-800 overflow-hidden relative">
                {filteredImages[selectedIndex] &&
                  imageStatuses[
                    generateImageKey(
                      filteredImages[selectedIndex]?.url,
                      selectedIndex,
                    )
                  ] === 'loading' && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="animate-pulse size-8 rounded-full bg-gray-700" />
                    </div>
                  )}

                <Image
                  src={getImageSrc(filteredImages[selectedIndex]?.url || '')}
                  alt={
                    filteredImages[selectedIndex]?.description ||
                    `Contenu ${selectedIndex + 1}`
                  }
                  width={800}
                  height={600}
                  className="max-h-[50vh] max-w-full object-contain"
                  style={{ maxWidth: 'min(calc(100vw - 4rem), 100%)' }}
                />
              </div>

              {/* Boutons de navigation */}
              <div className="absolute inset-0 flex items-center justify-between p-1 sm:p-2 z-10">
                <button
                  type="button"
                  onClick={() =>
                    setSelectedIndex((prev) =>
                      prev > 0 ? prev - 1 : filteredImages.length - 1,
                    )
                  }
                  className="size-8 sm:size-10 rounded-full shadow focus:outline-none bg-black/70 hover:bg-black/90 text-white border-0 flex items-center justify-center"
                  aria-label="Image précédente"
                >
                  &#10094;
                </button>
                <button
                  type="button"
                  onClick={() =>
                    setSelectedIndex((prev) =>
                      prev < filteredImages.length - 1 ? prev + 1 : 0,
                    )
                  }
                  className="size-8 sm:size-10 rounded-full shadow focus:outline-none bg-black/70 hover:bg-black/90 text-white border-0 flex items-center justify-center"
                  aria-label="Image suivante"
                >
                  &#10095;
                </button>
              </div>
            </div>

            {/* Compteur */}
            <div className="py-1 text-center text-xs text-gray-300">
              {selectedIndex + 1} sur {filteredImages.length}
            </div>

            {/* Description */}
            {filteredImages[selectedIndex]?.description && (
              <div className="mt-1 p-1 sm:p-2 text-xs bg-gray-800 rounded-md max-w-full overflow-hidden text-ellipsis line-clamp-2 sm:line-clamp-none">
                {filteredImages[selectedIndex].description}
              </div>
            )}

            {/* Miniatures */}
            <div className="mt-1 flex flex-wrap justify-center gap-1 pb-1 max-w-full overflow-hidden">
              {filteredImages.map((img, idx) => (
                <button
                  type="button"
                  key={generateImageKey(img.url, idx)}
                  onClick={() => setSelectedIndex(idx)}
                  className={`relative shrink-0 w-10 h-8 sm:w-14 sm:h-10 overflow-hidden rounded-md border-2 ${
                    selectedIndex === idx
                      ? 'border-blue-500'
                      : 'border-transparent'
                  }`}
                >
                  <Image
                    src={getImageSrc(img.url)}
                    alt={`Miniature ${idx + 1}`}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
