/**
 * Composant Bouton pour activer/désactiver la recherche extrême
 */

'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useExtremeSearch } from '@/hooks/use-extreme-search';
import { Microscope, Crown } from 'lucide-react';

// ============================================================================
// TYPES
// ============================================================================

export interface ExtremeSearchButtonProps {
  userId?: string;
  isProUser?: boolean;
  onToggle?: (isExtreme: boolean) => void;
  className?: string;
  showBadge?: boolean;
  showUsageCount?: boolean;
  variant?: 'default' | 'compact' | 'icon-only';
}

// ============================================================================
// COMPOSANT PRINCIPAL
// ============================================================================

export function ExtremeSearchButton({
  userId,
  isProUser = false,
  onToggle,
  className,
  showBadge = true,
  showUsageCount = true,
  variant = 'default',
}: ExtremeSearchButtonProps) {
  const {
    isExtreme,
    toggleExtreme,
    usageCount,
    usageLimit,
    canUseExtreme,
    isLoading,
  } = useExtremeSearch({
    userId,
    isProUser,
    onToggle,
  });

  const remainingSearches = isProUser
    ? '∞'
    : Math.max(0, usageLimit - usageCount);
  const isLimitReached = !isProUser && usageCount >= usageLimit;

  const renderTooltipContent = () => {
    if (isLimitReached) {
      return (
        <div className="space-y-1">
          <p className="font-semibold">Limite atteinte</p>
          <p className="text-xs">
            Vous avez utilisé {usageCount}/{usageLimit} recherches ce mois-ci.
          </p>
        </div>
      );
    }

    if (isExtreme) {
      return (
        <div className="space-y-1">
          <p className="font-semibold">Mode Recherche Extrême activé</p>
          <p className="text-xs">
            Recherche approfondie multi-étapes avec planification IA
          </p>
          {showUsageCount && !isProUser && (
            <p className="text-xs text-muted-foreground">
              {remainingSearches} recherches restantes ce mois-ci
            </p>
          )}
        </div>
      );
    }

    return (
      <div className="space-y-1">
        <p className="font-semibold">Activer la Recherche Extrême</p>
        <p className="text-xs">
          Recherche avancée avec analyse approfondie et sources multiples
        </p>
        {showUsageCount && !isProUser && (
          <p className="text-xs text-muted-foreground">
            {remainingSearches} recherches disponibles ce mois-ci
          </p>
        )}
      </div>
    );
  };

  const renderButtonContent = () => {
    if (variant === 'icon-only') {
      return <Microscope className="h-4 w-4" />;
    }

    if (variant === 'compact') {
      return (
        <div className="flex items-center gap-1.5">
          <Microscope className="h-4 w-4" />
          {showBadge && isProUser && (
            <Crown className="h-3 w-3 text-yellow-500" />
          )}
        </div>
      );
    }

    return (
      <div className="flex items-center gap-2">
        <Microscope className="h-4 w-4" />
        <span className="text-sm font-medium">
          {isExtreme ? 'Mode Extreme' : 'Recherche Extrême'}
        </span>
        {showBadge && isProUser && (
          <Badge variant="secondary" className="ml-1 text-xs">
            <Crown className="h-3 w-3 mr-1" />
            Pro
          </Badge>
        )}
        {showBadge && !isProUser && showUsageCount && (
          <Badge
            variant={isLimitReached ? 'destructive' : 'secondary'}
            className="ml-1 text-xs"
          >
            {remainingSearches}/{usageLimit}
          </Badge>
        )}
      </div>
    );
  };

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant={isExtreme ? 'default' : 'ghost'}
          size={variant === 'icon-only' ? 'icon' : 'sm'}
          onClick={toggleExtreme}
          disabled={isLoading || isLimitReached}
          className={cn(
            'transition-all duration-200',
            isExtreme &&
              'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700',
            isLimitReached && 'opacity-50 cursor-not-allowed',
            className,
          )}
        >
          {renderButtonContent()}
        </Button>
      </TooltipTrigger>
      <TooltipContent side="bottom" className="max-w-xs">
        {renderTooltipContent()}
      </TooltipContent>
    </Tooltip>
  );
}

// ============================================================================
// VARIANTES DU COMPOSANT
// ============================================================================

export function ExtremeSearchIconButton(
  props: Omit<ExtremeSearchButtonProps, 'variant'>,
) {
  return <ExtremeSearchButton {...props} variant="icon-only" />;
}

export function ExtremeSearchToggle({
  userId,
  isProUser = false,
  onToggle,
  className,
}: Omit<ExtremeSearchButtonProps, 'variant' | 'showBadge' | 'showUsageCount'>) {
  const { isExtreme, toggleExtreme, usageCount, usageLimit, isLoading } =
    useExtremeSearch({
      userId,
      isProUser,
      onToggle,
    });

  const isLimitReached = !isProUser && usageCount >= usageLimit;

  return (
    <div className={cn('flex items-center gap-3', className)}>
      <div className="flex-1">
        <div className="flex items-center gap-2">
          <Microscope className="h-4 w-4 text-purple-600" />
          <span className="text-sm font-medium">Recherche Extrême</span>
          {isProUser && (
            <Badge variant="secondary" className="text-xs">
              <Crown className="h-3 w-3 mr-1" />
              Pro
            </Badge>
          )}
        </div>
        <p className="text-xs text-muted-foreground mt-0.5">
          {isProUser
            ? 'Accès illimité'
            : `${Math.max(0, usageLimit - usageCount)}/${usageLimit} restantes`}
        </p>
      </div>
      <button
        type="button"
        role="switch"
        aria-checked={isExtreme}
        disabled={isLoading || isLimitReached}
        onClick={toggleExtreme}
        className={cn(
          'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
          'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
          isExtreme
            ? 'bg-gradient-to-r from-purple-600 to-blue-600'
            : 'bg-gray-200 dark:bg-gray-700',
          (isLoading || isLimitReached) && 'opacity-50 cursor-not-allowed',
        )}
      >
        <span
          className={cn(
            'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
            isExtreme ? 'translate-x-6' : 'translate-x-1',
          )}
        />
      </button>
    </div>
  );
}

// ============================================================================
// COMPOSANT DE STATUT
// ============================================================================

export function ExtremeSearchStatus({
  userId,
  isProUser = false,
  className,
}: {
  userId?: string;
  isProUser?: boolean;
  className?: string;
}) {
  const { isExtreme, usageCount, usageLimit } = useExtremeSearch({
    userId,
    isProUser,
  });

  if (!isExtreme) return null;

  return (
    <div
      className={cn(
        'flex items-center gap-2 px-3 py-1.5 rounded-full',
        'bg-gradient-to-r from-purple-600/10 to-blue-600/10',
        'border border-purple-600/20',
        className,
      )}
    >
      <Microscope className="h-3 w-3 text-purple-600 animate-pulse" />
      <span className="text-xs font-medium text-purple-600">
        Mode Extreme actif
      </span>
      {!isProUser && (
        <Badge variant="secondary" className="text-xs">
          {Math.max(0, usageLimit - usageCount)} restantes
        </Badge>
      )}
    </div>
  );
}

export default ExtremeSearchButton;
