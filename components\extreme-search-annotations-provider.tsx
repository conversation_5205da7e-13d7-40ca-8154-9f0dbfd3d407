'use client';

import React, { createContext, useContext, useMemo, useState } from 'react';
import type { DataUIPart } from 'ai';
import type { CustomUIDataTypes } from '@/lib/types';

// Define the shape of the context value
interface ExtremeSearchAnnotationsContextValue {
  annotations: DataUIPart<CustomUIDataTypes>[];
  setAnnotations: React.Dispatch<
    React.SetStateAction<DataUIPart<CustomUIDataTypes>[]>
  >;
  clearAnnotations: () => void;
}

// Create the context with a null default value
const ExtremeSearchAnnotationsContext =
  createContext<ExtremeSearchAnnotationsContextValue | null>(null);

// Create the provider component
export function ExtremeSearchAnnotationsProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [annotations, setAnnotations] = useState<
    DataUIPart<CustomUIDataTypes>[]
  >([]);

  const clearAnnotations = () => {
    setAnnotations([]);
  };

  const value = useMemo(
    () => ({ annotations, setAnnotations, clearAnnotations }),
    [annotations],
  );

  return (
    <ExtremeSearchAnnotationsContext.Provider value={value}>
      {children}
    </ExtremeSearchAnnotationsContext.Provider>
  );
}

// Create the custom hook for easy context consumption
export function useExtremeSearchAnnotations() {
  const context = useContext(ExtremeSearchAnnotationsContext);
  if (!context) {
    throw new Error(
      'useExtremeSearchAnnotations must be used within an ExtremeSearchAnnotationsProvider',
    );
  }
  return context;
}
