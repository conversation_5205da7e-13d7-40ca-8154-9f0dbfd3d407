/* Leaflet CSS for weather maps */
@import 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';

/* Custom styles for weather map integration */
.leaflet-container {
  font-family: inherit;
}

.leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.leaflet-popup-content {
  margin: 8px 12px;
  line-height: 1.4;
  font-size: 13px;
}

.leaflet-popup-tip {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Dark mode adjustments */
.dark .leaflet-popup-content-wrapper {
  background-color: #1f2937;
  color: #f9fafb;
}

.dark .leaflet-popup-tip {
  background-color: #1f2937;
}

.dark .leaflet-control-attribution {
  background-color: rgba(31, 41, 55, 0.8);
  color: #d1d5db;
}

.dark .leaflet-control-attribution a {
  color: #93c5fd;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .leaflet-popup-content {
    margin: 6px 8px;
    font-size: 12px;
  }

  .leaflet-control-zoom {
    margin-left: 5px !important;
    margin-top: 5px !important;
  }

  .leaflet-control-attribution {
    font-size: 10px;
  }
}

/* Weather layer opacity adjustments */
.leaflet-tile-pane .leaflet-layer:not(:first-child) {
  opacity: 0.85;
  /* Increased opacity for better visibility */
}

/* Specific adjustments for precipitation layers */
.leaflet-tile-pane .leaflet-layer[data-layer="precipitation_new"] {
  opacity: 0.95 !important;
  mix-blend-mode: multiply;
  /* Better blending for precipitation */
  filter: contrast(1.8) saturate(2.5) brightness(1.2) hue-rotate(-10deg) !important;
  /* Intensify precipitation colors: more contrast, saturation, and slight hue shift for better blues/greens */
}

/* Enhance contrast for other weather data */
.leaflet-tile-pane .leaflet-layer:not(:first-child):not([data-layer="precipitation_new"]) {
  filter: contrast(1.2) saturate(1.4);
}

/* Additional precipitation enhancement */
.leaflet-tile-pane .leaflet-layer {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Force precipitation layer styling via CSS class */
.precipitation-layer {
  opacity: 0.95 !important;
  filter: contrast(1.8) saturate(2.5) brightness(1.2) hue-rotate(-10deg) !important;
  mix-blend-mode: multiply !important;
}

/* Enhanced weather layer styling */
.weather-layer {
  opacity: 0.75 !important;
  filter: contrast(1.3) saturate(1.6) brightness(1.1) !important;
}

/* Specific enhancements for different weather types */
.leaflet-tile-pane .leaflet-layer[data-layer*="clouds"] {
  filter: contrast(1.4) saturate(1.8) brightness(1.1) !important;
}

.leaflet-tile-pane .leaflet-layer[data-layer*="temp"] {
  filter: contrast(1.5) saturate(2.0) brightness(1.2) hue-rotate(5deg) !important;
}

.leaflet-tile-pane .leaflet-layer[data-layer*="wind"] {
  filter: contrast(1.3) saturate(1.7) brightness(1.1) !important;
}

/* Additional color enhancement for precipitation */
.precipitation-layer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
      rgba(0, 100, 255, 0.1) 0%,
      rgba(0, 200, 100, 0.1) 50%,
      rgba(0, 150, 255, 0.1) 100%);
  pointer-events: none;
  mix-blend-mode: overlay;
}

/* Custom marker styles */
.leaflet-marker-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Loading state */
.leaflet-container.leaflet-loading {
  cursor: wait;
}

/* Zoom control styling */
.leaflet-control-zoom a {
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .leaflet-control-zoom a {
  background-color: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.dark .leaflet-control-zoom a:hover {
  background-color: #4b5563;
}