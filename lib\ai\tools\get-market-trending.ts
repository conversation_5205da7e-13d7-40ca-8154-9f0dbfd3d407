import { tool } from 'ai';
import { z } from 'zod';

export const showMarketTrending = tool({
  description: 'Get the current market trending data showing top gainers, losers, and most active stocks',
  inputSchema: z.object({
    // No parameters needed as this shows general market data
  }),
  execute: async () => {
    // This tool doesn't need any parameters as it shows general market data
    // The frontend component will use TradingView's widget to display the data
    return {
      timestamp: new Date().toISOString(),
    };
  },
});
