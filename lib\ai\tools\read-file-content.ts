import { tool } from 'ai';
import { z } from 'zod';

export const readFileContent = tool({
  description:
    'Extract and read content from uploaded files (PDF, TXT, images, etc.)',
  inputSchema: z.object({
    url: z.string().describe('The URL of the uploaded file to read'),
    mediaType: z
      .string()
      .describe(
        'The media type of the file (e.g., application/pdf, text/plain, image/jpeg)',
      ),
  }),
  execute: async ({ url, mediaType }: { url: string; mediaType: string }) => {
    try {
      console.log(`Reading file content from: ${url} (${mediaType})`);

      // Fetch the file content
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(
          `Failed to fetch file: ${response.status} ${response.statusText}`,
        );
      }

      // Handle different file types
      if (mediaType === 'application/pdf') {
        // For PDF files, we need to extract text content
        try {
          // Use pdf-parse library for PDF text extraction
          const pdfParse = await import('pdf-parse');
          const buffer = await response.arrayBuffer();
          const data = await pdfParse.default(Buffer.from(buffer));

          return {
            success: true,
            content: data.text,
            metadata: {
              pages: data.numpages,
              info: data.info,
              fileType: 'PDF',
              url: url,
            },
          };
        } catch (pdfError) {
          console.error('PDF parsing error:', pdfError);
          return {
            success: false,
            error:
              'Failed to parse PDF content. The PDF might be encrypted or corrupted.',
            fileType: 'PDF',
            url: url,
          };
        }
      } else if (mediaType.startsWith('text/')) {
        // Handle text files
        const textContent = await response.text();
        return {
          success: true,
          content: textContent,
          metadata: {
            fileType: 'Text',
            encoding: 'UTF-8',
            url: url,
          },
        };
      } else if (mediaType.startsWith('image/')) {
        // For images, we can't extract text content directly
        // But we can provide metadata and suggest using vision capabilities
        return {
          success: true,
          content: `Image file detected: ${mediaType}. This is a visual file that requires image analysis capabilities to describe its content.`,
          metadata: {
            fileType: 'Image',
            mediaType: mediaType,
            url: url,
            note: 'Use vision capabilities to analyze image content',
          },
        };
      } else if (mediaType === 'application/json') {
        // Handle JSON files
        const jsonContent = await response.text();
        try {
          const parsed = JSON.parse(jsonContent);
          return {
            success: true,
            content: JSON.stringify(parsed, null, 2),
            metadata: {
              fileType: 'JSON',
              url: url,
            },
          };
        } catch (jsonError) {
          return {
            success: false,
            error: 'Invalid JSON format',
            fileType: 'JSON',
            url: url,
          };
        }
      } else {
        // For other file types, try to read as text
        try {
          const textContent = await response.text();
          return {
            success: true,
            content: textContent,
            metadata: {
              fileType: 'Unknown',
              mediaType: mediaType,
              url: url,
              note: 'File read as plain text',
            },
          };
        } catch (error) {
          return {
            success: false,
            error: `Unsupported file type: ${mediaType}`,
            fileType: mediaType,
            url: url,
          };
        }
      }
    } catch (error) {
      console.error('File reading error:', error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Unknown error occurred while reading file',
        url: url,
      };
    }
  },
});
