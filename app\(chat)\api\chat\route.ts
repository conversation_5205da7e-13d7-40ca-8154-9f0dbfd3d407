import {
  convertToModelMessages,
  createUIMessageStream,
  JsonToSseTransformStream,
  smoothStream,
  stepCountIs,
  streamText,
  tool,
} from 'ai';
import { auth, type UserType } from '@/app/(auth)/auth';
import { type RequestHints, systemPrompt } from '@/lib/ai/prompts';
import {
  createStreamId,
  getChatById,
  getMessageCountByUserId,
  getMessagesByChatId,
  saveChat,
  saveMessages,
} from '@/lib/db/queries';
import { convertToUIMessages, generateUUID } from '@/lib/utils';
import { generateTitleFromUserMessage } from '../../actions';
import { createDocument } from '@/lib/ai/tools/create-document';
import { updateDocument } from '@/lib/ai/tools/update-document';
import { requestSuggestions } from '@/lib/ai/tools/request-suggestions';
import { getWeather } from '@/lib/ai/tools/get-weather';
import { getStockChart } from '@/lib/ai/tools/get-stock-chart';
import { getStockNews } from '@/lib/ai/tools/get-stock-news';
import { getStockPrice } from '@/lib/ai/tools/get-stock-price';
import { translateTextTool } from '@/lib/ai/tools/translate-text';
import { getStockScreener } from '@/lib/ai/tools/get-stock-screener';
import { getHeatmapsMarket } from '@/lib/ai/tools/get-heatmaps-market';
import { getCryptoCoinsHeatmap } from '@/lib/ai/tools/get-crypto-coins-heatmap';
import { getETFHeatmap } from '@/lib/ai/tools/get-etf-heatmap';
import { getForexCrossRates } from '@/lib/ai/tools/get-forex-cross-rates';
import { getForexHeatmap } from '@/lib/ai/tools/get-forex-heatmap';
import { getCryptocurrencyMarket } from '@/lib/ai/tools/get-cryptocurrency-market';
import { getSymbolInfo } from '@/lib/ai/tools/get-symbol-info';
import { getTechnicalAnalysis } from '@/lib/ai/tools/get-technical-analysis';
import { getCompanyProfile } from '@/lib/ai/tools/get-company-profile';
import { getEconomicCalendar } from '@/lib/ai/tools/get-economic-calendar';
import { getStockFinancials } from '@/lib/ai/tools/get-stock-financials';
import { showMarketTrending } from '@/lib/ai/tools/get-market-trending';
import { memoryRetrieval } from '@/lib/ai/tools/memory-retrieval';
import { remember } from '@/lib/ai/tools/remember';
import { extremeSearchToolImproved } from '@/lib/ai/tools/extreme-search-improved';
import { webSearchEnhancedTool } from '@/lib/ai/tools/web-search-enhanced';
import { youtube_search } from '@/lib/ai/tools/youtube-search';
import { map_search } from '@/lib/ai/tools/map-search';
import { readFileContent } from '@/lib/ai/tools/read-file-content';
import { imageSearchTool } from '@/lib/ai/tools/image-search';
import { createXSearchTool } from '@/lib/ai/tools/x-search';
import '@/lib/object-entries-patch';
import FirecrawlApp from '@mendable/firecrawl-js';
import { z } from 'zod';
import type { MemoryClient } from 'mem0ai';
import { enqueueMemory } from '@/lib/memoryBatcher';
import { isProductionEnvironment } from '@/lib/constants';
import { myProvider } from '@/lib/ai/providers';
import { entitlementsByUserType } from '@/lib/ai/entitlements';
import {
  createResumableStreamContext,
  type ResumableStreamContext,
} from 'resumable-stream';
import { geolocation } from '@vercel/functions';
import { ChatSDKError } from '@/lib/errors';
import { after } from 'next/server';
import type { ChatMessage } from '@/lib/types';
import type { ChatModel } from '@/lib/ai/models';
import type { VisibilityType } from '@/components/visibility-selector';
import { postRequestBodySchema, type PostRequestBody } from './schema';

export const maxDuration = 60;

// Fonction améliorée pour sauvegarder les messages dans la mémoire
async function saveMessageToMemory(
  _client: MemoryClient,
  message: ChatMessage | any,
  userId: string,
  _orgId: string,
  _projectId: string,
  chatId: string,
  _timeoutMs = 10000,
) {
  try {
    let messageContent = '';

    if (message.parts) {
      messageContent = message.parts
        .map((part: any) => {
          if (typeof part === 'string') {
            return part;
          } else if (part.text) {
            return part.text;
          } else if (part.content) {
            return part.content;
          }
          return JSON.stringify(part);
        })
        .filter(Boolean)
        .join('\n')
        .trim();
    } else if (typeof message.content === 'string') {
      try {
        const parsed = JSON.parse(message.content);
        messageContent = parsed.text || parsed.content || message.content;
      } catch (e) {
        messageContent = message.content;
      }
    } else if (message.content) {
      messageContent = String(message.content);
    }

    const content = messageContent;

    const metadata: Record<string, any> = {
      messageId: message.id,
      chatId,
      timestamp: new Date().toISOString(),
      role: message.role,
      memory_type: 'conversation',
      categories_info: ['message', message.role].join(','),
    };

    const personalInfoPatterns = [
      {
        pattern: /mon nom est|je m'appelle|je suis/i,
        type: 'demographic',
        category: 'name',
      },
      {
        pattern: /mon email|mon courriel|mon adresse e-mail/i,
        type: 'contact',
        category: 'email',
      },
      {
        pattern: /mon téléphone|mon numéro|mon portable/i,
        type: 'contact',
        category: 'phone',
      },
      {
        pattern: /j'aime|je préfère|ma préférence/i,
        type: 'preference',
        category: 'general',
      },
      {
        pattern: /ma langue|je parle|langue maternelle/i,
        type: 'demographic',
        category: 'language',
      },
      {
        pattern: /mon pays|j'habite|je vis à/i,
        type: 'demographic',
        category: 'location',
      },
      {
        pattern: /mon hobby|mon loisir|ma passion/i,
        type: 'preference',
        category: 'hobby',
      },
    ];

    if (message.role === 'user') {
      for (const { pattern, type, category } of personalInfoPatterns) {
        if (pattern.test(messageContent)) {
          metadata.may_contain_personal_info = true;
          metadata.potential_info_type = type;
          metadata.potential_info_category = category;
          metadata.categories_info += `,potential_personal_info,${type},${category}`;
          break;
        }
      }

      enqueueMemory({
        data: content,
        userId,
        metadata,
        priority: true,
      });
    } else {
      enqueueMemory({
        data: content,
        userId,
        metadata,
      });
    }

    return true;
  } catch (error) {
    logError('Failed to save message to memory:', {
      messageId: message.id,
      role: message.role,
      error: error instanceof Error ? error.message : String(error),
    });
    return false;
  }
}

// Cache pour stocker les configurations par userId
const memoryEnvCache = new Map<
  string,
  {
    apiKey: string;
    orgId: string;
    projectId: string;
    userId: string;
  }
>();

function getMemoryEnv(userId?: string) {
  if (!userId) {
    throw new Error('User ID is required for memory operations');
  }

  const cachedConfig = memoryEnvCache.get(userId);
  if (cachedConfig) {
    return cachedConfig;
  }

  const { MEM0_API_KEY, MEM0_ORG_ID, MEM0_PROJECT_ID } = process.env;

  if (!MEM0_API_KEY || !MEM0_ORG_ID || !MEM0_PROJECT_ID) {
    throw new Error('Missing Mem0 environment variables');
  }

  const config = {
    apiKey: MEM0_API_KEY,
    orgId: MEM0_ORG_ID,
    projectId: MEM0_PROJECT_ID,
    userId,
  };

  memoryEnvCache.set(userId, config);

  return config;
}

const log = (...args: any[]) => {
  if (!isProductionEnvironment) console.log(...args);
};

const logError = (message: string, error: unknown) => {
  const errorInfo =
    error instanceof Error
      ? {
          name: error.name,
          message: error.message,
          stack: isProductionEnvironment
            ? undefined
            : error.stack?.split('\n').slice(0, 3).join('\n'),
        }
      : typeof error === 'object' && error !== null
        ? { ...error }
        : { message: String(error) };

  console.error(`${message}`, errorInfo);
};

let globalStreamContext: ResumableStreamContext | null = null;

export function getStreamContext() {
  if (!globalStreamContext) {
    try {
      globalStreamContext = createResumableStreamContext({
        waitUntil: after,
      });
    } catch (error: any) {
      if (error.message.includes('REDIS_URL')) {
        console.log(
          ' > Resumable streams are disabled due to missing REDIS_URL',
        );
      } else {
        console.error(error);
      }
    }
  }

  return globalStreamContext;
}

// Note: Object.entries shim is applied globally via '@/lib/object-entries-patch'.

export async function POST(request: Request) {
  let requestBody: PostRequestBody;

  try {
    const json = await request.json();
    requestBody = postRequestBodySchema.parse(json);
  } catch (_) {
    return new ChatSDKError('bad_request:api').toResponse();
  }

  try {
    const {
      id,
      message,
      selectedChatModel,
      selectedVisibilityType,
      extremeSearchActive = false,
    }: {
      id: string;
      message: ChatMessage;
      selectedChatModel: ChatModel['id'];
      selectedVisibilityType: VisibilityType;
      extremeSearchActive?: boolean;
    } = requestBody as any;

    // Debug log pour vérifier si extremeSearchActive est reçu
    console.log('\n🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥');
    console.log('🔍 DEBUG - extremeSearchActive:', extremeSearchActive);
    console.log('🔍 DEBUG - Type:', typeof extremeSearchActive);
    console.log('🔍 DEBUG - selectedChatModel:', selectedChatModel);
    console.log(
      '🔍 DEBUG - Full requestBody:',
      JSON.stringify(requestBody).substring(0, 300),
    );
    console.log('🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥\n');

    const session = await auth();

    if (!session?.user) {
      return new ChatSDKError('unauthorized:chat').toResponse();
    }

    const userType: UserType = session.user.type;

    const messageCount = await getMessageCountByUserId({
      id: session.user.id,
      differenceInHours: 24,
    });

    if (messageCount > entitlementsByUserType[userType].maxMessagesPerDay) {
      return new ChatSDKError('rate_limit:chat').toResponse();
    }

    const chat = await getChatById({ id });

    if (!chat) {
      const title = await generateTitleFromUserMessage({
        message,
      });

      await saveChat({
        id,
        userId: session.user.id,
        title,
        visibility: selectedVisibilityType,
      });
    } else {
      if (chat.userId !== session.user.id) {
        return new ChatSDKError('forbidden:chat').toResponse();
      }
    }

    const messagesFromDb = await getMessagesByChatId({ id });
    const uiMessages = [...convertToUIMessages(messagesFromDb), message];

    // Récupération automatique de la mémoire utilisateur pour personnaliser les réponses
    let userMemoryContext = '';
    try {
      const { apiKey, orgId, projectId, userId } = getMemoryEnv(
        session.user?.id,
      );
      const { MemoryClient } = await import('mem0ai');
      const client = new MemoryClient({ apiKey });

      // Recherche automatique des souvenirs personnels de l'utilisateur
      const memoryResults = await client.search(
        'user personal information preferences',
        {
          user_id: userId,
          org_id: orgId,
          project_id: projectId,
          api_version: 'v2',
          filters: { user_id: userId },
          limit: 10,
        },
      );

      if (
        memoryResults &&
        Array.isArray(memoryResults) &&
        memoryResults.length > 0
      ) {
        const memories = memoryResults
          .map((item: any) => item.memory || item.text || item.content)
          .filter(Boolean);
        if (memories.length > 0) {
          userMemoryContext = `\n\nCONTEXTE PERSONNEL DE L'UTILISATEUR (à utiliser pour personnaliser vos réponses) :\n${memories.slice(0, 5).join('\n- ')}\n`;
          log('Auto-loaded user memory context:', {
            memoryCount: memories.length,
          });
        }
      }
    } catch (error) {
      logError('Failed to auto-load user memory:', error);
      // Continue sans mémoire si erreur
    }

    const { longitude, latitude, city, country } = geolocation(request);

    const requestHints: RequestHints = {
      longitude,
      latitude,
      city,
      country,
    };

    await saveMessages({
      messages: [
        {
          chatId: id,
          id: message.id,
          role: 'user',
          parts: message.parts,
          attachments:
            message.parts
              ?.filter((part: any) => part.type === 'file')
              .map((part: any) => ({
                name: part.name || 'image',
                contentType: part.mediaType || 'image/jpeg',
                url: part.url,
              })) || [],
          createdAt: new Date(),
        },
      ],
    });

    const streamId = generateUUID();
    await createStreamId({ streamId, chatId: id });

    const stream = createUIMessageStream({
      execute: ({ writer: dataStream }) => {
        const textStream = streamText({
          model: myProvider.languageModel(selectedChatModel),
          system: systemPrompt({
            selectedChatModel,
            requestHints,
            userMemoryContext,
            extremeSearchActive: extremeSearchActive, // Force explicit property
          }),
          messages: convertToModelMessages(uiMessages),
          // ✅ En mode extreme: 1 seule étape (extreme_search uniquement)
          // En mode normal: 5 étapes maximum
          stopWhen: stepCountIs(extremeSearchActive ? 1 : 5),
          // ✅ Forcer l'utilisation d'un outil en mode extreme
          ...(extremeSearchActive &&
            selectedChatModel !== 'chat-model-reasoning' && {
              toolChoice: 'required' as const,
            }),
          experimental_activeTools: (selectedChatModel ===
          'chat-model-reasoning'
            ? [
                'memoryRetrieval',
                'remember',
                'datetime',
                'web_search_enhanced',
                'youtube_search',
                'map_search',
                'x_search',
                'retrieve',
                'readFileContent',
                'getStockChart',
                'getStockNews',
                'getStockScreener',
                'getHeatmapsMarket',
                'getCryptoCoinsHeatmap',
                'getETFHeatmap',
                'getForexCrossRates',
                'getForexHeatmap',
                'getCryptocurrencyMarket',
                'getSymbolInfo',
                'getTechnicalAnalysis',
                'getCompanyProfile',
                'getEconomicCalendar',
                'getStockFinancials',
                'showMarketTrending',
                'translateTextTool',
              ]
            : extremeSearchActive
              ? [
                  // MODE EXTREME SEARCH: UNIQUEMENT extreme_search
                  'extreme_search',
                  // ❌ AUCUN autre outil en mode extreme
                  // L'outil extreme_search gère tout en interne
                ]
              : [
                  'getWeather',
                  'getStockChart',
                  'getStockNews',
                  'getStockScreener',
                  'getHeatmapsMarket',
                  'getCryptoCoinsHeatmap',
                  'getETFHeatmap',
                  'getForexCrossRates',
                  'getForexHeatmap',
                  'getCryptocurrencyMarket',
                  'getSymbolInfo',
                  'getTechnicalAnalysis',
                  'getCompanyProfile',
                  'getEconomicCalendar',
                  'getStockPrice',
                  'getStockFinancials',
                  'showMarketTrending',
                  'translateTextTool',
                  'createDocument',
                  'updateDocument',
                  'requestSuggestions',
                  'memoryRetrieval',
                  'remember',
                  'datetime',
                  'web_search_enhanced',
                  'youtube_search',
                  'map_search',
                  'x_search',
                  'retrieve',
                  'readFileContent',
                ]) as any,
          experimental_transform: smoothStream({ chunking: 'word' }),
          tools: {
            getWeather,
            getStockChart,
            getStockNews,
            getStockScreener,
            getHeatmapsMarket,
            getCryptoCoinsHeatmap,
            getETFHeatmap,
            getForexCrossRates,
            getForexHeatmap,
            getCryptocurrencyMarket,
            getSymbolInfo,
            getTechnicalAnalysis,
            getCompanyProfile,
            getEconomicCalendar,
            getStockFinancials,
            getStockPrice,
            showMarketTrending,
            translateTextTool,
            createDocument: createDocument({ session, dataStream }),
            updateDocument: updateDocument({ session, dataStream }),
            // MODE EXTREME SEARCH: Seulement extreme_search, sinon web_search_enhanced
            ...(extremeSearchActive
              ? {
                  extreme_search: extremeSearchToolImproved(dataStream),
                }
              : {
                  web_search_enhanced: webSearchEnhancedTool(
                    dataStream,
                    'tavily',
                  ),
                }),
            image_search: imageSearchTool,
            youtube_search: youtube_search({ session, dataStream }),
            map_search: map_search({ session, dataStream }),
            x_search: createXSearchTool(dataStream),
            requestSuggestions: requestSuggestions({
              session,
              dataStream,
            }),
            memoryRetrieval,
            remember,
            readFileContent,
            retrieve: tool({
              description:
                'Retrieve the information from a URL using Firecrawl.',
              inputSchema: z.object({
                url: z
                  .string()
                  .describe('The URL to retrieve the information from.'),
              }),
              execute: async ({ url }: { url: string }) => {
                const app = new FirecrawlApp({
                  apiKey: process.env.FIRECRAWL_API_KEY,
                });
                try {
                  const content = await app.scrape(url, {
                    formats: ['markdown', 'html'],
                  });

                  if (!content || !content.metadata) {
                    return {
                      results: [
                        {
                          error: 'Failed to retrieve content',
                        },
                      ],
                    };
                  }

                  return {
                    results: [
                      {
                        title: content.metadata.title || 'Untitled',
                        content: content.markdown || '',
                        url: content.metadata.sourceURL || url,
                        description: content.metadata.description || '',
                        language: content.metadata.language || 'en',
                      },
                    ],
                  };
                } catch (error) {
                  console.error('Firecrawl API error:', error);
                  return { error: 'Failed to retrieve content' };
                }
              },
            }),
            datetime: tool({
              description:
                "Get the current date and time in the user's timezone. Use this tool when the user asks for the current time, date, or both.",
              inputSchema: z.object({}),
              execute: async () => {
                try {
                  const now = new Date();
                  return {
                    timestamp: now.getTime(),
                    iso: now.toISOString(),
                    timezone: 'UTC',
                    locale: 'fr-FR',
                    formatted: {
                      date: now.toLocaleDateString('fr-FR', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      }),
                      time: now.toLocaleTimeString('fr-FR', {
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                      }),
                    },
                  };
                } catch (error) {
                  console.error('Error getting datetime:', error);
                  return {
                    error: 'Failed to get current date and time',
                  };
                }
              },
            }),
            memory_manager: tool({
              description:
                'Manage personal memories with add, search, and personal information operations.',
              inputSchema: z.object({
                action: z
                  .enum([
                    'add',
                    'search',
                    'add_personal_info',
                    'search_personal_info',
                  ])
                  .describe('The memory operation to perform'),
                content: z
                  .string()
                  .optional()
                  .describe('The memory content for add operation'),
                query: z
                  .string()
                  .optional()
                  .describe('The search query for search operations'),
                info_type: z
                  .string()
                  .optional()
                  .describe(
                    'The type of personal information (e.g., "preference", "contact", "demographic")',
                  ),
                info_category: z
                  .string()
                  .optional()
                  .describe(
                    'The category of personal information (e.g., "name", "email", "language", "hobby")',
                  ),
              }),
              execute: async ({
                action,
                content,
                query,
                info_type,
                info_category,
              }: {
                action:
                  | 'add'
                  | 'search'
                  | 'add_personal_info'
                  | 'search_personal_info';
                content?: string;
                query?: string;
                info_type?: string;
                info_category?: string;
              }) => {
                try {
                  const { apiKey, orgId, projectId, userId } = getMemoryEnv(
                    session.user?.id,
                  );
                  const { MemoryClient } = await import('mem0ai');
                  const client = new MemoryClient({ apiKey });

                  log('Memory operation:', {
                    action,
                    hasContent: !!content,
                    contentLength: content ? content.length : 0,
                    hasQuery: !!query,
                    queryLength: query ? query.length : 0,
                    info_type,
                    info_category,
                  });

                  switch (action) {
                    case 'add': {
                      if (!content) {
                        return {
                          success: false,
                          action: 'add',
                          message: 'Content is required for add operation',
                        };
                      }
                      const messageData = [
                        {
                          role: 'user',
                          content: content,
                        },
                      ];

                      const result = await client.add(messageData, {
                        user_id: userId,
                        org_id: orgId,
                        project_id: projectId,
                        metadata: {
                          timestamp: new Date().toISOString(),
                          memory_type: 'general',
                        },
                      });
                      if (result.length === 0) {
                        return {
                          success: false,
                          action: 'add',
                          message: 'No memory added',
                        };
                      }
                      log('Memory add result:', {
                        success: true,
                        memoryId: result[0]?.id,
                        timestamp: new Date().toISOString(),
                      });
                      return {
                        success: true,
                        action: 'add',
                        memory: result[0],
                      };
                    }
                    case 'search': {
                      if (!query) {
                        return {
                          success: false,
                          action: 'search',
                          message: 'Query is required for search operation',
                        };
                      }

                      try {
                        const filters = {
                          user_id: userId,
                        };

                        const result = await client.search(query, {
                          user_id: userId,
                          org_id: orgId,
                          project_id: projectId,
                          api_version: 'v2',
                          filters: filters,
                          limit: 10,
                        });

                        log('Memory search results:', {
                          success: true,
                          count: Array.isArray(result)
                            ? result.length
                            : result
                              ? 1
                              : 0,
                          timestamp: new Date().toISOString(),
                        });

                        if (!result || result.length === 0) {
                          return {
                            success: true,
                            action: 'search',
                            results: [],
                          };
                        }

                        const searchResults = Array.isArray(result)
                          ? result
                          : [result];
                        const itemsToProcess = Array.isArray(searchResults[0])
                          ? searchResults[0]
                          : searchResults;

                        const enrichedResults = itemsToProcess.map(
                          (item: any) => ({
                            ...item,
                            chatId: item.metadata?.chatId,
                            messageRole: item.metadata?.role,
                            timestamp: item.metadata?.timestamp,
                            messageId: item.metadata?.messageId,
                            memory_type:
                              item.metadata?.memory_type || 'general',
                            info_type: item.metadata?.info_type,
                            info_category: item.metadata?.info_category,
                          }),
                        );

                        return {
                          success: true,
                          action: 'search',
                          results: enrichedResults,
                        };
                      } catch (error: unknown) {
                        logError('Search error:', error);
                        return {
                          success: false,
                          action: 'search',
                          message: `Error during search: ${error instanceof Error ? error.message : 'Unknown error'}`,
                        };
                      }
                    }
                  }
                } catch (error) {
                  logError('Memory operation error:', error);
                  throw error;
                }
              },
            }),
          },
          experimental_telemetry: {
            isEnabled: isProductionEnvironment,
            functionId: 'stream-text',
          },
        });

        textStream.consumeStream();

        dataStream.merge(
          textStream.toUIMessageStream({
            sendReasoning: true,
          }),
        );
      },
      generateId: generateUUID,
      onFinish: async ({ messages }) => {
        try {
          await saveMessages({
            messages: messages.map((message: any) => ({
              id: message.id,
              role: message.role,
              parts: message.parts,
              createdAt: new Date(),
              attachments: message.experimental_attachments ?? [],
              chatId: id,
            })),
          });
        } catch (error) {
          logError('Failed to persist streamed messages to DB:', error);
        }
      },
      onError: (error) => {
        console.log(error);
        return 'Oops, an error occurred!';
      },
    });

    const streamContext = getStreamContext();

    if (streamContext) {
      return new Response(
        await streamContext.resumableStream(streamId, () =>
          stream.pipeThrough(new JsonToSseTransformStream()),
        ),
      );
    } else {
      return new Response(stream);
    }
  } catch (error) {
    if (error instanceof ChatSDKError) {
      return error.toResponse();
    }
    console.error('Error deleting chat:', error);
    return new Response('An error occurred while processing your request!', {
      status: 500,
    });
  }
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new ChatSDKError('bad_request:api').toResponse();
  }

  const session = await auth();

  if (!session?.user?.id) {
    return new ChatSDKError('unauthorized:chat').toResponse();
  }

  try {
    const chat = await getChatById({ id });

    if (!chat) {
      return new ChatSDKError('not_found:chat').toResponse();
    }

    if (chat.userId !== session.user.id) {
      return new ChatSDKError('forbidden:chat').toResponse();
    }

    const { deleteChatById } = await import('@/lib/db/queries');
    await deleteChatById({ id });

    return new Response('Chat deleted', { status: 200 });
  } catch (error) {
    if (error instanceof ChatSDKError) {
      return error.toResponse();
    }
    console.error('Error deleting chat:', error);
    return new Response('An error occurred while processing your request!', {
      status: 500,
    });
  }
}
