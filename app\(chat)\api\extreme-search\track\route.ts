/**
 * API Route pour tracker l'utilisation de la recherche extrême
 * POST /api/extreme-search/track
 */

import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { extremeSearchUsage } from '@/lib/db/schema';
import { eq, and, gte } from 'drizzle-orm';

// Initialize database connection
// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json({ error: 'userId est requis' }, { status: 400 });
    }

    // Calculer le début du mois actuel
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Vérifier si un enregistrement existe pour ce mois
    const existing = await db
      .select()
      .from(extremeSearchUsage)
      .where(
        and(
          eq(extremeSearchUsage.userId, userId),
          gte(extremeSearchUsage.date, startOfMonth),
        ),
      )
      .limit(1);

    if (existing.length > 0) {
      // Incrémenter le compteur
      const updated = await db
        .update(extremeSearchUsage)
        .set({
          searchCount: String(Number.parseInt(existing[0].searchCount) + 1),
          updatedAt: now,
        })
        .where(eq(extremeSearchUsage.id, existing[0].id))
        .returning();

      return NextResponse.json({
        success: true,
        count: Number.parseInt(updated[0].searchCount),
        month: now.toISOString().slice(0, 7),
      });
    } else {
      // Créer un nouvel enregistrement
      const created = await db
        .insert(extremeSearchUsage)
        .values({
          userId,
          searchCount: '1',
          date: now,
          createdAt: now,
          updatedAt: now,
        })
        .returning();

      return NextResponse.json({
        success: true,
        count: Number.parseInt(created[0].searchCount),
        month: now.toISOString().slice(0, 7),
      });
    }
  } catch (error) {
    console.error('Erreur lors du tracking:', error);
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
  }
}
