// Global patch for Object.entries to handle null/undefined values
const originalObjectEntries = Object.entries;
const g = globalThis as any;
const isProd = process.env.NODE_ENV === 'production';

if (typeof window === 'undefined' && !g.__OBJECT_ENTRIES_PATCHED) {
  Object.entries = (obj: any): [string, any][] => {
    if (obj == null) {
      // Warn only once and only in non-production
      if (!isProd && !g.__OBJECT_ENTRIES_WARNED) {
        console.warn(
          'Object.entries called with null/undefined, returning empty array',
        );
        g.__OBJECT_ENTRIES_WARNED = true;
      }
      return [];
    }
    return originalObjectEntries(obj as any);
  };

  g.__OBJECT_ENTRIES_PATCHED = true;
}

export {};
