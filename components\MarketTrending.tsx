'use client';

import React, { useEffect, useRef, memo } from 'react';

interface MarketTrendingProps {
  className?: string;
}

function MarketTrending({ className = '' }: MarketTrendingProps) {
  const container = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!container.current) return;

    const currentContainer = container.current;

    // Clear previous script if it exists
    currentContainer.innerHTML = '';

    const script = document.createElement('script');
    script.src =
      'https://s3.tradingview.com/external-embedding/embed-widget-hotlists.js';
    script.type = 'text/javascript';
    script.async = true;
    script.innerHTML = JSON.stringify({
      colorTheme: 'light',
      dateRange: '1D',
      exchange: 'US',
      showChart: true,
      locale: 'en',
      largeChartUrl: '',
      isTransparent: false,
      showSymbolLogo: true,
      showFloatingTooltip: true,
      width: '100%',
      height: '100%',
      plotLineColorGrowing: 'rgba(106, 168, 79, 1)',
      plotLineColorFalling: 'rgba(255, 0, 0, 1)',
      gridLineColor: 'rgba(0, 0, 0, 0)',
      scaleFontColor: 'rgba(19, 23, 34, 1)',
      belowLineFillColorGrowing: 'rgba(41, 98, 255, 0.12)',
      belowLineFillColorFalling: 'rgba(41, 98, 255, 0.12)',
      belowLineFillColorGrowingBottom: 'rgba(41, 98, 255, 0)',
      belowLineFillColorFallingBottom: 'rgba(41, 98, 255, 0)',
      symbolActiveColor: 'rgba(60, 120, 216, 0.12)',
    });

    currentContainer.appendChild(script);

    return () => {
      if (currentContainer) {
        currentContainer.removeChild(script);
      }
    };
  }, []);

  return (
    <div className={`h-[500px] w-full ${className}`}>
      <div className="tradingview-widget-container h-full" ref={container}>
        <div
          className="tradingview-widget-container__widget"
          style={{ height: 'calc(100% - 32px)' }}
        />
        <div className="tradingview-widget-copyright">
          <a
            href="https://www.tradingview.com/"
            rel="noopener nofollow"
            target="_blank"
            className="text-xs text-muted-foreground hover:underline"
          >
            Track all markets on TradingView
          </a>
        </div>
      </div>
    </div>
  );
}

export default memo(MarketTrending);
