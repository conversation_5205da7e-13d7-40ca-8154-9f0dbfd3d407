'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Cloud,
  CloudRain,
  Thermometer,
  Wind,
  Gauge,
  MapPin,
  RefreshCw,
  AlertCircle,
} from 'lucide-react';
import WeatherColorScale from './weather-color-scale';

interface WeatherMapIntegratedProps {
  latitude: number;
  longitude: number;
  city: string;
  apiKey: string;
}

const weatherLayers = [
  {
    id: 'clouds_new',
    name: 'Clouds',
    icon: Cloud,
    color: 'bg-gray-500',
    description: 'Nuages',
  },
  {
    id: 'precipitation_new',
    name: 'Rain',
    icon: CloudRain,
    color: 'bg-blue-500',
    description: 'Précipitations',
  },
  {
    id: 'temp_new',
    name: 'Temperature',
    icon: Thermometer,
    color: 'bg-orange-500',
    description: 'Température',
  },
  {
    id: 'wind_new',
    name: 'Wind',
    icon: Wind,
    color: 'bg-green-500',
    description: 'Vent',
  },
  {
    id: 'pressure_new',
    name: 'Pressure',
    icon: Gauge,
    color: 'bg-purple-500',
    description: 'Pression',
  },
];

const WeatherMapIntegrated: React.FC<WeatherMapIntegratedProps> = ({
  latitude,
  longitude,
  city,
  apiKey,
}) => {
  const { resolvedTheme } = useTheme();
  const [selectedLayer, setSelectedLayer] = useState('precipitation_new');
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<any>(null);
  const weatherLayerRef = useRef<any>(null);

  const isDark = resolvedTheme === 'dark';

  // Initialize Leaflet map
  useEffect(() => {
    const currentContainer = mapContainerRef.current;
    let resizeObserver: ResizeObserver | null;

    const initializeMap = async () => {
      if (
        typeof window === 'undefined' ||
        !currentContainer ||
        mapRef.current
      ) {
        return;
      }

      try {
        // Wait for container to be properly sized
        const waitForContainer = () => {
          return new Promise<void>((resolve) => {
            const checkDimensions = () => {
              if (!currentContainer) {
                resolve();
                return;
              }

              const rect = currentContainer.getBoundingClientRect();
              const computedStyle = window.getComputedStyle(currentContainer);

              // Check if container has proper dimensions
              if (
                rect.width > 0 &&
                rect.height > 0 &&
                computedStyle.display !== 'none' &&
                computedStyle.visibility !== 'hidden'
              ) {
                resolve();
              } else {
                // Force dimensions if needed
                currentContainer.style.width = '100%';
                currentContainer.style.height = '280px';
                currentContainer.style.minHeight = '280px';
                currentContainer.style.display = 'block';
                currentContainer.style.position = 'relative';
                currentContainer.style.visibility = 'visible';

                // Try again after a short delay
                setTimeout(checkDimensions, 50);
              }
            };
            checkDimensions();
          });
        };

        await waitForContainer();

        // Import Leaflet
        const L = await import('leaflet');

        // Clear any existing content
        if (currentContainer) {
          currentContainer.innerHTML = '';
          (currentContainer as any)._leaflet_id = null;
        }

        // Fix default markers
        L.Icon.Default.mergeOptions({
          iconRetinaUrl:
            'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
          iconUrl:
            'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
          shadowUrl:
            'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
        });

        // Create map with error handling
        const map = L.map(currentContainer, {
          zoomControl: true,
          attributionControl: true,
          preferCanvas: true,
          // Disable animations that might cause issues
          zoomAnimation: false,
          fadeAnimation: false,
          markerZoomAnimation: false,
        }).setView([latitude, longitude], 6);

        // Add base tile layer
        const baseTileLayer = isDark
          ? L.tileLayer(
              'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png',
              {
                attribution:
                  '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
                subdomains: 'abcd',
                maxZoom: 19,
              },
            )
          : L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
              attribution:
                '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            });

        baseTileLayer.addTo(map);

        // Create custom icon for the location marker
        const customIcon = L.divIcon({
          className: 'weather-location-marker',
          html: `
            <div style="position: relative; width: 32px; height: 32px;">
              <div style="position: absolute; width: 32px; height: 32px; background-color: #3b82f6; border-radius: 50% 50% 0 50%; transform: rotate(45deg); left: 0; top: 0;">
                <div style="position: absolute; width: 24px; height: 24px; background-color: white; border-radius: 50%; left: 4px; top: 4px; display: flex; align-items: center; justify-content: center;">
                  <MapPin size={12} color="#3b82f6" style="transform: rotate(-45deg);" />
                </div>
              </div>
            </div>
          `,
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32],
        });

        // Add marker for the requested location
        const marker = L.marker([latitude, longitude], {
          zIndexOffset: 1000, // Ensure marker appears above weather layers
          icon: customIcon,
          title: city,
        }).addTo(map);

        marker.bindPopup(`
          <div class="text-center">
            <b>${city}</b><br/>
            <small>Localisation météo</small>
          </div>
        `);

        // Ensure marker is visible by bringing it to front
        marker.on('add', () => {
          const markerElement = marker.getElement();
          if (markerElement) {
            markerElement.style.zIndex = '999';
          }
        });

        // Add initial weather layer
        const initialWeatherLayer = L.tileLayer(
          `https://tile.openweathermap.org/map/${selectedLayer}/{z}/{x}/{y}.png?appid=${apiKey}`,
          {
            attribution:
              '&copy; <a href="https://openweathermap.org/">OpenWeatherMap</a>',
            opacity: selectedLayer === 'precipitation_new' ? 0.95 : 0.7,
            maxZoom: 19,
            errorTileUrl:
              'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
            className:
              selectedLayer === 'precipitation_new'
                ? 'precipitation-layer'
                : 'weather-layer',
          },
        );

        initialWeatherLayer.addTo(map);
        weatherLayerRef.current = initialWeatherLayer;

        // Apply enhanced styling for precipitation layers
        if (selectedLayer === 'precipitation_new') {
          setTimeout(() => {
            try {
              const layerElement = initialWeatherLayer.getContainer();
              if (layerElement) {
                layerElement.style.filter =
                  'contrast(1.8) saturate(2.5) brightness(1.2) hue-rotate(-10deg)';
                layerElement.style.mixBlendMode = 'multiply';
              }
            } catch (e) {
              console.warn('Error applying precipitation styling:', e);
            }
          }, 100);
        }

        // Set up resize observer to handle container size changes
        resizeObserver = new ResizeObserver(() => {
          if (map && currentContainer) {
            try {
              map.invalidateSize();
            } catch (e) {
              console.warn('Error invalidating map size:', e);
            }
          }
        });

        if (currentContainer) {
          resizeObserver.observe(currentContainer);
        }

        mapRef.current = map;
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to initialize map:', error);
        setHasError(true);
        setIsLoading(false);
      }
    };

    // Start initialization
    const timer = setTimeout(initializeMap, 50);

    return () => {
      if (timer) {
        clearTimeout(timer);
      }

      if (resizeObserver) {
        resizeObserver.disconnect();
      }

      if (mapRef.current) {
        try {
          mapRef.current.remove();
        } catch (e) {
          console.warn('Error removing map during cleanup:', e);
        }
        mapRef.current = null;
      }

      weatherLayerRef.current = null;

      if (currentContainer) {
        try {
          currentContainer.innerHTML = '';
          (currentContainer as any)._leaflet_id = null;
        } catch (e) {
          console.warn('Error cleaning up map container:', e);
        }
      }
    };
  }, [latitude, longitude, city, isDark, apiKey, selectedLayer]);

  // Update weather layer when selection changes
  useEffect(() => {
    if (mapRef.current && weatherLayerRef.current) {
      try {
        // Remove current weather layer
        mapRef.current.removeLayer(weatherLayerRef.current);

        // Add new weather layer
        const L = (window as any).L;
        if (L) {
          const newWeatherLayer = L.tileLayer(
            `https://tile.openweathermap.org/map/${selectedLayer}/{z}/{x}/{y}.png?appid=${apiKey}&t=${Date.now()}`,
            {
              attribution:
                '&copy; <a href="https://openweathermap.org/">OpenWeatherMap</a>',
              opacity: selectedLayer === 'precipitation_new' ? 0.95 : 0.7,
              maxZoom: 19,
              errorTileUrl:
                'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
              className:
                selectedLayer === 'precipitation_new'
                  ? 'precipitation-layer'
                  : 'weather-layer',
            },
          );

          newWeatherLayer.addTo(mapRef.current);
          weatherLayerRef.current = newWeatherLayer;

          // Apply enhanced styling for precipitation layers
          if (selectedLayer === 'precipitation_new') {
            setTimeout(() => {
              try {
                const layerElement = newWeatherLayer.getContainer();
                if (layerElement) {
                  layerElement.style.filter =
                    'contrast(1.8) saturate(2.5) brightness(1.2) hue-rotate(-10deg)';
                  layerElement.style.mixBlendMode = 'multiply';
                }
              } catch (e) {
                console.warn('Error applying precipitation styling:', e);
              }
            }, 100);
          }
        }
      } catch (error) {
        console.error('Error updating weather layer:', error);
      }
    }
  }, [selectedLayer, apiKey]);

  const refreshMap = () => {
    if (mapRef.current && weatherLayerRef.current) {
      try {
        // Force refresh by adding timestamp
        const L = (window as any).L;
        if (L) {
          mapRef.current.removeLayer(weatherLayerRef.current);

          const refreshedLayer = L.tileLayer(
            `https://tile.openweathermap.org/map/${selectedLayer}/{z}/{x}/{y}.png?appid=${apiKey}&t=${Date.now()}`,
            {
              attribution:
                '&copy; <a href="https://openweathermap.org/">OpenWeatherMap</a>',
              opacity: selectedLayer === 'precipitation_new' ? 0.95 : 0.7,
              maxZoom: 19,
              errorTileUrl:
                'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
              className:
                selectedLayer === 'precipitation_new'
                  ? 'precipitation-layer'
                  : 'weather-layer',
            },
          );

          refreshedLayer.addTo(mapRef.current);
          weatherLayerRef.current = refreshedLayer;

          // Apply enhanced styling for precipitation layers
          if (selectedLayer === 'precipitation_new') {
            setTimeout(() => {
              try {
                const layerElement = refreshedLayer.getContainer();
                if (layerElement) {
                  layerElement.style.filter =
                    'contrast(1.8) saturate(2.5) brightness(1.2) hue-rotate(-10deg)';
                  layerElement.style.mixBlendMode = 'multiply';
                }
              } catch (e) {
                console.warn('Error applying precipitation styling:', e);
              }
            }, 100);
          }
        }
      } catch (error) {
        console.error('Error refreshing map:', error);
        setHasError(true);
      }
    }
  };

  return (
    <div className="space-y-3">
      {/* Layer Selection */}
      <div className="flex flex-wrap gap-1 justify-center">
        {weatherLayers.map((layer) => {
          const IconComponent = layer.icon;
          return (
            <Button
              key={layer.id}
              variant={selectedLayer === layer.id ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedLayer(layer.id)}
              className="flex items-center gap-1 text-[10px] px-1.5 py-1 h-7"
            >
              <IconComponent className="h-3 w-3" />
              <span className="hidden xs:inline">{layer.description}</span>
            </Button>
          );
        })}
      </div>

      {/* Map Info */}
      <div className="flex flex-wrap items-center justify-between gap-1">
        <div className="flex flex-wrap items-center gap-1">
          <Badge
            variant="secondary"
            className="flex items-center gap-1 text-[10px] px-1.5 py-0.5"
          >
            <MapPin className="h-2.5 w-2.5" />
            {city}
          </Badge>
          <Badge 
            variant="outline" 
            className="text-[10px] px-1.5 py-0.5 text-neutral-800 dark:text-neutral-200 border-neutral-300 dark:border-neutral-600 bg-white/50 dark:bg-neutral-800/50"
          >
            {weatherLayers.find((l) => l.id === selectedLayer)?.description}
          </Badge>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={refreshMap}
          className="flex items-center gap-1 text-[10px] px-1.5 py-1 h-7"
          disabled={isLoading}
        >
          <RefreshCw
            className={`h-2.5 w-2.5 ${isLoading ? 'animate-spin' : ''}`}
          />
          <span className="hidden xs:inline">Actualiser</span>
        </Button>
      </div>

      {/* Map Container */}
      <div className="relative w-full">
        <div
          ref={mapContainerRef}
          className="w-full h-[300px] rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden"
          style={{
            background: isDark ? '#1f1f1f' : '#f8f9fa',
            position: 'relative',
            zIndex: 1,
          }}
        />

        {/* Color Scale Overlay */}
        <div className="absolute bottom-4 left-2 z-[1000] max-w-[90%]">
          <WeatherColorScale layer={selectedLayer} />
        </div>

        {/* Loading overlay */}
        {isLoading && (
          <div className="absolute inset-0 bg-neutral-100/80 dark:bg-neutral-800/80 flex items-center justify-center backdrop-blur-sm rounded-lg">
            <div className="text-center space-y-2">
              <div className="animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mx-auto" />
              <div className="text-[10px] text-neutral-600 dark:text-neutral-400">
                Chargement de la carte...
              </div>
            </div>
          </div>
        )}

        {/* Error overlay */}
        {hasError && (
          <div className="absolute inset-0 bg-neutral-100/80 dark:bg-neutral-800/80 flex items-center justify-center backdrop-blur-sm rounded-lg">
            <div className="text-center p-3 space-y-2">
              <AlertCircle className="h-8 w-8 text-neutral-400 mx-auto" />
              <div>
                <h3 className="text-xs font-medium text-neutral-700 dark:text-neutral-300">
                  Carte indisponible
                </h3>
                <p className="text-[10px] text-neutral-500 dark:text-neutral-400 mt-1">
                  Impossible de charger la carte météo
                </p>
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={refreshMap}
                className="text-[10px] h-6 px-2"
              >
                Réessayer
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Legend */}
      <div className="text-[10px] text-neutral-500 dark:text-neutral-400 text-center">
        Données météo en temps réel • OpenWeatherMap
      </div>
    </div>
  );
};

export default WeatherMapIntegrated;
