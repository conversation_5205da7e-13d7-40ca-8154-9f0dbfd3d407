'use client';

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Globe } from 'lucide-react';

interface Source {
  title: string;
  url: string;
  content?: string;
  favicon?: string;
}

interface CitationLinkProps {
  sourceNumber: number;
  sources: Source[];
}

export const CitationLink: React.FC<CitationLinkProps> = ({
  sourceNumber,
  sources,
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipStyle, setTooltipStyle] = useState<React.CSSProperties>({});
  const linkRef = React.useRef<HTMLAnchorElement>(null);
  const tooltipRef = React.useRef<HTMLDivElement>(null);
  const closeTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);
  const [mounted, setMounted] = useState(false);
  const source = sources[sourceNumber - 1]; // Les sources sont indexées à partir de 0

  useEffect(() => {
    setMounted(true);
    return () => {
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, []);

  if (!source) {
    return <span className="text-neutral-500">[Source {sourceNumber}]</span>;
  }

  // Extraire le nom de domaine
  let sourceName = '';
  try {
    const urlObj = new URL(source.url);
    sourceName = urlObj.hostname.replace('www.', '');
    if (sourceName.length > 20) {
      sourceName = `${sourceName.substring(0, 17)}...`;
    }
  } catch {
    sourceName = `Source ${sourceNumber}`;
  }

  const handleMouseEnter = () => {
    // Annuler tout timeout de fermeture en cours
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }

    setShowTooltip(true);

    // Calculer la position du tooltip pour éviter qu'il soit tronqué
    if (linkRef.current) {
      const rect = linkRef.current.getBoundingClientRect();
      const tooltipWidth = 320; // w-80 = 320px
      const tooltipHeight = 150; // Hauteur approximative
      const viewportWidth = window.innerWidth;
      const padding = 10;

      let left = rect.left + rect.width / 2 - tooltipWidth / 2;
      let top = rect.top - tooltipHeight - 8; // 8px de marge

      // Ajuster si dépasse à gauche
      if (left < padding) {
        left = padding;
      }
      // Ajuster si dépasse à droite
      if (left + tooltipWidth > viewportWidth - padding) {
        left = viewportWidth - tooltipWidth - padding;
      }

      // Si pas assez d'espace en haut, afficher en bas
      if (top < padding) {
        top = rect.bottom + 8;
      }

      setTooltipStyle({
        position: 'fixed',
        left: `${left}px`,
        top: `${top}px`,
        zIndex: 9999,
      });
    }
  };

  const handleMouseLeave = () => {
    // Ajouter un délai avant de fermer le tooltip
    closeTimeoutRef.current = setTimeout(() => {
      setShowTooltip(false);
    }, 200); // 200ms de délai
  };

  const handleTooltipMouseEnter = () => {
    // Annuler la fermeture si on survole le tooltip
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
  };

  const handleTooltipMouseLeave = () => {
    // Fermer le tooltip quand on quitte le tooltip
    setShowTooltip(false);
  };

  return (
    <span className="relative inline-block">
      <a
        ref={linkRef}
        href={source.url}
        target="_blank"
        rel="noopener noreferrer"
        className="inline-flex items-center gap-1.5 px-2 py-1 rounded-md bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors text-xs font-medium no-underline"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={(e) => e.stopPropagation()}
      >
        {source.favicon ? (
          <img
            src={source.favicon}
            alt=""
            className="w-3.5 h-3.5 flex-shrink-0"
            onError={(e) => {
              // Fallback vers l'icône globe si le favicon ne charge pas
              e.currentTarget.style.display = 'none';
              const globeIcon = e.currentTarget.nextElementSibling;
              if (globeIcon) {
                (globeIcon as HTMLElement).style.display = 'inline-block';
              }
            }}
          />
        ) : null}
        <Globe
          className="h-3.5 w-3.5"
          style={{ display: source.favicon ? 'none' : 'inline-block' }}
        />
        <span>{sourceName}</span>
      </a>

      {showTooltip &&
        mounted &&
        createPortal(
          <div
            ref={tooltipRef}
            role="tooltip"
            style={tooltipStyle}
            onMouseEnter={handleTooltipMouseEnter}
            onMouseLeave={handleTooltipMouseLeave}
          >
            <div className="bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-lg shadow-xl p-3 w-80">
              <div className="flex items-start gap-2 mb-2">
                {source.favicon && (
                  <img
                    src={source.favicon}
                    alt=""
                    className="w-4 h-4 mt-0.5 flex-shrink-0"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                )}
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm text-neutral-900 dark:text-neutral-100 line-clamp-2">
                    {source.title}
                  </div>
                  <div className="text-xs text-blue-600 dark:text-blue-400 truncate mt-1">
                    {new URL(source.url).hostname}
                  </div>
                </div>
              </div>
              {source.content && (
                <p className="text-xs text-neutral-600 dark:text-neutral-400 line-clamp-3">
                  {source.content.substring(0, 200)}...
                </p>
              )}
            </div>
          </div>,
          document.body,
        )}
    </span>
  );
};

/**
 * Transforme les citations [Source X] en composants CitationLink
 */
export const transformCitations = (
  text: string,
  sources: Source[],
): React.ReactNode => {
  // Regex pour trouver [Source X] ou [Source X: Description]
  const citationRegex = /\[Source (\d+)(?::[^\]]+)?\]/g;

  const parts: React.ReactNode[] = [];
  const matches = Array.from(text.matchAll(citationRegex));
  let lastIndex = 0;

  matches.forEach((match, index) => {
    // Ajouter le texte avant la citation
    if (match.index !== undefined && match.index > lastIndex) {
      parts.push(text.substring(lastIndex, match.index));
    }

    // Ajouter le composant Citation
    const sourceNumber = Number.parseInt(match[1], 10);
    parts.push(
      <CitationLink
        key={`citation-${index}-${sourceNumber}`}
        sourceNumber={sourceNumber}
        sources={sources}
      />,
    );

    if (match.index !== undefined) {
      lastIndex = match.index + match[0].length;
    }
  });

  // Ajouter le texte restant
  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }

  return <>{parts}</>;
};

/**
 * Composant pour rendre du texte avec des citations transformées
 */
export const TextWithCitations: React.FC<{
  text: string;
  sources: Source[];
  className?: string;
}> = ({ text, sources, className }) => {
  const parts = transformCitations(text, sources);

  return <span className={className}>{parts}</span>;
};
