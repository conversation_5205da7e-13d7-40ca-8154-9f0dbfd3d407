'use client';

import React, { useEffect, useRef, useState, memo } from 'react';

interface ETFHeatmapProps {
  className?: string;
}

function ETFHeatmap({ className = '' }: ETFHeatmapProps) {
  const container = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!container.current) return;

    const currentContainer = container.current;

    const loadScript = () => {
      try {
        const script = document.createElement('script');
        script.src =
          'https://s3.tradingview.com/external-embedding/embed-widget-etf-heatmap.js';
        script.type = 'text/javascript';
        script.async = true;

        script.onload = () => {
          setIsLoading(false);
          setError(null);
        };

        script.onerror = () => {
          setError('Error loading TradingView widget. Please try again.');
          setIsLoading(false);
        };

        script.innerHTML = JSON.stringify({
          dataSource: 'AllUSEtf',
          blockSize: 'volume',
          blockColor: 'change',
          grouping: 'asset_class',
          locale: 'en',
          symbolUrl: '',
          colorTheme: 'light',
          hasTopBar: false,
          isDataSetEnabled: false,
          isZoomEnabled: true,
          hasSymbolTooltip: true,
          isMonoSize: false,
          width: '100%',
          height: '100%',
        });

        if (currentContainer) {
          currentContainer.innerHTML = '';
          currentContainer.appendChild(script);
        }
      } catch (err) {
        setError('Error initializing the widget.');
        setIsLoading(false);
      }
    };

    // Delay to avoid loading issues
    const timer = setTimeout(loadScript, 300);

    return () => {
      clearTimeout(timer);
      if (currentContainer) {
        currentContainer.innerHTML = '';
      }
    };
  }, []);

  if (error) {
    return (
      <div
        className={`p-4 bg-red-50 border border-red-200 rounded-md ${className}`}
      >
        <p className="text-red-600">{error}</p>
        <button
          type="button"
          onClick={() => window.location.reload()}
          className="mt-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className={`w-full h-[600px] ${className}`}>
      {isLoading && (
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
        </div>
      )}
      <div
        className="tradingview-widget-container"
        ref={container}
        style={{
          height: '100%',
          width: '100%',
          display: isLoading ? 'none' : 'block',
        }}
      >
        <div
          className="tradingview-widget-container__widget"
          style={{
            height: 'calc(100% - 32px)',
            width: '100%',
            minHeight: '500px',
          }}
        />
        <div className="tradingview-widget-copyright">
          <a
            href="https://www.tradingview.com/"
            rel="noopener nofollow"
            target="_blank"
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            Track all markets on TradingView
          </a>
        </div>
      </div>
    </div>
  );
}

export default memo(ETFHeatmap);
