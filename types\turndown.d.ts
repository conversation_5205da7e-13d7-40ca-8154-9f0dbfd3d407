declare module 'turndown' {
  export interface TurndownOptions {
    headingStyle?: 'setext' | 'atx';
    hr?: string;
    bulletListMarker?: '-' | '+' | '*';
    codeBlockStyle?: 'indented' | 'fenced';
    emDelimiter?: '_' | '*';
    strongDelimiter?: '_' | '*';
    linkStyle?: 'inlined' | 'referenced';
    linkReferenceStyle?: 'full' | 'collapsed' | 'shortcut';
  }

  export default class TurndownService {
    constructor(options?: TurndownOptions);
    turndown(input: string | Node): string;
    addRule(key: string, rule: any): this;
    remove(key: string): this;
    keep(filter: string | string[] | ((node: Node) => boolean)): this;
    escape?: (s: string) => string;
  }
}
