'use client';
/* eslint-disable tailwindcss/no-contradicting-classname */

import type React from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

interface BorderBeamProps {
  /**
   * The size of the border beam.
   */
  size?: number;
  /**
   * The duration of the border beam.
   */
  duration?: number;
  /**
   * The delay of the border beam.
   */
  delay?: number;
  /**
   * The color of the border beam from.
   */
  colorFrom?: string;
  /**
   * The color of the border beam to.
   */
  colorTo?: string;
  /**
   * The motion transition of the border beam.
   */
  transition?: any;
  /**
   * The class name of the border beam.
   */
  className?: string;
  /**
   * The style of the border beam.
   */
  style?: React.CSSProperties;
  /**
   * Whether to reverse the animation direction.
   */
  reverse?: boolean;
  /**
   * Color variant for different themes.
   */
  variant?: 'blue' | 'green' | 'red';
}

export const BorderBeam = ({
  className,
  size = 200,
  duration = 6,
  delay = 0,
  colorFrom = '#ffaa40',
  colorTo = '#9c40ff',
  transition,
  style,
  reverse = false,
  variant = 'red',
}: BorderBeamProps) => {
  // Define colors based on variant
  const colors = {
    blue: {
      gradient:
        'bg-[conic-gradient(from_0deg,transparent,transparent,#3b82f6,#3b82f6,#3b82f6,#3b82f6,#3b82f6,#3b82f6,transparent,transparent)]',
    },
    green: {
      gradient:
        'bg-[conic-gradient(from_0deg,transparent,transparent,#22c55e,#22c55e,#22c55e,#22c55e,#22c55e,#22c55e,transparent,transparent)]',
    },
    red: {
      gradient:
        'bg-[conic-gradient(from_0deg,transparent,transparent,#ef4444,#ef4444,#ef4444,#ef4444,#ef4444,#ef4444,transparent,transparent)]',
    },
  };

  const colorConfig = colors[variant] || colors.red;

  return (
    <div className="pointer-events-none absolute inset-0 rounded-[inherit] [border:1px_solid_transparent] ![mask-clip:padding-box,border-box] ![mask-composite:intersect] [mask:linear-gradient(transparent,transparent),linear-gradient(white,white)]">
      <motion.div
        className={cn(
          'absolute aspect-square rounded-full border-2 border-transparent opacity-35 blur-[0.5px]',
          colorConfig.gradient,
          className,
        )}
        style={{
          width: size,
          height: size,
          offsetPath: `rect(0 auto auto 0 round ${size}px)`,
          ...style,
        }}
        animate={{
          offsetDistance: reverse ? ['100%', '0%'] : ['0%', '100%'],
        }}
        transition={{
          repeat: Number.POSITIVE_INFINITY,
          duration,
          ease: 'linear',
          delay,
          ...transition,
        }}
      />
    </div>
  );
};
