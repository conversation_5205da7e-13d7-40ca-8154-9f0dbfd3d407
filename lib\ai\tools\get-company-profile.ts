import { z } from 'zod';
import { tool } from 'ai';

// Define and export the tool configuration
export const getCompanyProfile = tool({
  description:
    'Display comprehensive company profile information including business description, key statistics, financial metrics, sector information, and company details. Supports major US stocks (just use ticker like AAPL, MSFT, TSLA).',
  inputSchema: z.object({
    symbol: z
      .string()
      .describe(
        'The trading symbol to display company profile for. For US stocks, just use the ticker (e.g., AAPL, MSFT, TSLA, GOOGL). Exchange prefix will be added automatically for US stocks.',
      ),
  }),
  execute: async ({ symbol }: { symbol: string }) => {
    // Normalize symbol format for TradingView
    let normalizedSymbol = symbol.toUpperCase();
    
    // If symbol doesn't contain exchange prefix, try to add appropriate one
    if (!normalizedSymbol.includes(':')) {
      // Common US stocks - try NASDAQ first, then NYSE
      const nasdaqStocks = [
        'AAPL',
        'MSFT',
        'GOOGL',
        'GOOG',
        'AMZN',
        'TSLA',
        'META',
        'NVDA',
        'NFLX',
        'ADBE',
      ];
      const nyseStocks = [
        'JPM',
        'JNJ',
        'WMT',
        'PG',
        'UNH',
        'V',
        'HD',
        'MA',
        'DIS',
        'KO',
      ];
      
      if (nasdaqStocks.includes(normalizedSymbol)) {
        normalizedSymbol = `NASDAQ:${normalizedSymbol}`;
      } else if (nyseStocks.includes(normalizedSymbol)) {
        normalizedSymbol = `NYSE:${normalizedSymbol}`;
      } else {
        // Default to NASDAQ for unknown US stocks
        normalizedSymbol = `NASDAQ:${normalizedSymbol}`;
      }
    }
    
    // Return a simple object that will be used by the frontend to render the component
    return {
      type: 'company_profile',
      data: {
        symbol: normalizedSymbol,
      },
    };
  },
});
