'use client';

import { useEffect, useRef } from 'react';
import { artifactDefinitions } from './artifact';
import { initialArtifactData, useArtifact } from '@/hooks/use-artifact';
import { useDataStream } from './data-stream-provider';
import type { ArtifactKind } from './artifact';

export function DataStreamHandler() {
  const { dataStream } = useDataStream();
  const { artifact, setArtifact, setMetadata } = useArtifact();
  const lastProcessedIndex = useRef(-1);

  // Refs to track state without causing re-renders
  const hasFinishedRef = useRef(false);
  const artifactRef = useRef(artifact);
  const lastDocumentIdRef = useRef<string | null>(null);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  // Track last processed HTML delta to avoid duplicate rendering (e.g., duplicate charts)
  const lastHtmlContentRef = useRef<string | null>(null);

  // Keep refs in sync with current state
  useEffect(() => {
    artifactRef.current = artifact;
  }, [artifact]); // Update when artifact changes

  useEffect(() => {
    if (!dataStream?.length) return;

    const newDeltas = dataStream.slice(lastProcessedIndex.current + 1);
    if (newDeltas.length === 0) return;

    // If we've finished the current document, only skip when there are no new deltas.
    if (
      hasFinishedRef.current &&
      artifactRef.current.status === 'idle' &&
      lastDocumentIdRef.current === artifactRef.current.documentId &&
      artifactRef.current.documentId !== 'init'
    ) {
      console.log(
        'DataStreamHandler - Finished previously but received new deltas, processing anyway:',
        newDeltas.length,
      );
    }

    // Log large bursts but do not drop them; rendering will be batched below
    if (newDeltas.length > 50) {
      console.warn(
        'DataStreamHandler - Large delta burst detected:',
        newDeltas.length,
      );
    }

    lastProcessedIndex.current = dataStream.length - 1;

    let finishReceived = false;

    // Traitement par lots pour améliorer les performances
    const criticalDeltas: any[] = [];
    const contentDeltas: any[] = [];

    (newDeltas as any[]).forEach((delta) => {
      if (
        delta.type === 'clear' ||
        delta.type === 'data-clear' ||
        delta.type === 'id' ||
        delta.type === 'data-id' ||
        delta.type === 'title' ||
        delta.type === 'data-title' ||
        delta.type === 'kind' ||
        delta.type === 'data-kind'
      ) {
        criticalDeltas.push(delta);
      } else if (
        delta.type === 'finish' ||
        delta.type === 'completion' ||
        delta.type === 'data-finish'
      ) {
        finishReceived = true;
      } else {
        contentDeltas.push(delta);
      }
    });

    // Traiter d'abord les deltas critiques en une seule mise à jour
    if (criticalDeltas.length > 0) {
      setArtifact((draftArtifact) => {
        const base = draftArtifact || { ...initialArtifactData };
        let updated = { ...base };

        criticalDeltas.forEach((delta) => {
          switch (delta.type) {
            case 'clear':
            case 'data-clear':
              updated = { ...updated, content: '', status: 'streaming' };
              // Reset HTML cache when clearing the document content
              lastHtmlContentRef.current = null;
              break;
            case 'id':
            case 'data-id':
              updated = {
                ...updated,
                // Switch to the new document immediately
                documentId: ((delta as any).content ??
                  (delta as any).data) as string,
                // Clear old content proactively to avoid showing previous doc
                content: '',
                // Make sure the artifact panel is visible for the new doc
                isVisible: true,
                status: 'streaming',
              };
              // Mettre à jour la référence de l'ID du document
              lastDocumentIdRef.current = ((delta as any).content ??
                (delta as any).data) as string;
              // Réinitialiser l'état de fin lorsqu'un nouvel ID est reçu
              hasFinishedRef.current = false;
              // Reset last HTML content when a new document starts streaming
              lastHtmlContentRef.current = null;
              break;
            case 'title':
            case 'data-title':
              updated = {
                ...updated,
                title: ((delta as any).content ??
                  (delta as any).data) as string,
                status: 'streaming',
              };
              break;
            case 'kind':
            case 'data-kind':
              updated = {
                ...updated,
                kind: ((delta as any).content ??
                  (delta as any).data) as ArtifactKind,
                // Clear proactively on new kind to prep the panel for new content
                content: '',
                isVisible: true,
                status: 'streaming',
              };
              break;
          }
        });

        return updated;
      });
    }

    // Traiter ensuite les deltas de contenu
    if (contentDeltas.length > 0) {
      // Resolve the effective kind for this batch: prefer any incoming 'kind' delta
      let effectiveKind = artifact.kind;
      const incomingKindDelta = criticalDeltas.find(
        (d) => d.type === 'kind' || d.type === 'data-kind',
      );
      if (incomingKindDelta) {
        const payload =
          (incomingKindDelta as any).content ?? (incomingKindDelta as any).data;
        effectiveKind = payload as ArtifactKind;
      }

      const artifactDefinition = artifactDefinitions.find(
        (def) => def.kind === effectiveKind,
      );

      if (artifactDefinition?.onStreamPart) {
        contentDeltas.forEach((delta) => {
          const payload = (delta as any).content ?? (delta as any).data;
          // Skip duplicate html-delta contents that are identical to the last processed one
          if (
            delta.type === 'html-delta' &&
            typeof payload === 'string' &&
            lastHtmlContentRef.current === (payload as string)
          ) {
            return;
          }

          artifactDefinition.onStreamPart({
            streamPart: delta,
            setArtifact,
            setMetadata,
          });

          if (delta.type === 'html-delta' && typeof payload === 'string') {
            lastHtmlContentRef.current = payload as string;
          }
        });
      }
    }

    if (finishReceived) {
      const currentDocId = artifactRef.current.documentId;
      console.log(
        'DataStreamHandler - Finish/Completion signal received for document:',
        currentDocId,
      );

      // Mark that we've received a finish signal
      hasFinishedRef.current = true;

      // Update artifact status
      setArtifact((draftArtifact) =>
        draftArtifact
          ? { ...draftArtifact, status: 'idle', isVisible: true }
          : initialArtifactData,
      );

      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set new timeout with cleanup
      timeoutRef.current = setTimeout(() => {
        console.log('DataStreamHandler - Workflow completion finalized');
      }, 1000);
    }

    // Cleanup function to clear timeout on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [dataStream, setArtifact, setMetadata, artifact.kind]); // Include all used dependencies

  return null;
}
