'use client';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';
import type { PlaceImage } from '@/lib/types';

interface ImageModalProps {
  images: PlaceImage[];
  currentIndex: number;
  isOpen: boolean;
  onClose: () => void;
  onPrevious: () => void;
  onNext: () => void;
  onImageSelect?: (index: number) => void;
  placeName: string;
}

const ImageModal: React.FC<ImageModalProps> = ({
  images,
  currentIndex,
  isOpen,
  onClose,
  onPrevious,
  onNext,
  onImageSelect,
  placeName,
}) => {
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());
  // Gérer les touches du clavier
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          onPrevious();
          break;
        case 'ArrowRight':
          onNext();
          break;
      }
    },
    [isOpen, onClose, onPrevious, onNext],
  );

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Empêcher le scroll du body
      document.body.style.overflow = 'hidden';
      // Indiquer globalement qu'un modal image est ouvert (utile pour masquer des boutons flottants)
      document.body.classList.add('image-modal-open');
    } else {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
      document.body.classList.remove('image-modal-open');
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
      document.body.classList.remove('image-modal-open');
    };
  }, [isOpen, handleKeyDown]);

  // Fonction pour gérer les erreurs de chargement d'images
  const handleImageError = useCallback((index: number) => {
    setImageErrors((prev) => new Set(prev).add(index));
  }, []);

  // Réinitialiser les erreurs quand le modal s'ouvre
  useEffect(() => {
    if (isOpen) {
      setImageErrors(new Set());
    }
  }, [isOpen]);

  if (!isOpen || !images[currentIndex]) return null;

  const currentImage = images[currentIndex];
  const hasError = imageErrors.has(currentIndex);

  // Utiliser l'URL de la miniature si l'image principale a une erreur
  const imageUrl = hasError
    ? currentImage.thumbnail || currentImage.url
    : currentImage.url;

  return (
    <div
      className="fixed inset-0 z-[2147483647] bg-black/90 flex items-center justify-center"
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="image-modal-title"
    >
      {/* Bouton de fermeture */}
      <button
        type="button"
        onClick={onClose}
        className="absolute top-4 right-4 z-10 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
        aria-label="Fermer"
      >
        <X size={24} />
      </button>

      {/* Boutons de navigation */}
      {images.length > 1 && (
        <>
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onPrevious();
            }}
            className="absolute left-4 top-1/2 -translate-y-1/2 z-10 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
            aria-label="Image précédente"
          >
            <ChevronLeft size={24} />
          </button>

          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onNext();
            }}
            className="absolute right-4 top-1/2 -translate-y-1/2 z-10 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
            aria-label="Image suivante"
          >
            <ChevronRight size={24} />
          </button>
        </>
      )}

      {/* Conteneur de l'image */}
      <div
        className="relative w-full h-full flex flex-col items-center justify-center p-4"
        onClick={(e) => e.stopPropagation()}
        role="img"
        aria-label={`Image de ${placeName}`}
      >
        {/* Image principale avec taille uniforme */}
        <div className="relative w-full max-w-4xl h-[60vh] bg-black/20 rounded-lg overflow-hidden shadow-2xl">
          <Image
            src={imageUrl}
            alt={currentImage.description || placeName}
            fill
            className="object-contain"
            priority
            onError={() => handleImageError(currentIndex)}
          />
          {hasError && (
            <div className="absolute top-2 left-2 bg-yellow-600/80 text-white text-xs px-2 py-1 rounded">
              Image de qualité réduite
            </div>
          )}
        </div>

        {/* Informations sur l'image */}
        <div className="mt-6 text-center text-white">
          <h3 id="image-modal-title" className="text-xl font-semibold mb-2">
            {placeName}
          </h3>
          {currentImage.description && (
            <p className="text-sm text-gray-300 mb-3">
              {currentImage.description}
            </p>
          )}
          {images.length > 1 && (
            <p className="text-sm text-gray-400 mb-4">
              {currentIndex + 1} / {images.length}
            </p>
          )}
        </div>

        {/* Miniatures plus visibles si plusieurs images */}
        {images.length > 1 && (
          <div className="mt-2 w-full max-w-4xl">
            <div className="flex gap-3 justify-center overflow-x-auto pb-4 px-4">
              {images.map((image, index) => {
                const thumbnailHasError = imageErrors.has(index);
                const thumbnailUrl = image.thumbnail || image.url;

                return (
                  <button
                    key={`thumbnail-${index}-${image.url}`}
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      onImageSelect?.(index);
                    }}
                    className={`flex-shrink-0 relative w-20 h-16 rounded-lg border-3 overflow-hidden transition-all duration-200 ${
                      index === currentIndex
                        ? 'border-white scale-110 shadow-lg'
                        : 'border-gray-400 hover:border-gray-200 hover:scale-105'
                    }`}
                  >
                    <Image
                      src={thumbnailUrl}
                      alt={`Miniature ${index + 1}`}
                      fill
                      className="object-cover"
                      onError={() => handleImageError(index)}
                    />
                    {/* Overlay pour l'image sélectionnée */}
                    {index === currentIndex && (
                      <div className="absolute inset-0 bg-white/20 border-2 border-white rounded-lg" />
                    )}
                    {/* Indicateur d'erreur pour les miniatures */}
                    {thumbnailHasError && (
                      <div className="absolute top-0 right-0 w-3 h-3 bg-yellow-500 rounded-full border border-white" />
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageModal;
