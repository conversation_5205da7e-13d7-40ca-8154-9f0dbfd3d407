/* Carousel responsive constraints to prevent layout expansion */
.place-cards-carousel-container {
  contain: layout style !important;
  isolation: isolate !important;
  max-width: 100% !important;
  width: 100% !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  display: block;
  visibility: visible;
}

.place-cards-carousel-scroll {
  contain: layout style !important;
  max-width: 100% !important;
  width: 100% !important;
  overflow-x: auto !important;
  overflow-y: hidden !important;
  box-sizing: border-box !important;
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: 100%;
  visibility: visible;
  min-height: 260px !important;
  /* Force scrollbar to stay within bounds */
  scrollbar-gutter: stable;
}

.place-cards-carousel-item {
  contain: inline-size layout style !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  display: block;
  visibility: visible;
  /* Allow height expansion while constraining width */
  height: auto !important;
  min-height: fit-content !important;
}

.place-card {
  contain: inline-size layout style !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  overflow: visible !important;
  word-wrap: break-word;
  overflow-wrap: break-word;
  display: block;
  visibility: visible;
  /* Allow height expansion */
  height: auto !important;
  min-height: fit-content !important;
}

/* Hours section positioning */
.place-card .mt-4 {
  position: relative;
  z-index: 10;
}

/* Hours dropdown - normal flow positioning */
.place-card .mt-2.border {
  position: relative;
  z-index: 20;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.place-card * {
  max-width: 100% !important;
  box-sizing: border-box !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

.place-card img {
  max-width: 100% !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  display: block !important;
}

/* Fix image container sizing */
.place-card .relative.w-16.h-16 {
  width: 4rem !important;
  height: 4rem !important;
  flex-shrink: 0 !important;
}

.place-card .relative.w-14.h-14 {
  width: 3.5rem !important;
  height: 3.5rem !important;
  flex-shrink: 0 !important;
}

/* Responsive breakpoints for carousel */
@media (max-width: 640px) {
  .place-cards-carousel-item {
    min-width: calc(100% - 1rem);
    max-width: calc(100% - 1rem);
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .place-cards-carousel-item {
    min-width: calc(100% - 1rem);
    max-width: calc(100% - 1rem);
  }
}

@media (min-width: 769px) {
  .place-cards-carousel-item {
    min-width: 100%;
    max-width: 100%;
  }
}

/* Debug: Ensure carousel is visible */
.place-cards-carousel-container,
.place-cards-carousel-scroll,
.place-cards-carousel-item {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

.place-cards-carousel-scroll {
  display: flex !important;
}
