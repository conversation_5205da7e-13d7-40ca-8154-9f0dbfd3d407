import { Artifact } from '@/components/create-artifact';
import {
  CopyIcon,
  SparklesIcon,
  CodeIcon,
  ImageIcon,
  FullscreenIcon,
  RouteIcon,
  BoxIcon,
} from '@/components/icons';
import { toast } from 'sonner';
import {
  SandpackPreview,
  SandpackProvider,
} from '@codesandbox/sandpack-react/unstyled';

interface Metadata {
  htmlContent: string;
  cssContent: string;
  jsContent: string;
}

export const htmlArtifact = new Artifact<'html', Metadata>({
  kind: 'html',
  description:
    'Useful for creating interactive web applications and visual presentations',
  initialize: async ({ setMetadata }) => {
    setMetadata({
      htmlContent: '<div id="app"></div>',
      cssContent: 'body { font-family: system-ui; }',
      jsContent: 'document.getElementById("app").innerHTML = "Hello World";',
    });
  },
  onStreamPart: ({ streamPart, setArtifact, setMetadata }) => {
    if (streamPart.type === 'data-htmlDelta') {
      const content = streamPart.data as string;
      try {
        // Vérifier si le contenu est déjà un objet
        let parsed: {
          htmlContent?: string;
          cssContent?: string;
          jsContent?: string;
        } = {};
        if (typeof content === 'object') {
          parsed = content as {
            htmlContent?: string;
            cssContent?: string;
            jsContent?: string;
          };
        } else {
          // Essayer de nettoyer le contenu avant de le parser
          let cleanContent = content;

          // Supprimer les backticks markdown s'ils existent
          if (
            cleanContent.startsWith('```json') ||
            cleanContent.startsWith('```')
          ) {
            cleanContent = cleanContent
              .replace(/^```(json)?\n/, '')
              .replace(/\n```$/, '');
          }

          // Essayer de trouver un objet JSON valide dans le texte
          const jsonMatch = cleanContent.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            cleanContent = jsonMatch[0];
          }

          parsed = JSON.parse(cleanContent);
        }

        // Vérifier si l'objet a les propriétés requises
        const validatedContent = {
          htmlContent:
            parsed.htmlContent || '<div id="app">Loading content...</div>',
          cssContent: parsed.cssContent || 'body { font-family: system-ui; }',
          jsContent: parsed.jsContent || 'console.log("HTML artifact loaded");',
        };

        console.log('HTML delta received:', validatedContent);

        setMetadata(validatedContent);
        setArtifact((draftArtifact) => ({
          ...draftArtifact,
          content: JSON.stringify(validatedContent),
          // Ne pas rendre visible pendant le streaming, sauf si c'est le premier rendu
          isVisible:
            draftArtifact.isVisible || draftArtifact.status !== 'streaming',
          status: 'streaming',
        }));
      } catch (e) {
        console.error('Failed to parse HTML artifact content', e, content);
        // Essayer de créer un contenu à partir du texte brut si c'est du HTML
        let htmlContent = '';
        if (
          typeof content === 'string' &&
          content.includes('<') &&
          content.includes('>')
        ) {
          // Si le contenu ressemble à du HTML, l'utiliser directement
          htmlContent = content;
        } else {
          htmlContent =
            '<div id="app">An error occurred while loading content. The model did not return valid JSON.</div>';
        }

        // Fournir un contenu par défaut en cas d'erreur
        const defaultContent = {
          htmlContent,
          cssContent: 'body { font-family: system-ui; color: #ff0000; }',
          jsContent: 'console.error("Error loading HTML artifact");',
        };

        setMetadata(defaultContent);
        setArtifact((draftArtifact) => ({
          ...draftArtifact,
          content: JSON.stringify(defaultContent),
          isVisible: true,
          status: 'streaming',
        }));
      }
    }
  },
  content: ({ metadata }) => {
    const parsedContent = metadata || {
      htmlContent: '<div id="app"></div>',
      cssContent: '',
      jsContent: '',
    };

    return (
      <div className="flex flex-col h-full">
        <div
          className="absolute w-full h-full"
          style={{
            height: 'calc(100vh - 100px)',
            top: '60px', // Descendre le cadre de 60px pour laisser de l'espace en haut
            left: 0,
            right: 0,
            bottom: 0,
          }}
        >
          <style jsx global>{`
              .custom-wrapper, .custom-layout, .custom-preview-container {
                height: 100% !important;
                width: 100% !important;
                max-width: 100% !important;
                max-height: 100% !important;
                border: none !important;
                margin: 0 !important;
                padding: 0 !important;
                overflow: hidden !important;
              }
              .sp-preview-iframe {
                height: 100% !important;
                width: 100% !important;
                max-width: 100% !important;
                max-height: 100% !important;
                border: none !important;
              }
            `}</style>
          <SandpackProvider
            template="static"
            files={{
              '/index.html': `
                  <!DOCTYPE html>
                  <html>
                    <head>
                      <meta charset="UTF-8">
                      <meta name="viewport" content="width=device-width, initial-scale=1.0">
                      <title>Interactive Presentation</title>
                      <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
                      <!-- Tailwind CSS via CDN with integrity hash -->
                      <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
                      <!-- Custom Tailwind-like utility classes -->
                      <style>
                        /* Primary colors */
                        .bg-primary-50 { background-color: #f0f9ff; }
                        .bg-primary-100 { background-color: #e0f2fe; }
                        .bg-primary-200 { background-color: #bae6fd; }
                        .bg-primary-300 { background-color: #7dd3fc; }
                        .bg-primary-400 { background-color: #38bdf8; }
                        .bg-primary-500 { background-color: #0ea5e9; }
                        .bg-primary-600 { background-color: #0284c7; }
                        .bg-primary-700 { background-color: #0369a1; }
                        .bg-primary-800 { background-color: #075985; }
                        .bg-primary-900 { background-color: #0c4a6e; }

                        .text-primary-50 { color: #f0f9ff; }
                        .text-primary-100 { color: #e0f2fe; }
                        .text-primary-200 { color: #bae6fd; }
                        .text-primary-300 { color: #7dd3fc; }
                        .text-primary-400 { color: #38bdf8; }
                        .text-primary-500 { color: #0ea5e9; }
                        .text-primary-600 { color: #0284c7; }
                        .text-primary-700 { color: #0369a1; }
                        .text-primary-800 { color: #075985; }
                        .text-primary-900 { color: #0c4a6e; }

                        /* Font family */
                        .font-sans { font-family: 'Inter', sans-serif; }
                      </style>
                      <style>
                        /* Simple Layout Styles - Clean Design */
                        ${parsedContent.cssContent}
                      </style>
                    </head>
                    <body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 antialiased">
                      ${parsedContent.htmlContent}
                      <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                      <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
                      <script>
                        // Script pour vérifier si l'image de destination est présente
                        window.addEventListener('load', function() {
                          console.log('Window loaded, checking for hero images');
                          const heroImages = document.querySelectorAll('.hero-image, .destination-image, [style*="background-image"]');
                          console.log('Found hero images:', heroImages.length);
                        });

                        // Fonction pour initialiser une seule carte OpenStreetMap avec tous les points d'intérêt
                        document.addEventListener('DOMContentLoaded', function() {
                          console.log('DOM loaded, initializing map');
                          // Vérifier si Leaflet est chargé
                          console.log('Leaflet available:', typeof L !== 'undefined');

                          if (typeof L !== 'undefined') {
                            // Trouver le conteneur principal de la carte
                            const mainMapContainer = document.getElementById('main-map') || document.querySelector('[id$="-map"]');
                            console.log('Map container found:', mainMapContainer ? mainMapContainer.id : 'none');

                            if (mainMapContainer) {
                              // Coordonnées par défaut pour différentes villes
                              const defaultLocations = {
                                'paris': [48.8566, 2.3522],
                                'rome': [41.9028, 12.4964],
                                'london': [51.5074, -0.1278],
                                'new york': [40.7128, -74.0060],
                                'tokyo': [35.6762, 139.6503],
                                'barcelona': [41.3851, 2.1734],
                                'amsterdam': [52.3676, 4.9041],
                                'berlin': [52.5200, 13.4050],
                                'venice': [45.4408, 12.3155],
                                'florence': [43.7696, 11.2558],
                                'madrid': [40.4168, -3.7038],
                                'monaco': [43.7384, 7.4246],
                                'vienna': [48.2082, 16.3738],
                                'prague': [50.0755, 14.4378],
                                'budapest': [47.4979, 19.0402],
                                'dubai': [25.2048, 55.2708],
                                'singapore': [1.3521, 103.8198],
                                'sydney': [33.8688, 151.2093],
                                'rio': [-22.9068, -43.1729],
                                'cairo': [30.0444, 31.2357],
                                'istanbul': [41.0082, 28.9784],
                                'athens': [37.9838, 23.7275],
                                'santorini': [36.3932, 25.4615],
                                'kyoto': [35.0116, 135.7681],
                                'bangkok': [13.7563, 100.5018],
                                'bali': [-8.3405, 115.0920],
                                'marrakech': [31.6295, -7.9811],
                                'cape town': [-33.9249, 18.4241],
                                'mexico city': [19.4326, -99.1332],
                                'havana': [23.1136, -82.3666]
                              };

                              // Obtenir le nom de la destination principale
                              const mainDestinationAttr = mainMapContainer.getAttribute('data-destination') || '';
                              const mainDestination = mainDestinationAttr.toLowerCase();

                              // Trouver les coordonnées correspondantes ou utiliser Rome par défaut
                              let mainCoords = [41.9028, 12.4964]; // Rome par défaut

                              // Chercher une correspondance partielle dans les clés
                              for (const [city, location] of Object.entries(defaultLocations)) {
                                if (mainDestination.includes(city) || city.includes(mainDestination)) {
                                  mainCoords = location;
                                  break;
                                }
                              }

                              // Créer la carte principale
                              const map = L.map(mainMapContainer).setView(mainCoords, 13);

                              // Ajouter la couche OpenStreetMap
                              L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                                maxZoom: 19
                              }).addTo(map);

                              // Ajouter un marqueur pour la destination principale
                              const mainMarker = L.marker(mainCoords)
                                .addTo(map)
                                .bindPopup('<strong>' + (mainDestinationAttr || 'Destination') + '</strong>')
                                .openPopup();

                              // Collecter tous les points d'intérêt
                              const pointsOfInterest = [];

                              // Ajouter le point principal
                              pointsOfInterest.push({
                                coords: mainCoords,
                                name: mainDestinationAttr || 'Destination principale',
                                day: 'Principal'
                              });

                              // Rechercher tous les éléments avec l'attribut data-poi
                              const poiElements = document.querySelectorAll('[data-poi]');
                              poiElements.forEach(function(element) {
                                const poiData = element.getAttribute('data-poi') || '';
                                try {
                                  // Essayer de parser les données JSON
                                  const poi = JSON.parse(poiData);
                                  if (poi.name && poi.lat && poi.lng) {
                                    pointsOfInterest.push({
                                      coords: [parseFloat(poi.lat), parseFloat(poi.lng)],
                                      name: poi.name,
                                      day: poi.day || '',
                                      description: poi.description || ''
                                    });
                                  }
                                } catch (e) {
                                  // Si ce n'est pas du JSON valide, essayer de parser le format "name|lat|lng"
                                  const parts = poiData.split('|');
                                  if (parts.length >= 3) {
                                    const lat = parseFloat(parts[1]);
                                    const lng = parseFloat(parts[2]);
                                    if (!isNaN(lat) && !isNaN(lng)) {
                                      pointsOfInterest.push({
                                        coords: [lat, lng],
                                        name: parts[0],
                                        day: parts[3] || '',
                                        description: parts[4] || ''
                                      });
                                    }
                                  }
                                }
                              });

                              // Si aucun POI n'est trouvé, essayer de générer des POIs fictifs autour de la destination principale
                              if (pointsOfInterest.length <= 1) {
                                // Rechercher tous les éléments qui pourraient contenir des noms de lieux
                                const potentialPOIs = document.querySelectorAll('h3, h4, .attraction-name, .location-name, .place-name');

                                // Créer un ensemble pour éviter les doublons
                                const poiNames = new Set();

                                potentialPOIs.forEach(function(element, index) {
                                  const name = element.textContent.trim();
                                  if (name && name.length > 3 && !poiNames.has(name)) {
                                    poiNames.add(name);

                                    // Générer des coordonnées aléatoires autour de la destination principale
                                    const lat = mainCoords[0] + (Math.random() - 0.5) * 0.05;
                                    const lng = mainCoords[1] + (Math.random() - 0.5) * 0.05;

                                    // Déterminer le jour en fonction du contexte
                                    let day = '';
                                    let parent = element.parentElement;
                                    while (parent && !day) {
                                      if (parent.id && parent.id.includes('day')) {
                                        day = parent.id;
                                      } else if (parent.className && parent.className.includes('day')) {
                                        const dayMatch = parent.className.match(/day-?(\d+)/i);
                                        if (dayMatch) {
                                          day = 'Jour ' + dayMatch[1];
                                        }
                                      }
                                      parent = parent.parentElement;
                                    }

                                    // Limiter à 10 POIs maximum
                                    if (pointsOfInterest.length < 11) {
                                      pointsOfInterest.push({
                                        coords: [lat, lng],
                                        name: name,
                                        day: day || 'Jour ' + (Math.floor(index / 3) + 1),
                                        description: ''
                                      });
                                    }
                                  }
                                });
                              }

                              // Créer un groupe de marqueurs pour tous les points d'intérêt
                              const markers = L.featureGroup();

                              // Couleurs pour différents jours
                              const colors = {
                                'Jour 1': '#e74c3c',
                                'Jour 2': '#3498db',
                                'Jour 3': '#2ecc71',
                                'Jour 4': '#f39c12',
                                'Jour 5': '#9b59b6',
                                'Jour 6': '#1abc9c',
                                'Jour 7': '#d35400',
                                'Principal': '#34495e'
                              };

                              // Ajouter tous les marqueurs à la carte
                              pointsOfInterest.forEach(function(poi, index) {
                                // Déterminer la couleur en fonction du jour
                                let color = '#3388ff'; // Couleur par défaut
                                for (const [day, dayColor] of Object.entries(colors)) {
                                  if (poi.day && poi.day.includes(day)) {
                                    color = dayColor;
                                    break;
                                  }
                                }

                                // Créer une icône personnalisée avec la couleur du jour
                                const markerIcon = L.divIcon({
                                  html: '<div style="background-color:' + color + '; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 12px;">' + (index + 1) + '</div>',
                                  className: 'custom-div-icon',
                                  iconSize: [24, 24],
                                  iconAnchor: [12, 12]
                                });

                                // Créer le contenu du popup
                                let popupContent = '<div style="min-width: 200px;"><strong>' + poi.name + '</strong>';
                                if (poi.day) {
                                  popupContent += '<br><span style="color:' + color + ';">' + poi.day + '</span>';
                                }
                                if (poi.description) {
                                  popupContent += '<br><br>' + poi.description;
                                }
                                popupContent += '</div>';

                                // Ajouter le marqueur à la carte
                                const marker = L.marker(poi.coords, { icon: markerIcon })
                                  .bindPopup(popupContent)
                                  .addTo(map);

                                markers.addLayer(marker);
                              });

                              // Ajuster la vue pour montrer tous les marqueurs si plus d'un
                              if (pointsOfInterest.length > 1) {
                                map.fitBounds(markers.getBounds(), { padding: [30, 30] });
                              }

                              // Cacher les autres conteneurs de carte s'il y en a
                              const otherMapContainers = document.querySelectorAll('[id$="-map"]');
                              otherMapContainers.forEach(function(container) {
                                if (container !== mainMapContainer) {
                                  container.style.display = 'none';
                                }
                              });
                            }
                          } else {
                            // Fallback si Leaflet n'est pas chargé
                            const mapContainers = document.querySelectorAll('[id$="-map"]');
                            if (mapContainers.length > 0) {
                              // Afficher uniquement le premier conteneur
                              const mainContainer = mapContainers[0];
                              const destination = mainContainer.getAttribute('data-destination') || 'city map';
                              const fallbackImage = document.createElement('div');
                              fallbackImage.className = 'w-full h-full bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center flex-col p-4';
                              fallbackImage.innerHTML = '<div class="text-center"><p class="text-gray-500 dark:text-gray-300 mb-2">Map of ' + destination + '</p><p class="text-sm text-gray-400">(OpenStreetMap could not be loaded)</p></div>';
                              mainContainer.innerHTML = '';
                              mainContainer.appendChild(fallbackImage);

                              // Cacher les autres conteneurs
                              for (let i = 1; i < mapContainers.length; i++) {
                                mapContainers[i].style.display = 'none';
                              }
                            }
                          }
                        });
                      </script>

                      <script>
                        // Simple Layout System - Clean Design
                        // No complex JavaScript needed for simple layouts
                      </script>
                      <script>${parsedContent.jsContent}</script>
                    </body>
                  </html>
                `,
            }}
            options={{
              externalResources: [
                'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
                'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
                'https://cdn.jsdelivr.net/npm/chart.js',
                'https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js',
                'https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js',
                'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css',
                'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js',
              ],
              classes: {
                'sp-wrapper': 'custom-wrapper',
                'sp-layout': 'custom-layout',
                'sp-preview-container': 'custom-preview-container',
              },
            }}
          >
            <SandpackPreview
              showNavigator={false}
              showOpenInCodeSandbox={false}
              showRefreshButton={false}
              showRestartButton={false}
              showOpenNewtab={true}
              style={{ height: '100%', width: '100%', border: 'none' }}
              className="h-full w-full"
            />
          </SandpackProvider>
        </div>
      </div>
    );
  },
  actions: [
    {
      icon: <CopyIcon size={18} />,
      label: 'Copy',
      description: 'Copy HTML to clipboard',
      onClick: async ({ content }) => {
        try {
          // Vérifier si le contenu est une chaîne de caractères
          if (typeof content !== 'string') {
            toast.error('Invalid content format');
            return;
          }

          // Vérifier si le contenu ressemble à du JSON
          if (
            content.trim().startsWith('{') ||
            content.trim().startsWith('[')
          ) {
            try {
              const parsed = JSON.parse(content);

              // Vérifier si le contenu parsé a les propriétés attendues
              if (parsed && typeof parsed === 'object') {
                const htmlContent = parsed.htmlContent || '';
                const cssContent = parsed.cssContent || '';
                const jsContent = parsed.jsContent || '';

                const fullHtml = `
<!DOCTYPE html>
<html>
<head>
  <style>${cssContent}</style>
</head>
<body>
  ${htmlContent}
  <script>${jsContent}</script>
</body>
</html>`;
                await navigator.clipboard.writeText(fullHtml);
                toast.success('HTML copied to clipboard');
                return;
              }
            } catch (e) {
              console.error('Error parsing JSON content:', e);
              // Si le parsing échoue, continuer avec le contenu brut
            }
          }

          // Si ce n'est pas du JSON valide ou si le parsing a échoué, utiliser le contenu brut
          await navigator.clipboard.writeText(content);
          toast.success('Content copied to clipboard');
        } catch (e) {
          console.error('Error copying to clipboard:', e);
          toast.error('Failed to copy content');
        }
      },
    },
  ],
  toolbar: [
    {
      icon: <SparklesIcon size={16} />,
      description: 'Beautify Design',
      onClick: ({ sendMessage }) => {
        sendMessage({
          role: 'user',
          content: '',
          parts: [
            {
              type: 'text',
              text: 'Can you please improve the visual design with better colors, typography, and layout? Make it look more professional and visually appealing.',
            },
          ],
        });
      },
    },
    {
      icon: <CodeIcon size={16} />,
      description: 'Make Responsive',
      onClick: ({ sendMessage }) => {
        sendMessage({
          role: 'user',
          content: '',
          parts: [
            {
              type: 'text',
              text: 'Can you please make the HTML fully responsive for all devices including mobile, tablet, and desktop?',
            },
          ],
        });
      },
    },
    {
      icon: <ImageIcon size={16} />,
      description: 'Add Visuals',
      onClick: ({ sendMessage }) => {
        sendMessage({
          role: 'user',
          content: '',
          parts: [
            {
              type: 'text',
              text: 'Can you please enhance this with more visual elements like icons, illustrations, or charts where appropriate?',
            },
          ],
        });
      },
    },
    {
      icon: <RouteIcon size={16} />,
      description: 'Travel Format',
      onClick: ({ sendMessage }) => {
        sendMessage({
          role: 'user',
          content: '',
          parts: [
            {
              type: 'text',
              text: 'Can you please format this as a travel itinerary with a day-by-day timeline, maps, and visual highlights?',
            },
          ],
        });
      },
    },
    {
      icon: <BoxIcon size={16} />,
      description: 'Add Interactivity',
      onClick: ({ sendMessage }) => {
        sendMessage({
          role: 'user',
          content: '',
          parts: [
            {
              type: 'text',
              text: 'Can you please add more interactive elements like expandable sections, tooltips, or animations to make this more engaging?',
            },
          ],
        });
      },
    },
    {
      icon: <FullscreenIcon size={16} />,
      description: 'Presentation Mode',
      onClick: ({ sendMessage }) => {
        sendMessage({
          role: 'user',
          content: '',
          parts: [
            {
              type: 'text',
              text: 'Can you please optimize this for a full-screen presentation format with clear sections and visual hierarchy?',
            },
          ],
        });
      },
    },
  ],
});
