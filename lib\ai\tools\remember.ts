import { tool } from 'ai';
import { z } from 'zod';
import { enqueueMemory } from '@/lib/memoryBatcher';
import { auth } from '@/app/(auth)/auth';

/**
 * Outil pour faciliter la mémorisation d'informations personnelles
 * Cet outil est conçu pour être utilisé directement par le modèle pour stocker
 * des informations personnelles importantes partagées par l'utilisateur
 */
export const remember = tool({
  description: 'Store important personal information shared by the user',
  inputSchema: z.object({
    content: z.string().describe('The personal information to remember'),
    info_type: z
      .string()
      .optional()
      .describe(
        'The type of information (preference, contact, demographic, etc.)',
      ),
    info_category: z
      .string()
      .optional()
      .describe(
        'The category of information (name, email, language, hobby, etc.)',
      ),
  }),
  execute: async ({ content, info_type, info_category }) => {
    try {
      // Vérifier que le contenu est fourni
      if (!content || content.trim() === '') {
        return {
          success: false,
          message: 'Content is required to remember information',
        };
      }

      // Récupérer la session utilisateur
      const session = await auth();
      if (!session?.user?.id) {
        return {
          success: false,
          message: 'User authentication required',
        };
      }

      // Valider et définir les types et catégories d'informations
      const validInfoType = info_type || 'preference';
      const validInfoCategory = info_category || 'general';

      // Enregistrer l'information dans la mémoire
      enqueueMemory({
        data: content,
        userId: session.user.id,
        metadata: {
          timestamp: new Date().toISOString(),
          memory_type: 'personal_info',
          info_type: validInfoType,
          info_category: validInfoCategory,
          source: 'remember_tool',
          // Stocker les catégories dans les métadonnées
          categories_info: [
            'personal_info',
            validInfoType,
            validInfoCategory,
          ].join(','),
        },
        priority: true, // Priorité élevée pour les informations personnelles
      });

      return {
        success: true,
        message: 'Information personnelle mémorisée avec succès',
        info_type: validInfoType,
        info_category: validInfoCategory,
      };
    } catch (error) {
      console.error('Error in remember tool:', error);
      return {
        success: false,
        message: 'Failed to store information in memory',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});
