import { tool } from 'ai';
import { z } from 'zod';

export const getStockChart = tool({
  description: 'Get a stock market chart for a given ticker symbol',
  inputSchema: z.object({
    ticker: z
      .string()
      .describe(
        'Stock ticker symbol (e.g. "AAPL" for Apple, "MSFT" for Microsoft)',
      ),
  }),
  execute: async ({ ticker }) => {
    // Vérifier si le ticker est valide (format simple)
    if (!ticker || typeof ticker !== 'string' || ticker.trim() === '') {
      return { error: 'Veuillez fournir un symbole boursier valide' };
    }

    // Nettoyer le ticker (enlever les espaces, mettre en majuscules)
    const cleanTicker = ticker.trim().toUpperCase();

    // Vérifier si le ticker est dans une liste de symboles connus (optionnel)
    // Cette étape est optionnelle mais peut aider à valider le ticker
    const knownTickers = [
      'AAPL',
      'MSFT',
      'GOOGL',
      'AMZN',
      'META',
      'TSLA',
      'NVDA',
      'JPM',
      'V',
      'WMT',
    ];

    if (!knownTickers.includes(cleanTicker)) {
      // Ce n'est pas une erreur, on laisse passer quand même mais on note que le ticker n'est pas dans la liste connue
      console.log(
        `Avertissement : Le symbole ${cleanTicker} n'est pas dans la liste des symboles connus.`,
      );
    }

    // Retourner les données nécessaires pour afficher le graphique
    // Le composant front-end utilisera ces données pour afficher le graphique avec TradingView
    return {
      ticker: cleanTicker,
      chartUrl: `https://www.tradingview.com/chart/?symbol=${cleanTicker}`,
      timestamp: new Date().toISOString(),
    };
  },
});
