import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { generateAdvancedTripPlan } from '@/lib/ai/workflows/advanced-trip-planning';
import { createUIMessageStream, JsonToSseTransformStream } from 'ai';
import { saveDocument } from '@/lib/db/queries';
import { generateUUID } from '@/lib/utils';
import { ChatSDKError } from '@/lib/errors';

// Increase timeout for complex trip planning operations with comprehensive web search
export const maxDuration = 240; // 4 minutes (increased for comprehensive web search)

/**
 * API route handler for advanced trip planning
 */
export async function POST(req: NextRequest) {
  try {
    // Get the user session
    const session = await auth();
    if (!session?.user) {
      return new ChatSDKError('unauthorized:api').toResponse();
    }

    // Parse the request body
    const body = await req.json();
    const { query } = body;

    if (!query) {
      return new ChatSDKError(
        'bad_request:api',
        'Missing required parameter: query',
      ).toResponse();
    }

    // Generate a unique ID for the artifact
    const artifactId = generateUUID();

    // Create the artifact in the database
    await saveDocument({
      id: artifactId,
      userId: session.user.id,
      kind: 'html',
      title: `Advanced Trip Plan: ${query}`,
      content: '',
    });

    // Create a UI message stream (JSON) and return as Server-Sent Events
    const stream = createUIMessageStream({
      execute: async ({ writer: dataStream }) => {
        try {
          // Send initial data to the stream
          dataStream.write({ type: 'data-kind', data: 'html', transient: true });
          dataStream.write({ type: 'data-id', data: artifactId, transient: true });
          dataStream.write({
            type: 'data-title',
            data: `Advanced Trip Plan: ${query}`,
            transient: true,
          });
          dataStream.write({ type: 'data-clear', data: null, transient: true });

          // Generate the trip plan
          const result = await generateAdvancedTripPlan({
            query,
            dataStream,
          });

          // Update the artifact with the final content
          const finalContent = JSON.stringify({
            htmlContent: result.htmlContent,
            cssContent: result.cssContent,
            jsContent: result.jsContent,
          });

          // Update the document with the final content
          await saveDocument({
            id: artifactId,
            userId: session.user.id,
            kind: 'html',
            title: `Advanced Trip Plan: ${query}`,
            content: finalContent,
          });

          // Send explicit completion signal for clients
          dataStream.write({
            type: 'data-finish',
            data: null,
            transient: true,
          });

          console.log('Advanced trip planning completed successfully');
        } catch (error) {
          console.error('Error generating advanced trip plan:', error);
          throw error;
        }
      },
    });

    return new Response(stream.pipeThrough(new JsonToSseTransformStream()));
  } catch (error) {
    if (error instanceof ChatSDKError) {
      return error.toResponse();
    }
    console.error('Error in advanced trip planning API route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
