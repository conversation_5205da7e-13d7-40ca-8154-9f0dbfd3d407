"use client";

import { useEffect, useMemo, useRef, useState } from "react";
import type { MessageAnnotation } from "@/types/ai-extensions";

interface UseStreamingAnnotationsParams {
  toolCallId: string;
  enabled?: boolean;
}

interface UseStreamingAnnotationsResult {
  annotations: MessageAnnotation[];
  isLoading: boolean;
  error: string | null;
}

// Minimal client-only hook that can be wired to a real source later.
// For now, it exposes a stable API and allows components to render gracefully.
export function useStreamingAnnotations(
  { toolCallId, enabled = true }: UseStreamingAnnotationsParams,
): UseStreamingAnnotationsResult {
  const [annotations, setAnnotations] = useState<MessageAnnotation[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const startedRef = useRef(false);

  useEffect(() => {
    if (!enabled || !toolCallId) {
      setIsLoading(false);
      return;
    }

    if (startedRef.current) return;
    startedRef.current = true;

    // Placeholder: In a real implementation, subscribe to a stream (SSE/websocket)
    // keyed by toolCallId. For now, we end loading shortly and keep annotations empty.
    const timeout = setTimeout(() => {
      setIsLoading(false);
    }, 200);

    return () => {
      clearTimeout(timeout);
    };
  }, [enabled, toolCallId]);

  // Memo so consumers can avoid unnecessary recalculations
  const value = useMemo(
    () => ({ annotations, isLoading, error }),
    [annotations, isLoading, error],
  );

  return value;
}
