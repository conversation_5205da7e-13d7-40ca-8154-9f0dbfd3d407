import { z } from 'zod';
import {
  generateObject,
  type LanguageModel,
} from 'ai';

/**
 * Interface for user preferences
 */
export interface UserPreferences {
  travelStyle: string[];
  budget: {
    level: 'budget' | 'moderate' | 'luxury';
    currency: string;
    maxAmount?: number;
  };
  accommodation: {
    type: string[];
    preferences: string[];
  };
  dining: {
    cuisinePreferences: string[];
    dietaryRestrictions: string[];
    mealPreferences: string[];
  };
  activities: {
    interests: string[];
    activityLevel: 'relaxed' | 'moderate' | 'active' | 'very active';
    preferredActivities: string[];
    primaryActivityType?: string; // Main activity focus (e.g., "cycling", "hiking", "beach", "cultural")
    specificActivities: {
      name: string;
      importance: 'must-do' | 'preferred' | 'optional';
      details?: string;
    }[];
  };
  accessibility: {
    requirements: string[];
    mobilityIssues: boolean;
  };
  transportation: {
    preferredModes: string[];
    publicTransportPreference: boolean;
    specificTransportRequirements?: string[]; // For specific transport needs like "bike rental"
  };
  specialRequests: string[];
  travelWithChildren: boolean;
  travelWithPets: boolean;
  sustainabilityPreference: boolean;
  seasonalPreferences: string[];
  timeOfDayPreferences: {
    morningActivities: string[];
    afternoonActivities: string[];
    eveningActivities: string[];
  };
  tripPurpose: string; // The main purpose of the trip (e.g., "cycling vacation", "cultural exploration")
  visualTheme?: string; // Preferred visual theme for the itinerary
  specialInterests: string[]; // Special interests that should influence the entire trip
  mustIncludeKeywords: string[]; // Keywords that must be included in the itinerary
}

/**
 * PreferenceAgent is responsible for extracting user preferences from their query
 * and creating a detailed preference profile to guide the trip planning.
 */
export class PreferenceAgent {
  private model: LanguageModel;

  constructor(model: LanguageModel) {
    this.model = model;
  }

  /**
   * Extract user preferences from the query
   */
  async extractPreferences(query: string): Promise<UserPreferences> {
    try {
      console.log('Extracting user preferences from query:', query);

      // Extract preferences using the model
      const { object: preferences } = await generateObject({
        model: this.model,
        system: `You are an expert travel consultant who specializes in understanding travelers' preferences and needs.
        Analyze the user's query to extract detailed travel preferences.
        Pay special attention to specific activities, interests, or requirements mentioned in the query.
        If the user mentions a specific activity like cycling, hiking, beach activities, or cultural visits, make sure to highlight this as a primary focus.
        If certain preferences are not explicitly mentioned, make reasonable assumptions based on the context.
        Be comprehensive and consider all aspects of travel preferences.
        If the query is very minimal, provide balanced default preferences that would suit most travelers.

        IMPORTANT: You MUST detect and extract specific activities mentioned in the query, even if they are not the main focus of the trip.

        ACTIVITY DETECTION RULES:
        1. If the user mentions "vélo", "cycling", "bike", "bicycle", "faire du vélo", or any cycling-related terms, set "cycling" as the primaryActivityType and add it to specificActivities as a "must-do" activity.
        2. If the user mentions "shopping", "boutiques", "faire les boutiques", "magasins", or any shopping-related terms, add "shopping" to specificActivities as a "must-do" activity.
        3. If the user mentions "hiking", "randonnée", "marche", "trekking", or any hiking-related terms, set "hiking" as the primaryActivityType and add it to specificActivities as a "must-do" activity.
        4. If the user mentions "plage", "beach", "mer", "ocean", "swimming", or any beach-related terms, set "beach" as the primaryActivityType and add it to specificActivities as a "must-do" activity.
        5. If the user mentions "musée", "museum", "culture", "histoire", "history", "art", or any cultural terms, set "cultural" as the primaryActivityType and add it to specificActivities as a "must-do" activity.
        6. If the user mentions "food", "cuisine", "gastronomie", "restaurant", "manger", or any food-related terms, set "food" as the primaryActivityType and add it to specificActivities as a "must-do" activity.

        MULTI-ACTIVITY HANDLING:
        - If multiple activities are mentioned (e.g., "faire du vélo et faire les boutiques"), identify ALL of them and add them to specificActivities.
        - Set the most emphasized activity as the primaryActivityType.
        - If no clear emphasis, prioritize in this order: cycling, hiking, beach, food, cultural, shopping.

        LANGUAGE SUPPORT:
        - You can understand queries in multiple languages, including English and French.
        - For French queries like "Je voudrais faire du vélo à Paris", correctly identify "cycling" as the primary activity.
        - For French queries like "faire les boutiques à Paris", correctly identify "shopping" as a specific activity.

        SPECIFIC TRANSPORT REQUIREMENTS:
        - If cycling is mentioned, add "bike rental" to specificTransportRequirements.
        - If the user mentions any specific transport needs, add them to specificTransportRequirements.

        TRIP PURPOSE:
        - Set the tripPurpose to reflect the main goal of the trip, including all major activities.
        - For example, "cycling and shopping in Paris" should have a tripPurpose like "cycling and shopping exploration in Paris".`,
        prompt: `Extract detailed travel preferences from this query: "${query}"

        Consider the following aspects:
        - Travel style (e.g., adventure, cultural, relaxation, luxury, budget)
        - Budget level and constraints
        - Accommodation preferences
        - Dining preferences and dietary restrictions
        - Activity interests and preferred activity level
        - Primary activity focus (if any specific activity like cycling, hiking, etc. is mentioned)
        - Specific activities that must be included
        - Accessibility requirements
        - Transportation preferences (including specific needs like bike rental)
        - Special requests or needs
        - Whether they're traveling with children or pets
        - Sustainability preferences
        - Seasonal preferences
        - Time-of-day preferences for activities
        - The main purpose of the trip
        - Any visual theme preferences for the itinerary
        - Special interests that should influence the entire trip
        - Keywords that must be included in the itinerary

        Provide a comprehensive preference profile based on explicit mentions and reasonable assumptions.
        Be particularly attentive to specific activities or interests mentioned by the user.`,
        schema: z.object({
          travelStyle: z
            .array(z.string())
            .optional()
            .describe('Travel styles preferred by the user'),
          budget: z.object({
            level: z
              .enum(['budget', 'moderate', 'luxury'])
              .describe('Budget level'),
            currency: z.string().describe('Preferred currency'),
            maxAmount: z
              .number()
              .optional()
              .describe('Maximum budget amount if specified'),
          }),
          accommodation: z.object({
            type: z.array(z.string()).describe('Preferred accommodation types'),
            preferences: z
              .array(z.string())
              .describe('Specific accommodation preferences'),
          }),
          dining: z.object({
            cuisinePreferences: z
              .array(z.string())
              .describe('Preferred cuisines'),
            dietaryRestrictions: z
              .array(z.string())
              .describe('Dietary restrictions'),
            mealPreferences: z.array(z.string()).describe('Meal preferences'),
          }),
          activities: z.object({
            interests: z.array(z.string()).describe('Activity interests'),
            activityLevel: z
              .enum(['relaxed', 'moderate', 'active', 'very active'])
              .describe('Preferred activity level'),
            preferredActivities: z
              .array(z.string())
              .describe('Specific preferred activities'),
            primaryActivityType: z
              .string()
              .optional()
              .describe(
                'Main activity focus (e.g., cycling, hiking, beach, cultural)',
              ),
            specificActivities: z
              .array(
                z.object({
                  name: z.string().describe('Name of the specific activity'),
                  importance: z
                    .enum(['must-do', 'preferred', 'optional'])
                    .describe('Importance of this activity'),
                  details: z
                    .string()
                    .optional()
                    .describe('Additional details about this activity'),
                }),
              )
              .describe('Specific activities mentioned by the user'),
          }),
          accessibility: z.object({
            requirements: z
              .array(z.string())
              .describe('Accessibility requirements'),
            mobilityIssues: z
              .boolean()
              .describe('Whether mobility issues were mentioned'),
          }),
          transportation: z.object({
            preferredModes: z
              .array(z.string())
              .describe('Preferred transportation modes'),
            publicTransportPreference: z
              .boolean()
              .describe('Preference for public transportation'),
            specificTransportRequirements: z
              .array(z.string())
              .optional()
              .describe('Specific transport needs like bike rental'),
          }),
          specialRequests: z
            .array(z.string())
            .describe('Special requests or needs'),
          travelWithChildren: z
            .boolean()
            .describe('Whether traveling with children'),
          travelWithPets: z.boolean().describe('Whether traveling with pets'),
          sustainabilityPreference: z
            .boolean()
            .describe('Preference for sustainable options'),
          seasonalPreferences: z
            .array(z.string())
            .describe('Seasonal preferences'),
          timeOfDayPreferences: z.object({
            morningActivities: z
              .array(z.string())
              .describe('Preferred morning activities'),
            afternoonActivities: z
              .array(z.string())
              .describe('Preferred afternoon activities'),
            eveningActivities: z
              .array(z.string())
              .describe('Preferred evening activities'),
          }),
          tripPurpose: z
            .string()
            .describe(
              'The main purpose of the trip (e.g., cycling vacation, cultural exploration)',
            ),
          visualTheme: z
            .string()
            .optional()
            .describe('Preferred visual theme for the itinerary'),
          specialInterests: z
            .array(z.string())
            .describe(
              'Special interests that should influence the entire trip',
            ),
          mustIncludeKeywords: z
            .array(z.string())
            .describe('Keywords that must be included in the itinerary'),
        }),
        temperature: 0.7,
      });

      console.log(
        'Extracted preferences:',
        JSON.stringify(preferences, null, 2),
      );

      // Ensure all required fields are defined with defaults
      const defaultPrefs = this.getDefaultPreferences();
      const finalPreferences: UserPreferences = {
        travelStyle: preferences.travelStyle || defaultPrefs.travelStyle,
        budget: {
          level: preferences.budget?.level || defaultPrefs.budget.level,
          currency:
            preferences.budget?.currency || defaultPrefs.budget.currency,
          maxAmount: preferences.budget?.maxAmount,
        },
        accommodation: {
          type:
            preferences.accommodation?.type || defaultPrefs.accommodation.type,
          preferences:
            preferences.accommodation?.preferences ||
            defaultPrefs.accommodation.preferences,
        },
        dining: {
          cuisinePreferences:
            preferences.dining?.cuisinePreferences ||
            defaultPrefs.dining.cuisinePreferences,
          dietaryRestrictions:
            preferences.dining?.dietaryRestrictions ||
            defaultPrefs.dining.dietaryRestrictions,
          mealPreferences:
            preferences.dining?.mealPreferences ||
            defaultPrefs.dining.mealPreferences,
        },
        activities: {
          interests:
            preferences.activities?.interests ||
            defaultPrefs.activities.interests,
          activityLevel:
            preferences.activities?.activityLevel ||
            defaultPrefs.activities.activityLevel,
          preferredActivities:
            preferences.activities?.preferredActivities ||
            defaultPrefs.activities.preferredActivities,
          primaryActivityType:
            preferences.activities?.primaryActivityType ||
            defaultPrefs.activities.primaryActivityType,
          specificActivities:
            preferences.activities?.specificActivities?.filter(
              (
                activity,
              ): activity is {
                name: string;
                importance: 'must-do' | 'preferred' | 'optional';
                details?: string;
              } => Boolean(activity.name && activity.importance),
            ) || defaultPrefs.activities.specificActivities,
        },
        accessibility: {
          requirements:
            preferences.accessibility?.requirements ||
            defaultPrefs.accessibility.requirements,
          mobilityIssues:
            preferences.accessibility?.mobilityIssues ??
            defaultPrefs.accessibility.mobilityIssues,
        },
        transportation: {
          preferredModes:
            preferences.transportation?.preferredModes ||
            defaultPrefs.transportation.preferredModes,
          publicTransportPreference:
            preferences.transportation?.publicTransportPreference ??
            defaultPrefs.transportation.publicTransportPreference,
          specificTransportRequirements:
            preferences.transportation?.specificTransportRequirements ||
            defaultPrefs.transportation.specificTransportRequirements,
        },
        specialRequests:
          preferences.specialRequests || defaultPrefs.specialRequests,
        travelWithChildren:
          preferences.travelWithChildren ?? defaultPrefs.travelWithChildren,
        travelWithPets:
          preferences.travelWithPets ?? defaultPrefs.travelWithPets,
        sustainabilityPreference:
          preferences.sustainabilityPreference ??
          defaultPrefs.sustainabilityPreference,
        seasonalPreferences:
          preferences.seasonalPreferences || defaultPrefs.seasonalPreferences,
        timeOfDayPreferences: {
          morningActivities:
            preferences.timeOfDayPreferences?.morningActivities ||
            defaultPrefs.timeOfDayPreferences.morningActivities,
          afternoonActivities:
            preferences.timeOfDayPreferences?.afternoonActivities ||
            defaultPrefs.timeOfDayPreferences.afternoonActivities,
          eveningActivities:
            preferences.timeOfDayPreferences?.eveningActivities ||
            defaultPrefs.timeOfDayPreferences.eveningActivities,
        },
        tripPurpose: preferences.tripPurpose || defaultPrefs.tripPurpose,
        visualTheme: preferences.visualTheme || defaultPrefs.visualTheme,
        specialInterests:
          preferences.specialInterests || defaultPrefs.specialInterests,
        mustIncludeKeywords:
          preferences.mustIncludeKeywords || defaultPrefs.mustIncludeKeywords,
      };

      return finalPreferences;
    } catch (error) {
      console.error('Error extracting preferences:', error);

      // Return default preferences if extraction fails
      return this.getDefaultPreferences();
    }
  }

  /**
   * Get default preferences when extraction fails
   */
  private getDefaultPreferences(): UserPreferences {
    return {
      travelStyle: ['cultural', 'sightseeing', 'relaxation'],
      budget: {
        level: 'moderate',
        currency: 'EUR',
      },
      accommodation: {
        type: ['hotel', 'apartment'],
        preferences: ['central location', 'clean', 'comfortable'],
      },
      dining: {
        cuisinePreferences: ['local cuisine', 'international'],
        dietaryRestrictions: [],
        mealPreferences: ['restaurants', 'cafes'],
      },
      activities: {
        interests: ['sightseeing', 'museums', 'walking tours'],
        activityLevel: 'moderate',
        preferredActivities: [
          'city tours',
          'cultural sites',
          'local experiences',
        ],
        primaryActivityType: 'cultural',
        specificActivities: [
          {
            name: 'city walking tour',
            importance: 'preferred',
            details: 'To get an overview of the main attractions',
          },
        ],
      },
      accessibility: {
        requirements: [],
        mobilityIssues: false,
      },
      transportation: {
        preferredModes: ['walking', 'public transport', 'taxi'],
        publicTransportPreference: true,
        specificTransportRequirements: [],
      },
      specialRequests: [],
      travelWithChildren: false,
      travelWithPets: false,
      sustainabilityPreference: false,
      seasonalPreferences: ['all seasons'],
      timeOfDayPreferences: {
        morningActivities: ['sightseeing', 'museums'],
        afternoonActivities: ['shopping', 'relaxation'],
        eveningActivities: ['dining', 'entertainment'],
      },
      tripPurpose: 'general sightseeing and cultural exploration',
      visualTheme: 'classic travel guide',
      specialInterests: ['local culture', 'history', 'architecture'],
      mustIncludeKeywords: ['must-see attractions', 'local experiences'],
    };
  }
}
