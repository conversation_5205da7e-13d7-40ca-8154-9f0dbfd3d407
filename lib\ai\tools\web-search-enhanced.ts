import { tool } from 'ai';
import { z } from 'zod';
import { Exa } from 'exa-js';
import type { UIMessageStreamWriter } from 'ai';
import type { ChatMessage } from '@/lib/types';
import FirecrawlApp, {
  type SearchResultWeb,
  type SearchResultNews,
  type SearchResultImages,
  type Document,
} from '@mendable/firecrawl-js';
import { tavily, type TavilyClient } from '@tavily/core';

const extractDomain = (url: string | null | undefined): string => {
  if (!url || typeof url !== 'string') return '';
  const urlPattern = /^https?:\/\/([^/?#]+)(?:[/?#]|$)/i;
  return url.match(urlPattern)?.[1] || url;
};

const cleanTitle = (title: string): string => {
  return title
    .replace(/\[.*?\]/g, '')
    .replace(/\(.*?\)/g, '')
    .replace(/\s+/g, ' ')
    .trim();
};

const deduplicateByDomainAndUrl = <T extends { url: string }>(
  items: T[],
): T[] => {
  const seenDomains = new Set<string>();
  const seenUrls = new Set<string>();

  return items.filter((item) => {
    const domain = extractDomain(item.url);
    const isNewUrl = !seenUrls.has(item.url);
    const isNewDomain = !seenDomains.has(domain);

    if (isNewUrl && isNewDomain) {
      seenUrls.add(item.url);
      seenDomains.add(domain);
      return true;
    }
    return false;
  });
};

const isSearchResultWeb = (
  item: SearchResultWeb | Document,
): item is SearchResultWeb => {
  return 'url' in item && typeof item.url === 'string';
};

const isSearchResultNewsWithUrl = (
  item: SearchResultNews | Document,
): item is SearchResultNews & { url: string } => {
  return 'url' in item && typeof item.url === 'string' && item.url.length > 0;
};

const isSearchResultImages = (
  item: SearchResultImages | Document,
): item is SearchResultImages => {
  return (
    ('url' in item && typeof item.url === 'string') ||
    ('imageUrl' in item && typeof item.imageUrl === 'string')
  );
};

const getImageUrl = (item: SearchResultImages): string | undefined => {
  return item.imageUrl || item.url;
};

const sanitizeUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    return urlObj.href;
  } catch {
    return url;
  }
};

const isValidImageUrl = async (
  url: string,
): Promise<{ valid: boolean; redirectedUrl?: string }> => {
  try {
    return { valid: true, redirectedUrl: url };
  } catch {
    return { valid: false };
  }
};

interface SearchStrategy {
  search(
    queries: string[],
    options: {
      maxResults: number[];
      topics: ('general' | 'news')[];
      quality: ('default' | 'best')[];
    },
  ): Promise<{
    searches: Array<{ query: string; results: any[]; images: any[] }>;
  }>;
}

class TavilySearchStrategy implements SearchStrategy {
  constructor(private tvly: TavilyClient) {}

  async search(
    queries: string[],
    options: {
      maxResults: number[];
      topics: ('general' | 'news')[];
      quality: ('default' | 'best')[];
    },
  ) {
    const searchPromises = queries.map(async (query, index) => {
      const currentTopic =
        options.topics[index] || options.topics[0] || 'general';
      const currentMaxResults =
        options.maxResults[index] || options.maxResults[0] || 10;
      const currentQuality =
        options.quality[index] || options.quality[0] || 'default';

      try {
        const tavilyData = await this.tvly.search(query, {
          topic: currentTopic || 'general',
          days: currentTopic === 'news' ? 7 : undefined,
          maxResults: currentMaxResults,
          searchDepth: currentQuality === 'best' ? 'advanced' : 'basic',
          includeAnswer: true,
          includeImages: true,
          includeImageDescriptions: true,
        });

        console.log(`🔍 Tavily raw response for "${query}":`, {
          hasImages: !!tavilyData.images,
          imagesIsArray: Array.isArray(tavilyData.images),
          rawImagesCount: tavilyData.images?.length || 0,
          resultsCount: tavilyData.results?.length || 0,
          sampleRawImages: tavilyData.images?.slice(0, 2),
        });

        const results = deduplicateByDomainAndUrl(tavilyData.results).map(
          (obj: any) => ({
            url: obj.url,
            title: cleanTitle(obj.title || ''),
            content: obj.content,
            published_date:
              currentTopic === 'news' ? obj.published_date : undefined,
            author: undefined,
          }),
        );

        // For images, only deduplicate by URL, not by domain (to allow multiple images from same site)
        const seenImageUrls = new Set<string>();
        const images = (tavilyData.images || [])
          .map(
            ({ url, description }: { url: string; description?: string }) => ({
              url: sanitizeUrl(url),
              description: description || query, // Use query as fallback description
            }),
          )
          .filter((img) => {
            if (!img.url || seenImageUrls.has(img.url)) {
              return false;
            }
            seenImageUrls.add(img.url);
            return true;
          });

        console.log(`🖼️ Tavily images for "${query}":`, {
          rawImagesCount: tavilyData.images?.length || 0,
          processedImagesCount: images.length,
          images: images.slice(0, 3), // Log first 3 for debugging
        });

        return {
          query,
          results: deduplicateByDomainAndUrl(results),
          images,
        };
      } catch (error) {
        console.error(`Tavily search error for query "${query}":`, error);
        return {
          query,
          results: [],
          images: [],
        };
      }
    });

    const searchResults = await Promise.all(searchPromises);
    return { searches: searchResults };
  }
}

class FirecrawlSearchStrategy implements SearchStrategy {
  constructor(private firecrawl: FirecrawlApp) {}

  async search(
    queries: string[],
    options: {
      maxResults: number[];
      topics: ('general' | 'news')[];
      quality: ('default' | 'best')[];
    },
  ) {
    const searchPromises = queries.map(async (query, index) => {
      const currentTopic =
        options.topics[index] || options.topics[0] || 'general';
      const currentMaxResults =
        options.maxResults[index] || options.maxResults[0] || 10;

      try {
        const sources = [] as ('web' | 'news' | 'images')[];

        if (currentTopic === 'news') {
          sources.push('news', 'web');
        } else {
          sources.push('web');
        }
        sources.push('images');

        const firecrawlData = await this.firecrawl.search(query, {
          sources,
          limit: currentMaxResults,
        });

        let results: any[] = [];

        if (firecrawlData?.web && Array.isArray(firecrawlData.web)) {
          const webResults = firecrawlData.web.filter(isSearchResultWeb);
          results = deduplicateByDomainAndUrl(webResults).map((result) => ({
            url: result.url,
            title: cleanTitle(result.title || ''),
            content: result.description || '',
            published_date: undefined,
            author: undefined,
          }));
        }

        if (
          firecrawlData?.news &&
          Array.isArray(firecrawlData.news) &&
          currentTopic === 'news'
        ) {
          const newsResults = firecrawlData.news.filter(
            isSearchResultNewsWithUrl,
          );
          const processedNewsResults = deduplicateByDomainAndUrl(
            newsResults,
          ).map((result) => ({
            url: result.url,
            title: cleanTitle(result.title || ''),
            content: result.snippet || '',
            published_date: result.date || undefined,
            author: undefined,
          }));

          results = [...processedNewsResults, ...results];
        }

        let images: { url: string; description: string }[] = [];
        if (firecrawlData?.images && Array.isArray(firecrawlData.images)) {
          const imageResults =
            firecrawlData.images.filter(isSearchResultImages);
          // For images, only deduplicate by URL, not by domain
          const seenImageUrls = new Set<string>();
          images = imageResults
            .map((image) => ({
              url: getImageUrl(image) || '',
              description: cleanTitle(image.title || query), // Use query as fallback
            }))
            .filter((img) => {
              if (!img.url || seenImageUrls.has(img.url)) {
                return false;
              }
              seenImageUrls.add(img.url);
              return true;
            });
        }

        console.log(`🖼️ Firecrawl images for "${query}":`, {
          rawImagesCount: firecrawlData?.images?.length || 0,
          processedImagesCount: images.length,
        });

        return {
          query,
          results: deduplicateByDomainAndUrl(results),
          images,
        };
      } catch (error) {
        console.error(`Firecrawl search error for query "${query}":`, error);
        return {
          query,
          results: [],
          images: [],
        };
      }
    });

    const searchResults = await Promise.all(searchPromises);
    return { searches: searchResults };
  }
}

class ExaSearchStrategy implements SearchStrategy {
  constructor(private exa: Exa) {}

  async search(
    queries: string[],
    options: {
      maxResults: number[];
      topics: ('general' | 'news')[];
      quality: ('default' | 'best')[];
    },
  ) {
    const searchPromises = queries.map(async (query, index) => {
      const currentTopic =
        options.topics[index] || options.topics[0] || 'general';
      const currentMaxResults =
        options.maxResults[index] || options.maxResults[0] || 10;
      const currentQuality =
        options.quality[index] || options.quality[0] || 'default';

      try {
        const searchOptions: any = {
          text: true,
          type: currentQuality === 'best' ? 'hybrid' : 'auto',
          numResults: currentMaxResults < 10 ? 10 : currentMaxResults,
          livecrawl: 'preferred',
          useAutoprompt: true,
          category: currentTopic === 'news' ? 'news' : '',
        };

        const data = await this.exa.searchAndContents(query, searchOptions);

        const collectedImages: { url: string; description: string }[] = [];

        const results = data.results.map((result) => {
          if (result.image) {
            collectedImages.push({
              url: result.image,
              description: cleanTitle(
                result.title || `${result.text?.substring(0, 100)}...` || '',
              ),
            });
          }

          return {
            url: result.url,
            title: cleanTitle(result.title || ''),
            content: (result.text || '').substring(0, 1000),
            published_date:
              currentTopic === 'news' && result.publishedDate
                ? result.publishedDate
                : undefined,
            author: result.author || undefined,
          };
        });

        // For images, only deduplicate by URL, not by domain
        const seenImageUrls = new Set<string>();
        const images = collectedImages.filter((img) => {
          if (!img.url || seenImageUrls.has(img.url)) {
            return false;
          }
          seenImageUrls.add(img.url);
          return true;
        });

        console.log(`🖼️ Exa images for "${query}":`, {
          collectedImagesCount: collectedImages.length,
          processedImagesCount: images.length,
        });

        return {
          query,
          results: deduplicateByDomainAndUrl(results),
          images,
        };
      } catch (error) {
        console.error(`Exa search error for query "${query}":`, error);
        return {
          query,
          results: [],
          images: [],
        };
      }
    });

    const searchResults = await Promise.all(searchPromises);
    return { searches: searchResults };
  }
}

const createSearchStrategy = (
  provider: 'exa' | 'tavily' | 'firecrawl',
  clients: {
    exa: Exa;
    firecrawl: FirecrawlApp;
    tvly: TavilyClient;
  },
): SearchStrategy => {
  const strategies = {
    tavily: () => new TavilySearchStrategy(clients.tvly),
    firecrawl: () => new FirecrawlSearchStrategy(clients.firecrawl),
    exa: () => new ExaSearchStrategy(clients.exa),
  };

  return strategies[provider]();
};

export function webSearchEnhancedTool(
  _dataStream?: UIMessageStreamWriter<ChatMessage> | undefined,
  searchProvider: 'exa' | 'tavily' | 'firecrawl' = 'tavily',
) {
  return tool({
    description: `Search the web for information with multiple queries, max results, search depth, topics, and quality.
    Rules:
    - The queries should always be in the same language as the user's message.
    - Count of the queries should be 6-10 for comprehensive coverage.
    - Use advanced search depth (best quality) for important topics to get detailed, authoritative information.
    - Create diverse queries covering different angles: current events, historical context, expert analysis, statistics, official sources.
    `,
    inputSchema: z.object({
      queries: z.array(
        z
          .string()
          .describe(
            'Array of 6-10 search queries to look up on the web. Default is 8. Minimum is 6. Maximum is 10. Create diverse queries covering different perspectives.',
          ),
      ),
      maxResults: z
        .array(
          z
            .number()
            .optional()
            .describe(
              'Array of maximum number of results to return per query. Default is 15. Minimum is 10. Maximum is 20.',
            ),
        )
        .optional()
        .describe(
          'Array of maximum number of results to return per query. Default is 15 for each query if not provided.',
        ),
      topics: z
        .array(
          z
            .enum(['general', 'news'])
            .optional()
            .describe(
              'Array of topic types to search for. Default is general. Other option is news.',
            ),
        )
        .optional()
        .describe(
          'Array of topic types to search for. Default is general for each query if not provided.',
        ),
      quality: z
        .array(
          z
            .enum(['default', 'best'])
            .optional()
            .describe(
              'Array of quality levels for the search. Default is best for comprehensive, authoritative results. Use default only for quick, simple queries.',
            ),
        )
        .optional()
        .describe(
          'Array of quality levels for the search. Default is best for each query if not provided.',
        ),
    }),
    execute: async ({
      queries,
      maxResults,
      topics,
      quality,
    }: {
      queries: string[];
      maxResults?: (number | undefined)[];
      topics?: ('general' | 'news' | undefined)[];
      quality?: ('default' | 'best' | undefined)[];
    }) => {
      console.log('\n🔍 ========================================');
      console.log('🔍 WEB_SEARCH_ENHANCED CALLED');
      console.log('🔍 Provider:', searchProvider);
      console.log('🔍 Number of queries:', queries.length);
      console.log('🔍 ========================================\n');

      const clients = {
        exa: new Exa(process.env.EXA_API_KEY || ''),
        firecrawl: new FirecrawlApp({
          apiKey: process.env.FIRECRAWL_API_KEY || '',
        }),
        tvly: tavily({ apiKey: process.env.TAVILY_API_KEY || '' }),
      };

      console.log('Queries:', queries);
      console.log('Max Results:', maxResults);
      console.log('Topics:', topics);
      console.log('Quality:', quality);
      console.log('Search Provider:', searchProvider);

      const strategy = createSearchStrategy(searchProvider, clients);

      if (!maxResults) {
        maxResults = new Array(queries.length).fill(15);
      }
      if (!topics) {
        topics = new Array(queries.length).fill('general');
      }
      if (!quality) {
        quality = new Array(queries.length).fill('best');
      }

      const searchResults = await strategy.search(queries, {
        maxResults: maxResults as number[],
        topics: topics as ('general' | 'news')[],
        quality: quality as ('default' | 'best')[],
      });

      // Debug: Log final results structure
      console.log('🔍 Final search results structure:', {
        hasSearches: 'searches' in searchResults,
        searchesCount: searchResults.searches?.length || 0,
        totalImages: searchResults.searches?.reduce(
          (sum, s) => sum + (s.images?.length || 0),
          0,
        ),
        firstSearchImages: searchResults.searches?.[0]?.images?.length || 0,
        sampleImages: searchResults.searches?.[0]?.images?.slice(0, 2),
      });

      return searchResults;
    },
  });
}
