/**
 * API Route pour récupérer l'utilisation de la recherche extrême
 * GET /api/extreme-search/usage?userId=xxx
 */

import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { extremeSearchUsage } from '@/lib/db/schema';
import { eq, and, gte } from 'drizzle-orm';

// Initialize database connection
// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'userId est requis' }, { status: 400 });
    }

    // Calculer le début du mois actuel
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Récupérer l'utilisation du mois en cours
    const usage = await db
      .select()
      .from(extremeSearchUsage)
      .where(
        and(
          eq(extremeSearchUsage.userId, userId),
          gte(extremeSearchUsage.date, startOfMonth),
        ),
      )
      .limit(1);

    const count = usage.length > 0 ? usage[0].searchCount : 0;

    return NextResponse.json({
      count,
      month: now.toISOString().slice(0, 7), // YYYY-MM
      userId,
    });
  } catch (error) {
    console.error("Erreur lors de la récupération de l'utilisation:", error);
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
  }
}
