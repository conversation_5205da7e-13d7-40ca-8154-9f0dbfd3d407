import { tool } from 'ai';
import { z } from 'zod';
import { getDocumentById } from '@/lib/db/queries';
import { getDocumentContentFromMemory } from '@/lib/ai/memory-retrieval';

export const getDocument = tool({
  description: 'Retrieve the content of a document by its ID or title',
  inputSchema: z.object({
    documentId: z
      .string()
      .optional()
      .describe('The ID of the document to retrieve'),
    title: z
      .string()
      .optional()
      .describe('The title of the document to search for'),
  }),
  execute: async ({ documentId, title }) => {
    // Vérifier qu'au moins un paramètre est fourni
    if (!documentId && !title) {
      return {
        error: "Vous devez fournir soit l'ID du document, soit son titre.",
      };
    }

    // Essayer d'abord de récupérer le document depuis la base de données
    try {
      if (documentId) {
        const document = await getDocumentById({ id: documentId });
        if (document) {
          return {
            id: document.id,
            title: document.title,
            kind: document.kind,
            content: document.content || '',
          };
        }
      }

      // Si on n'a pas trouvé le document par ID, essayer de le récupérer depuis la mémoire
      if (documentId) {
        const content = await getDocumentContentFromMemory(documentId);
        if (content) {
          return {
            id: documentId,
            content,
            message: 'Document récupéré depuis la mémoire',
          };
        }
      }

      // Si on n'a pas trouvé le document, retourner une erreur
      return {
        error: `Document non trouvé. Veuillez vérifier l'ID ou le titre.`,
      };
    } catch (error) {
      console.error('Erreur lors de la récupération du document:', error);
      return {
        error: 'Une erreur est survenue lors de la récupération du document.',
      };
    }
  },
});
