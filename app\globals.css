@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
  /* autres variables */
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-neutral-200 dark:border-neutral-800;
  }

  body {
    @apply bg-background text-foreground;
  }
}

.skeleton {
  * {
    pointer-events: none !important;
  }

  *[class^="text-"] {
    color: transparent;
    @apply rounded-md bg-foreground/20 select-none animate-pulse;
  }

  .skeleton-bg {
    @apply bg-foreground/10;
  }

  .skeleton-div {
    @apply bg-foreground/20 animate-pulse;
  }
}

.ProseMirror {
  outline: none;
}

.cm-editor,
.cm-gutters {
  @apply bg-background dark:bg-zinc-800 outline-none selection:bg-zinc-900 !important;
}

.ͼo.cm-focused>.cm-scroller>.cm-selectionLayer .cm-selectionBackground,
.ͼo.cm-selectionBackground,
.ͼo.cm-content::selection {
  @apply bg-zinc-200 dark:bg-zinc-900 !important;
}

.cm-activeLine,
.cm-activeLineGutter {
  @apply bg-transparent !important;
}

.cm-activeLine {
  @apply rounded-r-sm !important;
}

.cm-lineNumbers {
  @apply min-w-7;
}

.cm-foldGutter {
  @apply min-w-3;
}

.cm-lineNumbers .cm-activeLineGutter {
  @apply rounded-l-sm !important;
}

/* Styles pour les suggestions */
.suggestion-highlight {
  background-color: rgba(255, 220, 0, 0.2);
  border-bottom: 2px dotted rgba(255, 220, 0, 0.8);
  position: relative;
}

/* Styles pour les widgets de suggestion */
.suggestion-widget {
  position: relative;
  display: inline-block;
  z-index: 9999 !important;
  pointer-events: auto !important;
}

/* Positionnement spécifique pour les widgets de suggestion */
.ProseMirror .suggestion-widget {
  position: absolute;
  right: -30px;
  transform: translateY(-50%);
}

/* S'assurer que les widgets sont au-dessus de tout */
.ProseMirror {
  position: relative;
}

/* Forcer la visibilité des icônes */
.suggestion-icon {
  opacity: 1 !important;
  visibility: visible !important;
  pointer-events: auto !important;
}

/* Rendre les URLs cliquables automatiquement */
.ProseMirror a[href],
.artifact-content a[href] {
  color: #2563eb;
  /* Bleu */
  text-decoration: none;
}

.ProseMirror a[href]:hover,
.artifact-content a[href]:hover {
  text-decoration: underline;
}

/* Activer le comportement de lien automatique pour les URLs */
.ProseMirror,
.artifact-content {
  overflow-wrap: break-word;
  word-wrap: break-word;
}

/* Ajouter ces styles pour s'assurer que le formatage Markdown persiste */
.markdown-container {
  /* Empêcher d'autres styles de remplacer notre formatage */
  display: block !important;
}

.markdown-container h1,
.markdown-container h2,
.markdown-container h3,
.markdown-container h4,
.markdown-container h5,
.markdown-container h6 {
  font-weight: bold !important;
  margin-top: 1em !important;
  margin-bottom: 0.5em !important;
}

.markdown-container h1 {
  font-size: 2rem !important;
}

.markdown-container h2 {
  font-size: 1.5rem !important;
}

.markdown-container h3 {
  font-size: 1.25rem !important;
}

.markdown-container p {
  margin-bottom: 1em !important;
}

.markdown-container ul {
  list-style-type: disc !important;
  padding-left: 1.5em !important;
  margin-bottom: 1em !important;
}

.markdown-container ol {
  list-style-type: decimal !important;
  padding-left: 1.5em !important;
  margin-bottom: 1em !important;
}

.markdown-container blockquote {
  border-left: 4px solid #e2e8f0 !important;
  padding-left: 1em !important;
  margin: 1em 0 !important;
  font-style: italic !important;
}

.markdown-container pre {
  background-color: #f7fafc !important;
  padding: 1em !important;
  border-radius: 0.25em !important;
  overflow-x: auto !important;
  margin: 1em 0 !important;
}

.dark .markdown-container pre {
  background-color: #2d3748 !important;
}

.markdown-container code {
  background-color: #f7fafc !important;
  padding: 0.1em 0.3em !important;
  border-radius: 0.25em !important;
  font-size: 0.9em !important;
}

.dark .markdown-container code {
  background-color: #2d3748 !important;
}

/* Weather component responsive styles */
@container (max-width: 350px) {
  .weather-chart-tab {
    padding: 0.25rem;
  }

  .weather-chart-tab .text-xs {
    font-size: 0.65rem;
  }

  .weather-chart-tab .text-sm {
    font-size: 0.75rem;
  }

  .weather-chart-tab .h-8 {
    height: 1.5rem;
  }

  .weather-chart-tab .w-8 {
    width: 1.5rem;
  }

  .weather-chart-tab .min-w-\[60px\] {
    min-width: 40px;
  }

  .weather-chart-tab .min-w-\[70px\] {
    min-width: 50px;
  }

  .weather-chart-tab .p-1\.5 {
    padding: 0.25rem;
  }

  .weather-chart-tab .p-2 {
    padding: 0.25rem;
  }

  .weather-chart-tab .gap-1\.5 {
    gap: 0.25rem;
  }

  .weather-chart-tab .gap-2 {
    gap: 0.25rem;
  }

  .weather-chart-tab .text-\[9px\] {
    font-size: 0.6rem;
  }

  .weather-chart-tab .text-\[10px\] {
    font-size: 0.65rem;
  }

  /* Ensure charts resize properly */
  .weather-chart-tab .recharts-wrapper {
    width: 100% !important;
  }

  .weather-chart-tab .recharts-surface {
    width: 100% !important;
  }

  /* Responsive grid for weather cards */
  .weather-chart-tab .grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .weather-chart-tab .grid-cols-6 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Additional responsive rules for weather component in artifact view */
@container (max-width: 320px) {
  .weather-chart-tab .text-xs {
    font-size: 0.65rem;
  }

  .weather-chart-tab .text-sm {
    font-size: 0.7rem;
  }

  .weather-chart-tab .h-8 {
    height: 1.5rem;
  }

  .weather-chart-tab .w-8 {
    width: 1.5rem;
  }

  .weather-chart-tab .p-1 {
    padding: 0.25rem;
  }

  .weather-chart-tab .gap-1 {
    gap: 0.25rem;
  }
}

/* Enhanced responsive rules for very narrow artifact view */
@container (max-width: 280px) {
  .weather-chart-tab .text-xs {
    font-size: 0.6rem;
  }

  .weather-chart-tab .text-sm {
    font-size: 0.65rem;
  }

  .weather-chart-tab .h-8 {
    height: 1.25rem;
  }

  .weather-chart-tab .w-8 {
    width: 1.25rem;
  }

  .weather-chart-tab .p-1 {
    padding: 0.2rem;
  }

  .weather-chart-tab .gap-1 {
    gap: 0.2rem;
  }
}

/* Extreme narrow view for artifact */
@container (max-width: 250px) {
  .weather-chart-tab .text-xs {
    font-size: 0.55rem;
  }

  .weather-chart-tab .text-sm {
    font-size: 0.6rem;
  }

  .weather-chart-tab .h-8 {
    height: 1.1rem;
  }

  .weather-chart-tab .w-8 {
    width: 1.1rem;
  }

  .weather-chart-tab .p-1 {
    padding: 0.15rem;
  }

  .weather-chart-tab .gap-1 {
    gap: 0.15rem;
  }
}

@container (max-width: 400px) {
  .weather-chart-tab {
    padding: 0.5rem;
  }

  .weather-chart-tab .text-xs {
    font-size: 0.7rem;
  }

  .weather-chart-tab .text-sm {
    font-size: 0.8rem;
  }

  .weather-chart-tab .h-8 {
    height: 1.75rem;
  }

  .weather-chart-tab .w-8 {
    width: 1.75rem;
  }

  /* Ensure charts resize properly */
  .weather-chart-tab .recharts-wrapper {
    width: 100% !important;
  }

  .weather-chart-tab .recharts-surface {
    width: 100% !important;
  }

  /* Responsive grid for weather cards */
  .weather-chart-tab .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .weather-chart-tab .grid-cols-5 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .weather-chart-tab .grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
}

/* Additional responsive rules for very narrow containers */
@container (max-width: 300px) {
  .weather-chart-tab .text-\[9px\] {
    font-size: 0.55rem;
  }

  .weather-chart-tab .text-\[10px\] {
    font-size: 0.6rem;
  }

  .weather-chart-tab .p-1\.5 {
    padding: 0.125rem;
  }

  .weather-chart-tab .p-2 {
    padding: 0.125rem;
  }

  .weather-chart-tab .gap-1\.5 {
    gap: 0.125rem;
  }

  .weather-chart-tab .gap-2 {
    gap: 0.125rem;
  }

  /* Responsive grid for weather cards */
  .weather-chart-tab .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .weather-chart-tab .grid-cols-5 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .weather-chart-tab .grid-cols-6 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

/* New container queries for artifact view */
@container (max-width: 280px) {
  .weather-chart-tab {
    padding: 0.125rem;
  }

  .weather-chart-tab .text-\[9px\] {
    font-size: 0.5rem;
  }

  .weather-chart-tab .text-\[10px\] {
    font-size: 0.55rem;
  }

  .weather-chart-tab .p-1\.5 {
    padding: 0.1rem;
  }

  .weather-chart-tab .p-2 {
    padding: 0.1rem;
  }

  .weather-chart-tab .gap-1\.5 {
    gap: 0.1rem;
  }

  .weather-chart-tab .gap-2 {
    gap: 0.1rem;
  }

  /* Responsive grid for weather cards */
  .weather-chart-tab .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .weather-chart-tab .grid-cols-5 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .weather-chart-tab .grid-cols-6 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* New container queries for very narrow artifact view */
@container (max-width: 250px) {
  .weather-chart-tab .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .weather-chart-tab .grid-cols-5 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .weather-chart-tab .grid-cols-6 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Container queries for artifact view when chat is reduced */
@container (max-width: 200px) {
  .weather-chart-tab .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .weather-chart-tab .grid-cols-5 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .weather-chart-tab .grid-cols-6 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

/* Container queries for medium-width containers */
@container (min-width: 401px) and (max-width: 500px) {
  .weather-chart-tab .grid-cols-5 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@container (min-width: 501px) and (max-width: 600px) {
  .weather-chart-tab .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}

@container (min-width: 601px) {
  .weather-chart-tab .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}

/* Additional container queries for artifact view */
@container (max-width: 350px) and (min-width: 301px) {
  .weather-chart-tab .grid-cols-5 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Container queries for narrow artifact view */
@container (max-width: 450px) and (min-width: 351px) {
  .weather-chart-tab .grid-cols-5 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

/* Hide scrollbar but keep functionality */
.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Floating Widget Styles */
.floating-widget {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease-in-out;
  user-select: none;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.floating-widget:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2), 0 6px 15px rgba(0, 0, 0, 0.15);
}

/* Optimisations pour le drag ultra-fluide */
.floating-widget.dragging {
  transition: none !important;
  pointer-events: none;
  transform: translateZ(0);
  will-change: transform;
  contain: layout style paint;
}

.floating-widget.dragging * {
  pointer-events: none;
  user-select: none;
}

.floating-widget.resizing {
  transition: none !important;
  will-change: width, height;
  contain: layout style;
}

/* Optimisations supplémentaires pour la performance */
.floating-widget {
  contain: layout style paint;
  isolation: isolate;
}

.floating-widget .widget-content {
  contain: layout style paint;
  transform: translateZ(0);
}

.floating-widget .drag-handle {
  cursor: move;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
}

.floating-widget .drag-handle:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.floating-widget .widget-content {
  overflow: hidden;
  position: relative;
}

/* Resize handle styles */
.floating-widget .resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  cursor: se-resize;
  background: linear-gradient(-45deg, transparent 30%, #9ca3af 30%, #9ca3af 70%, transparent 70%);
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.floating-widget:hover .resize-handle {
  opacity: 1;
}

/* Window control buttons */
.floating-widget .window-controls button {
  transition: all 0.15s ease;
  border-radius: 4px;
}

.floating-widget .window-controls button:hover {
  transform: scale(1.1);
}

/* Floating widget overlay for better visibility */
.floating-widget-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 999;
}

/* Ensure floating widgets are above other content */
.floating-widget {
  z-index: 1000;
}

/* Smooth animations for minimize/maximize */
.floating-widget {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark theme support for floating widgets */
.dark .floating-widget {
  background-color: #1f2937;
  border-color: #374151;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 4px 10px rgba(0, 0, 0, 0.2);
}

.dark .floating-widget .drag-handle {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  border-bottom-color: #4b5563;
}

.dark .floating-widget .drag-handle:hover {
  background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
}

/* Hide floating chat controls while the image modal is open */
body.image-modal-open [data-testid="scroll-to-bottom-button"] {
  display: none !important;
}

body.image-modal-open [data-testid="microphone-button"] {
  display: none !important;
}

/* Enhanced responsive rules for 5-day overview alignment */
@container (max-width: 250px) {
  .weather-chart-tab .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .weather-chart-tab .gap-2 {
    gap: 0.25rem;
  }

  .weather-chart-tab .p-2 {
    padding: 0.25rem;
  }

  .weather-chart-tab .h-10 {
    height: 2rem;
  }

  .weather-chart-tab .w-10 {
    width: 2rem;
  }

  .weather-chart-tab .my-2 {
    margin-top: 0.25rem;
    margin-bottom: 0.25rem;
  }
}

@container (min-width: 251px) and (max-width: 350px) {
  .weather-chart-tab .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@container (min-width: 351px) and (max-width: 450px) {
  .weather-chart-tab .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .weather-chart-tab .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@container (min-width: 451px) and (max-width: 600px) {
  .weather-chart-tab .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@container (min-width: 601px) {
  .weather-chart-tab .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}

/* Ensure consistent alignment for weather cards */
.weather-chart-tab .flex.flex-col.items-center {
  justify-content: flex-start;
}

.weather-chart-tab .flex.flex-col.items-center>div {
  text-align: center;
  width: 100%;
}

/* X Search Tweet Responsive Styles */
.tweet-wrapper,
.tweet-wrapper-sheet {
  max-width: 100%;
  overflow: hidden;
}

.tweet-wrapper > div,
.tweet-wrapper-sheet > div {
  max-width: 100% !important;
  width: 100% !important;
}

/* Ensure react-tweet components are responsive */
.tweet-wrapper article,
.tweet-wrapper-sheet article {
  max-width: 100% !important;
}

/* Fix tweet container overflow */
[data-theme] {
  max-width: 100% !important;
}

/* Responsive tweet embed */
@media (max-width: 640px) {
  .tweet-wrapper,
  .tweet-wrapper-sheet {
    width: 100%;
  }
}

/* X Search Tweet Wrapper - Prevent width overflow */
.tweet-wrapper {
  max-width: 100%;
  overflow: hidden;
}

.tweet-wrapper > div {
  max-width: 100% !important;
  width: 100% !important;
}

.tweet-wrapper-sheet {
  max-width: 100%;
  overflow: hidden;
}

.tweet-wrapper-sheet > div {
  max-width: 100% !important;
  width: 100% !important;
}


/* X Search Tweet Responsive Scaling with Container Queries */
.x-search-container {
  container-type: inline-size;
  container-name: x-search;
}

.tweet-wrapper {
  width: 350px;
  max-width: 100%;
  overflow: visible;
  transform-origin: top left;
  flex-shrink: 0;
}

/* Use container queries for responsive scaling */
@container x-search (max-width: 700px) {
  .tweet-wrapper {
    transform: scale(0.9);
    width: 315px;
  }
}

@container x-search (max-width: 600px) {
  .tweet-wrapper {
    transform: scale(0.85);
    width: 300px;
  }
}

@container x-search (max-width: 500px) {
  .tweet-wrapper {
    transform: scale(0.75);
    width: 265px;
  }
}

@container x-search (max-width: 400px) {
  .tweet-wrapper {
    transform: scale(0.65);
    width: 230px;
  }
}

@container x-search (max-width: 350px) {
  .tweet-wrapper {
    transform: scale(0.55);
    width: 195px;
  }
}

@container x-search (max-width: 300px) {
  .tweet-wrapper {
    transform: scale(0.5);
    width: 175px;
  }
}

/* Custom scrollbar for tweet containers */
.x-search-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.x-search-scroll::-webkit-scrollbar {
  height: 8px;
}

.x-search-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.x-search-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}

.x-search-scroll::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Dark mode scrollbar */
.dark .x-search-scroll {
  scrollbar-color: rgba(107, 114, 128, 0.5) transparent;
}

.dark .x-search-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(107, 114, 128, 0.5);
}

.dark .x-search-scroll::-webkit-scrollbar-thumb:hover {
  background-color: rgba(107, 114, 128, 0.7);
}
