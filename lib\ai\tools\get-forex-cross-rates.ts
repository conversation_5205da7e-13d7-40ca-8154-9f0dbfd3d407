import { z } from 'zod';
import { tool } from 'ai';

// Define and export the tool configuration
export const getForexCrossRates = tool({
  description:
    'Display a forex cross rates table showing exchange rates between major currencies',
  inputSchema: z.object({
    // No parameters needed for the cross rates as it shows a predefined view
  }),
  execute: async () => {
    // Return a simple object that will be used by the frontend to render the component
    return {
      type: 'forex_cross_rates',
      data: {},
    };
  },
});
