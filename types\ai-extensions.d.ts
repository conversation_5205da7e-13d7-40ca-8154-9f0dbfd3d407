// Extension des types pour le package 'ai'

// Définir le type MessageAnnotation
export interface MessageAnnotation<T = any> {
  id?: string;
  type: string;
  name?: string;
  data: T;
  content?: string | any; // Pour les résultats d'outils
}

// Types spécifiques pour les annotations
export type MapDisplayAnnotation = MessageAnnotation<{
  query: string;
  places: Array<{
    cid: string;
    title: string;
    address: string;
    latitude: number;
    longitude: number;
    rating: number;
    category: string;
    phoneNumber?: string;
    website?: string;
  }>;
}>;

export type YouTubeSearchAnnotation = MessageAnnotation<{
  query: string;
  status: string;
  resultsCount: number;
}>;

export type ExtremeSearchProgressAnnotation = MessageAnnotation<{
  stage: string;
  message: string;
  timestamp: number;
  query?: string;
  section?: string;
  plan?: any;
}>;

// Étendre le module 'ai'
declare module 'ai' {
  // Étendre UIMessage pour inclure les annotations et d'autres propriétés
  export interface UIMessage {
    annotations?: MessageAnnotation[];
    role: 'user' | 'assistant' | 'system' | 'tool' | 'function' | 'data';
    content: string;
    id: string;
    createdAt?: Date;
    parts?: any[];
    experimental_attachments?: any[];
  }

  // Étendre Message pour être compatible avec UIMessage
  export interface Message {
    role: 'user' | 'assistant' | 'system' | 'tool' | 'function' | 'data';
    content: string;
    id: string;
    createdAt?: Date;
    name?: string;
  }
}
