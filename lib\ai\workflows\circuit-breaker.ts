/**
 * Circuit Breaker implementation for API overload protection
 * Prevents overwhelming APIs when they are experiencing issues
 */

import { TIMEOUT_CONFIG } from './timeout-config';

enum CircuitState {
  CLOSED = 'CLOSED', // Normal operation
  OPEN = 'OPEN', // Circuit is open, failing fast
  HALF_OPEN = 'HALF_OPEN', // Testing if service has recovered
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  halfOpenMaxCalls: number;
}

class CircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private failureCount = 0;
  private lastFailureTime = 0;
  private halfOpenCalls = 0;
  private config: CircuitBreakerConfig;

  constructor(
    private name: string,
    config?: Partial<CircuitBreakerConfig>,
  ) {
    this.config = {
      failureThreshold:
        config?.failureThreshold ||
        TIMEOUT_CONFIG.RETRY.CIRCUIT_BREAKER.FAILURE_THRESHOLD,
      recoveryTimeout:
        config?.recoveryTimeout ||
        TIMEOUT_CONFIG.RETRY.CIRCUIT_BREAKER.RECOVERY_TIMEOUT,
      halfOpenMaxCalls:
        config?.halfOpenMaxCalls ||
        TIMEOUT_CONFIG.RETRY.CIRCUIT_BREAKER.HALF_OPEN_MAX_CALLS,
    };
  }

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitState.HALF_OPEN;
        this.halfOpenCalls = 0;
        console.log(`Circuit breaker ${this.name}: Moving to HALF_OPEN state`);
      } else {
        throw new Error(
          `Circuit breaker ${this.name} is OPEN. Service unavailable.`,
        );
      }
    }

    if (this.state === CircuitState.HALF_OPEN) {
      if (this.halfOpenCalls >= this.config.halfOpenMaxCalls) {
        throw new Error(
          `Circuit breaker ${this.name} is HALF_OPEN with max calls reached.`,
        );
      }
      this.halfOpenCalls++;
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failureCount = 0;
    if (this.state === CircuitState.HALF_OPEN) {
      this.state = CircuitState.CLOSED;
      console.log(
        `Circuit breaker ${this.name}: Moving to CLOSED state (recovered)`,
      );
    }
  }

  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.state === CircuitState.HALF_OPEN) {
      this.state = CircuitState.OPEN;
      console.log(
        `Circuit breaker ${this.name}: Moving to OPEN state (half-open test failed)`,
      );
    } else if (this.failureCount >= this.config.failureThreshold) {
      this.state = CircuitState.OPEN;
      console.log(
        `Circuit breaker ${this.name}: Moving to OPEN state (threshold reached: ${this.failureCount})`,
      );
    }
  }

  private shouldAttemptReset(): boolean {
    return Date.now() - this.lastFailureTime >= this.config.recoveryTimeout;
  }

  getState(): CircuitState {
    return this.state;
  }

  getStats() {
    return {
      state: this.state,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
      halfOpenCalls: this.halfOpenCalls,
    };
  }

  reset(): void {
    this.state = CircuitState.CLOSED;
    this.failureCount = 0;
    this.lastFailureTime = 0;
    this.halfOpenCalls = 0;
    console.log(`Circuit breaker ${this.name}: Manually reset to CLOSED state`);
  }
}

// Global circuit breakers for different services
const circuitBreakers = new Map<string, CircuitBreaker>();

/**
 * Get or create a circuit breaker for a service
 */
export function getCircuitBreaker(
  serviceName: string,
  config?: Partial<CircuitBreakerConfig>,
): CircuitBreaker {
  const existing = circuitBreakers.get(serviceName);
  if (existing) return existing;
  const created = new CircuitBreaker(serviceName, config);
  circuitBreakers.set(serviceName, created);
  return created;
}

/**
 * Execute operation with circuit breaker protection
 */
export async function withCircuitBreaker<T>(
  operation: () => Promise<T>,
  serviceName: string,
  config?: Partial<CircuitBreakerConfig>,
): Promise<T> {
  const circuitBreaker = getCircuitBreaker(serviceName, config);
  return circuitBreaker.execute(operation);
}

/**
 * Get stats for all circuit breakers
 */
export function getAllCircuitBreakerStats() {
  const stats: Record<string, any> = {};
  for (const [name, breaker] of circuitBreakers.entries()) {
    stats[name] = breaker.getStats();
  }
  return stats;
}

/**
 * Reset all circuit breakers
 */
export function resetAllCircuitBreakers(): void {
  for (const breaker of circuitBreakers.values()) {
    breaker.reset();
  }
}

/**
 * Reset specific circuit breaker
 */
export function resetCircuitBreaker(serviceName: string): void {
  const breaker = circuitBreakers.get(serviceName);
  if (breaker) {
    breaker.reset();
  }
}

export { CircuitState, CircuitBreaker };
