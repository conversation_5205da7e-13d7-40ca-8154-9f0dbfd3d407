'use client';

import React, { useEffect, useRef, useState, memo } from 'react';

interface StockScreenerProps {
  className?: string;
}

function StockScreener({ className = '' }: StockScreenerProps) {
  const container = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!container.current) return;

    const currentContainer = container.current;

    const loadScript = () => {
      try {
        const script = document.createElement('script');
        script.src =
          'https://s3.tradingview.com/external-embedding/embed-widget-screener.js';
        script.type = 'text/javascript';
        script.async = true;

        script.onload = () => {
          setIsLoading(false);
          setError(null);
        };

        script.onerror = () => {
          setError(
            'Erreur lors du chargement du widget TradingView. Veuillez réessayer.',
          );
          setIsLoading(false);
        };

        script.innerHTML = JSON.stringify({
          width: '100%',
          height: '100%',
          defaultColumn: 'overview',
          defaultScreen: 'most_capitalized',
          market: 'america',
          showToolbar: true,
          colorTheme: 'light',
          locale: 'fr',
          isTransparent: true,
          autosize: true,
          enable_publishing: false,
          allow_symbol_change: true,
          save_image: false,
          hide_top_toolbar: false,
          hide_side_toolbar: false,
          hide_legend: true,
          container_id: 'tradingview-screener',
        });

        if (currentContainer) {
          currentContainer.innerHTML = '';
          currentContainer.appendChild(script);
        }
      } catch (err) {
        setError("Erreur lors de l'initialisation du widget.");
        setIsLoading(false);
      }
    };

    // Délai pour éviter les problèmes de chargement
    const timer = setTimeout(loadScript, 300);

    return () => {
      clearTimeout(timer);
      if (currentContainer) {
        currentContainer.innerHTML = '';
      }
    };
  }, []);

  if (error) {
    return (
      <div
        className={`p-4 bg-red-50 border border-red-200 rounded-md ${className}`}
      >
        <p className="text-red-600">{error}</p>
        <button
          type="button"
          onClick={() => window.location.reload()}
          className="mt-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
        >
          Réessayer
        </button>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`} style={{ height: '600px' }}>
      {isLoading && (
        <div className="w-full h-full flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500" />
        </div>
      )}
      <div
        id="tradingview-screener"
        className="tradingview-widget-container"
        ref={container}
        style={{
          height: '100%',
          width: '100%',
          display: isLoading ? 'none' : 'block',
        }}
      >
        <div
          className="tradingview-widget-container__widget"
          style={{
            height: 'calc(100% - 32px)',
            width: '100%',
            minHeight: '500px',
          }}
        />
        <div className="tradingview-widget-copyright">
          <a
            href="https://www.tradingview.com/"
            rel="noopener nofollow"
            target="_blank"
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            Suivez tous les marchés sur TradingView
          </a>
        </div>
      </div>
    </div>
  );
}

export default memo(StockScreener);
