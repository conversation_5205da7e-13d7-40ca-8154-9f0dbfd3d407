import { z } from 'zod';
import {
  generateObject,
  type LanguageModel,
  type UIMessageStreamWriter,
} from 'ai';
import type { DestinationInfo } from '../types';
import type { ChatMessage } from '@/lib/types';
import { web_search } from '@/lib/ai/tools/web-search';

/**
 * DestinationAgent is responsible for extracting and validating destination information
 * from the user query, including destination name, country, and duration.
 */
export class DestinationAgent {
  private model: LanguageModel;

  constructor(model: LanguageModel) {
    this.model = model;
  }

  /**
   * Extract destination information from the user query
   */
  async extractDestinationInfo(
    query: string,
    dataStream?: UIMessageStreamWriter<ChatMessage>,
  ): Promise<DestinationInfo> {
    try {
      console.log('Extracting destination info from query:', query);

      // Stream progress update
      if (dataStream) {
        console.log(
          '🔄 [DestinationAgent] Streaming: Starting destination analysis',
        );
      }

      // 1. Extract basic destination info from the query
      const { object: extractedInfo } = await generateObject({
        model: this.model,
        system: `You are an expert travel agent with advanced natural language understanding and multilingual capabilities.

        Your task is to intelligently extract destination and duration from travel queries in any language using your understanding of context, semantics, and travel patterns.

        🚨 CRITICAL: The user's duration request is ABSOLUTE and MUST be respected exactly. If they say "2 jours", you MUST extract exactly 2, not 3 or any other number.

        🧠 INTELLIGENT DURATION EXTRACTION - Use your natural language understanding:

        📅 NUMERIC EXPRESSIONS (any language) - EXTRACT EXACTLY:
        - "2 jours", "3 days", "5 giorni", "4 días", "2 дня" → extract the EXACT number (2, 3, 5, 4, 2)
        - "deux jours", "three days", "cinco días", "zwei Tage" → convert written numbers to digits EXACTLY
        - "un jour", "one day", "un día" → EXACTLY 1 day
        - "une journée", "a day" → EXACTLY 1 day

        📅 TIME PERIODS (multilingual understanding) - BE PRECISE:
        - "week-end", "weekend", "fin de semana" → EXACTLY 2 days (not 2-3, exactly 2)
        - "long week-end", "long weekend" → EXACTLY 3 days
        - week/semaine/settimana/semana → EXACTLY 7 days
        - month/mois/mese/mes → EXACTLY 30 days
        - fortnight/quinzaine → EXACTLY 14 days

        📅 CONTEXTUAL INTELLIGENCE - When duration is EXPLICITLY stated, use it EXACTLY:
        - "2 jours à Rome" → duration: 2 (EXACTLY 2, not 3)
        - "3 days in Paris" → duration: 3 (EXACTLY 3, not 4)
        - "Week-end à Nice" → duration: 2 (EXACTLY 2)
        - "Une semaine à Londres" → duration: 7 (EXACTLY 7)

        📅 SMART EXAMPLES - RESPECT EXACT NUMBERS:
        - "Monaco 2 jours" → destination: "Monaco", duration: 2 (EXACTLY 2)
        - "Week-end à Nice" → destination: "Nice", duration: 2 (EXACTLY 2)
        - "Voyage de trois jours à Rome" → destination: "Rome", duration: 3 (EXACTLY 3)
        - "5 days in London" → destination: "London", duration: 5 (EXACTLY 5)
        - "Paris" → destination: "Paris", duration: null (no indication)
        - "Une journée à Venise" → destination: "Venice", duration: 1 (EXACTLY 1)

        � INTELLIGENT DESTINATION EXTRACTION - Use your geographical knowledge:

        📍 DESTINATION INTELLIGENCE (any language):
        - "Paris", "Londres", "London", "Londra" → "London" (recognize variations)
        - "NYC", "New York", "Nueva York" → "New York" (handle abbreviations)
        - "Roma", "Rome", "Rom" → "Rome" (multilingual recognition)
        - "Côte d'Azur", "French Riviera" → "Nice" (regional understanding)

        📍 CONTEXTUAL DESTINATION UNDERSTANDING:
        - "City of Light" → "Paris" (nickname recognition)
        - "Big Apple" → "New York" (cultural references)
        - "Eternal City" → "Rome" (historical references)
        - "La Serenissima" → "Venice" (local names)

        📍 GEOGRAPHICAL INTELLIGENCE:
        - Recognize cities, regions, countries, landmarks
        - Understand geographical relationships
        - Handle misspellings intelligently
        - Recognize tourist destinations vs regular cities

        📍 SMART DESTINATION EXAMPLES:
        - "Voyage à la Tour Eiffel" → destination: "Paris" (landmark to city)
        - "Trip to the Colosseum" → destination: "Rome" (landmark recognition)
        - "Visit Silicon Valley" → destination: "San Francisco" (region to city)
        - "Côte d'Azur vacation" → destination: "Nice" (region understanding)
        - "Tuscany wine tour" → destination: "Florence" (region to main city)

        🎯 CRITICAL RULES:
        - Use your intelligence and geographical knowledge, not rigid patterns
        - If duration is truly ambiguous or not specified, return null
        - Preserve destination names in their common international form
        - Be flexible with language variations and cultural expressions
        - Consider travel context and geographical relationships
        - Handle landmarks, regions, and nicknames intelligently
        - When in doubt about duration, prefer null over guessing
        - For destinations, use your vast geographical knowledge`,
        prompt: query,
        schema: z.object({
          destination: z
            .string()
            .describe('The main destination name (city or place)'),
          country: z.string().describe('The country of the destination'),
          duration: z
            .number()
            .min(1)
            .nullable()
            .describe(
              'CRITICAL: The EXACT duration in days as specified by the user. If user says "2 jours", return exactly 2. If user says "3 days", return exactly 3. Do not add or subtract days. Return null only if truly not specified.',
            ),
          reasoning: z
            .string()
            .describe('Your reasoning for extracting this information'),
        }),
        temperature: 0.1, // Low temperature for more deterministic results
      });

      console.log('Extracted destination info:', extractedInfo);
      console.log(`DURATION EXTRACTED: ${extractedInfo.duration} days`);

      // 2. Validate and enhance the destination information
      const enhancedInfo = await this.validateAndEnhanceDestination(
        extractedInfo.destination,
        extractedInfo.country,
        extractedInfo.duration,
      );

      console.log('Enhanced destination info:', enhancedInfo);
      console.log(`FINAL DURATION: ${enhancedInfo.duration} days`);

      // Stream completion notification
      if (dataStream) {
        console.log(
          '🔄 [DestinationAgent] Streaming: Destination extraction completed',
        );
      }

      return enhancedInfo;
    } catch (error) {
      console.error('Error extracting destination info:', error);

      // Stream fallback notification
      if (dataStream) {
        console.log(
          '🔄 [DestinationAgent] Streaming: Using intelligent fallback method',
        );
      }

      // Try to extract destination and duration using intelligent fallback
      const { destination, duration } =
        await this.extractDestinationIntelligentFallback(query);

      console.log(
        `FALLBACK DURATION EXTRACTED: ${duration !== null ? duration : 'none'}`,
      );

      // Fallback to default destination but keep duration as null if not specified
      const fallbackResult = {
        destination: destination || 'Paris',
        country: 'France',
        duration: duration, // Keep as null if not specified
        coordinates: {
          lat: '48.8566',
          lng: '2.3522',
        },
      };

      console.log(`FALLBACK FINAL DURATION: ${fallbackResult.duration} days`);
      return fallbackResult;
    }
  }

  /**
   * Intelligent fallback method using LLM to extract destination and duration
   * @returns An object with destination and duration if found, or null values if not found
   */
  private async extractDestinationIntelligentFallback(query: string): Promise<{
    destination: string | null;
    duration: number | null;
  }> {
    try {
      console.log('Using intelligent LLM fallback for extraction...');

      // Use a simpler, more robust LLM approach for fallback
      const { object: fallbackInfo } = await generateObject({
        model: this.model,
        system: `You are a smart travel assistant. Extract destination and duration from this travel query.

        🚨 CRITICAL: If the user specifies a number of days, extract it EXACTLY. Do not modify, round, or adjust the number.

        STRICT RULES:
        - If you see a clear destination name, extract it
        - If you see a clear duration (number + time unit), extract the EXACT number
        - "2 jours" = exactly 2, "3 days" = exactly 3, "week-end" = exactly 2
        - If anything is unclear or ambiguous, return null
        - Don't guess or assume anything
        - NEVER add extra days "for flexibility" or "just in case"

        EXAMPLES:
        - "Monaco 2 jours" → destination: "Monaco", duration: 2 (EXACTLY 2)
        - "Paris" → destination: "Paris", duration: null
        - "Week-end Nice" → destination: "Nice", duration: 2 (EXACTLY 2)
        - "3 days in Rome" → destination: "Rome", duration: 3 (EXACTLY 3)
        - "Trip to Rome" → destination: "Rome", duration: null`,
        prompt: `Extract destination and duration from: "${query}"`,
        schema: z.object({
          destination: z
            .string()
            .nullable()
            .describe('Destination name or null'),
          duration: z.number().nullable().describe('Duration in days or null'),
          confidence: z
            .string()
            .describe('Your confidence level: high/medium/low'),
        }),
        temperature: 0.1,
      });

      console.log('LLM Fallback result:', fallbackInfo);

      // Only use results with high confidence
      if (fallbackInfo.confidence === 'high') {
        return {
          destination: fallbackInfo.destination,
          duration: fallbackInfo.duration,
        };
      } else {
        // If fallback LLM confidence is low, we could potentially try a simpler prompt,
        // but for now, let's remove the static list fallback and just return null/default.
        console.log('LLM fallback confidence too low, returning null/default.');
        return { destination: null, duration: null }; // Rely solely on LLM fallbacks or return null
      }
    } catch (error) {
      console.error('Error in intelligent fallback:', error);
      // If LLM fallback itself fails, return null/default.
      console.log('LLM fallback failed, returning null/default.');
      return { destination: null, duration: null };
    }
  }

  private async validateAndEnhanceDestination(
    destination: string,
    country: string,
    duration: number | null,
  ): Promise<DestinationInfo> {
    try {
      // 1. Search for destination information to validate
      const searchQuery = `${destination} ${country} travel information coordinates`;
      const searchResults = await this.searchDestinationInfo(searchQuery);

      // If duration is null, we need a different approach
      if (duration === null) {
        // 2. Use the search results to validate and enhance the destination info without duration
        const { object: validatedInfo } = await generateObject({
          model: this.model,
          system: `You are an expert travel agent with extensive knowledge of global destinations.
          Validate and enhance the destination information using the search results.
          Provide accurate coordinates for the destination.
          If the search results don't contain enough information, use your knowledge to provide the best estimate.

          CRITICAL: The user has not specified a duration for their trip. Keep the duration as null.`,
          prompt: `Destination: ${destination}
          Country: ${country}
          Duration: Not specified by the user

          Search Results:
          ${JSON.stringify(searchResults, null, 2)}

          Please validate and enhance this destination information.
          IMPORTANT: The user has not specified a duration for their trip. Keep the duration as null.`,
          schema: z.object({
            destination: z.string().describe('The validated destination name'),
            country: z.string().describe('The validated country name'),
            duration: z
              .number()
              .nullable()
              // Modified description to avoid potential API schema interpretation issue
              .describe('The duration in days, or null if not specified'),
            coordinates: z.object({
              lat: z.string().describe('Latitude of the destination'),
              lng: z.string().describe('Longitude of the destination'),
            }),
          }),
        });

        return validatedInfo;
      } else {
        // 2. Use the search results to validate and enhance the destination info with duration
        const { object: validatedInfo } = await generateObject({
          model: this.model,
          system: `You are an expert travel agent with extensive knowledge of global destinations.
          Validate and enhance the destination information using the search results.
          Provide accurate coordinates for the destination.
          If the search results don't contain enough information, use your knowledge to provide the best estimate.

          CRITICAL: You MUST preserve the exact duration that was extracted from the user's query.`,
          prompt: `Destination: ${destination}
          Country: ${country}
          Duration: ${duration} days

          Search Results:
          ${JSON.stringify(searchResults, null, 2)}

          Please validate and enhance this destination information.
          IMPORTANT: You MUST keep the duration EXACTLY as provided (${duration} days) - do not change it under any circumstances.
          The duration of ${duration} days was explicitly requested by the user and must be preserved.`,
          schema: z.object({
            destination: z.string().describe('The validated destination name'),
            country: z.string().describe('The validated country name'),
            duration: z
              .number()
              .int()
              .positive()
              // Modified description to avoid potential API schema interpretation issue
              .describe('The duration in days'),
            coordinates: z.object({
              lat: z.string().describe('Latitude of the destination'),
              lng: z.string().describe('Longitude of the destination'),
            }),
          }),
        });

        return validatedInfo;
      }
    } catch (error) {
      console.error('Error validating destination:', error);

      // Return the original info with default coordinates if validation fails
      return {
        destination,
        country,
        duration,
        coordinates: {
          lat: '0',
          lng: '0',
        },
      };
    }
  }

  /**
   * Search for destination information using the web search tool
   */
  private async searchDestinationInfo(query: string): Promise<any> {
    try {
      // Create a mock session and dataStream for the web_search tool
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
      // Create a mock data stream object
      const mockDataStream = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      };

      // Use the web_search tool to get information about the destination
      const searchTool = web_search({
        session: mockSession,
        dataStream: mockDataStream,
      });
      const results = await (searchTool.execute as any)({
        queries: [query],
        maxResults: [5],
        topics: ['general'],
      });

      return results;
    } catch (error) {
      console.error('Error searching destination info:', error);
      return { results: [] };
    }
  }
}
