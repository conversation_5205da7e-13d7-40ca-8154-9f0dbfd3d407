/**
 * Système de récupération de contenu avec fallback Firecrawl
 * Amélioration pour extreme-search
 */

import { Exa } from 'exa-js';
import FirecrawlApp from '@mendable/firecrawl-js';
import { extractFavicon } from '@/lib/utils/favicon-extractor';

// ============================================================================
// TYPES
// ============================================================================

export type SearchResult = {
  title: string;
  url: string;
  content: string;
  publishedDate: string;
  favicon: string;
};

// ============================================================================
// CONFIGURATION
// ============================================================================

const EXA_API_KEY = process.env.EXA_API_KEY;
const FIRECRAWL_API_KEY = process.env.FIRECRAWL_API_KEY;

let exa: Exa | null = null;
let firecrawl: FirecrawlApp | null = null;

if (EXA_API_KEY) {
  try {
    exa = new Exa(EXA_API_KEY);
    console.log('✅ Exa API initialisée');
  } catch (error) {
    console.error('❌ Erreur initialisation Exa:', error);
  }
}

if (FIRECRAWL_API_KEY) {
  try {
    firecrawl = new FirecrawlApp({ apiKey: FIRECRAWL_API_KEY });
    console.log('✅ Firecrawl API initialisée');
  } catch (error) {
    console.error('❌ Erreur initialisation Firecrawl:', error);
  }
}

// ============================================================================
// FONCTIONS PRINCIPALES
// ============================================================================

/**
 * Récupère le contenu des URLs avec système de fallback intelligent
 * 1. Essaie avec Exa d'abord
 * 2. Si échec, utilise Firecrawl
 */
export async function getContentsWithFallback(
  links: string[],
): Promise<SearchResult[]> {
  console.log(`📄 Récupération du contenu de ${links.length} URLs`);

  if (!links || links.length === 0) {
    console.error('❌ Aucun lien fourni');
    return [];
  }

  const results: SearchResult[] = [];
  const failedUrls: string[] = [];

  // ========================================
  // ÉTAPE 1: Tentative avec Exa
  // ========================================

  if (exa) {
    try {
      console.log(`🔍 Tentative Exa pour ${links.length} URLs...`);

      const result = await exa.getContents(links, {
        text: {
          maxCharacters: 3000,
          includeHtmlTags: false,
        },
        livecrawl: 'preferred' as any,
      });

      if (result?.results) {
        for (const r of result.results) {
          // Vérifier si le contenu est valide
          if (r.text && r.text.trim().length > 50) {
            const favicon = await extractFaviconSafe(r.url);

            results.push({
              title: r.title || extractTitleFromUrl(r.url),
              url: r.url,
              content: r.text,
              publishedDate: r.publishedDate || new Date().toISOString(),
              favicon,
            });
          } else {
            // Contenu invalide ou trop court
            failedUrls.push(r.url);
          }
        }

        // Ajouter les URLs manquantes
        const exaUrls = result.results.map((r: any) => r.url);
        const missingUrls = links.filter((url) => !exaUrls.includes(url));
        failedUrls.push(...missingUrls);

        console.log(
          `✅ Exa: ${results.length} succès, ${failedUrls.length} échecs`,
        );
      } else {
        console.warn('⚠️ Réponse Exa invalide');
        failedUrls.push(...links);
      }
    } catch (error) {
      console.error('❌ Erreur Exa:', error);
      failedUrls.push(...links);
    }
  } else {
    console.warn('⚠️ Exa non disponible, passage direct à Firecrawl');
    failedUrls.push(...links);
  }

  // ========================================
  // ÉTAPE 2: Fallback avec Firecrawl
  // ========================================

  if (failedUrls.length > 0 && firecrawl) {
    console.log(`🔄 Fallback Firecrawl pour ${failedUrls.length} URLs...`);

    for (const url of failedUrls) {
      try {
        const scrapeResponse = await firecrawl.scrape(url, {
          formats: ['markdown'],
        });

        if (scrapeResponse?.markdown) {
          const favicon = await extractFaviconSafe(url);

          results.push({
            title: scrapeResponse.metadata?.title || extractTitleFromUrl(url),
            url: url,
            content: scrapeResponse.markdown.slice(0, 3000),
            publishedDate:
              (scrapeResponse.metadata?.publishedDate as string) ||
              new Date().toISOString(),
            favicon,
          });

          console.log(`✅ Firecrawl succès: ${url}`);
        } else {
          console.warn(`⚠️ Firecrawl échec: ${url}`);
        }
      } catch (firecrawlError) {
        console.error(`❌ Erreur Firecrawl pour ${url}:`, firecrawlError);
      }
    }
  } else if (failedUrls.length > 0) {
    console.warn(
      `⚠️ ${failedUrls.length} URLs n'ont pas pu être récupérées (Firecrawl non disponible)`,
    );
  }

  console.log(`✅ Total: ${results.length}/${links.length} contenus récupérés`);

  return results;
}

/**
 * Recherche web avec Exa
 */
export async function searchWebWithExa(
  query: string,
  options: {
    numResults?: number;
    category?: string;
    includeDomains?: string[];
  } = {},
): Promise<SearchResult[]> {
  const { numResults = 8, category, includeDomains } = options;

  console.log(`🔍 Recherche web: "${query}"`);

  if (!exa) {
    console.error('❌ Exa non disponible');
    return [];
  }

  try {
    const searchOptions: any = {
      numResults,
      type: 'auto',
    };

    if (category) {
      searchOptions.category = category;
    }

    if (includeDomains && includeDomains.length > 0) {
      searchOptions.includeDomains = includeDomains;
    }

    const { results } = await exa.searchAndContents(query, searchOptions);

    console.log(`✅ ${results.length} résultats trouvés`);

    const mappedResults = await Promise.all(
      results.map(async (r: any) => {
        const favicon = await extractFaviconSafe(r.url);

        return {
          title: r.title || extractTitleFromUrl(r.url),
          url: r.url,
          content: r.text || '',
          publishedDate: r.publishedDate || new Date().toISOString(),
          favicon,
        };
      }),
    );

    return mappedResults;
  } catch (error) {
    console.error('❌ Erreur searchWeb:', error);
    return [];
  }
}

// ============================================================================
// FONCTIONS UTILITAIRES
// ============================================================================

/**
 * Extrait le favicon de manière sécurisée
 */
async function extractFaviconSafe(url: string): Promise<string> {
  try {
    if (!url || url.includes('example.com')) {
      return getDefaultFavicon(url);
    }

    const favicon = await extractFavicon(url);
    return favicon;
  } catch (error) {
    return getDefaultFavicon(url);
  }
}

/**
 * Retourne le favicon par défaut
 */
function getDefaultFavicon(url: string): string {
  try {
    const hostname = new URL(url).hostname;
    return `https://www.google.com/s2/favicons?domain=${hostname}&sz=128`;
  } catch {
    return 'https://www.google.com/favicon.ico';
  }
}

/**
 * Extrait un titre depuis l'URL
 */
function extractTitleFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/').filter((p) => p);
    const lastPart = pathParts[pathParts.length - 1] || urlObj.hostname;

    return lastPart
      .replace(/[-_]/g, ' ')
      .replace(/\.[^.]+$/, '')
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  } catch {
    return 'Contenu Récupéré';
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export { exa, firecrawl };
