import React, {
  useMemo,
  useState,
  memo,
  useEffect,
  useCallback,
  useRef,
} from 'react';
import { useTheme } from 'next-themes';
import {
  Line,
  LineChart,
  CartesianGrid,
  XAxis,
  YAxis,
  ResponsiveContainer,
  Area,
  AreaChart,
  Tooltip,
} from 'recharts';
import {
  Card,
  CardHeader,
  CardContent,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import type { ChartConfig } from '@/components/ui/chart';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Cloud, Droplets, Wind, Gauge } from 'lucide-react';
import Image from 'next/image';
import WeatherMapIntegrated from './weather-map-integrated';

// Interfaces
interface WeatherDataPoint {
  date: string;
  timestamp: number;
  minTemp: number;
  maxTemp: number;
  temp: number;
  feelsLike: number;
  humidity: number;
  windSpeed: number;
  description: string;
  icon: string;
  pressure: number;
  clouds: number;
  pop: number;
  hour: number;
  visibility: number | null;
}

interface AirPollutionData {
  dt: number;
  main: {
    aqi: number;
  };
  components: {
    co: number;
    no: number;
    no2: number;
    o3: number;
    so2: number;
    pm2_5: number;
    pm10: number;
    nh3: number;
  };
}

interface DailyForecastData {
  dt: number;
  sunrise: number;
  sunset: number;
  temp: {
    day: number;
    min: number;
    max: number;
    night: number;
    eve: number;
    morn: number;
  };
  feels_like: {
    day: number;
    night: number;
    eve: number;
    morn: number;
  };
  pressure: number;
  humidity: number;
  weather: Array<{
    id: number;
    main: string;
    description: string;
    icon: string;
  }>;
  speed: number;
  deg: number;
  clouds: number;
  pop: number;
  rain?: number;
  snow?: number;
}

interface ProcessedDailyForecast {
  date: string;
  dateFormatted: string;
  timestamp: number;
  day: number;
  dayOfWeek: string;
  minTemp: number;
  maxTemp: number;
  dayTemp: number;
  nightTemp: number;
  humidity: number;
  windSpeed: number;
  description: string;
  icon: string;
  pop: number;
  rain?: number;
  snow?: number;
}

interface WeatherChartProps {
  result: any;
}

// Utility functions
const convertWindSpeed = (speed: number): number => {
  return Math.round(speed * 3.6);
};

const getWeatherIconUrl = (iconCode: string): string => {
  return `https://openweathermap.org/img/wn/${iconCode}@2x.png`;
};

const formatTime = (timestamp: number): string => {
  return new Date(timestamp * 1000).toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
  });
};

const formatDate = (date: string): string => {
  return new Date(date).toLocaleDateString(undefined, {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
  });
};

const formatVisibility = (visibility: number | null): string => {
  if (visibility === null || visibility === undefined) {
    return 'N/A';
  }
  const visibilityKm = visibility / 1000;
  if (visibilityKm >= 10) {
    return '10+ km';
  } else if (visibilityKm >= 1) {
    return `${visibilityKm.toFixed(1)} km`;
  } else {
    return `${visibility} m`;
  }
};

const getDayLabel = (timestamp: number, index: number): string => {
  const date = new Date(timestamp * 1000);
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);

  const dateOnly = new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
  );
  const todayOnly = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate(),
  );
  const tomorrowOnly = new Date(
    tomorrow.getFullYear(),
    tomorrow.getMonth(),
    tomorrow.getDate(),
  );

  if (dateOnly.getTime() === todayOnly.getTime()) {
    return "Aujourd'hui";
  } else if (dateOnly.getTime() === tomorrowOnly.getTime()) {
    return 'Demain';
  } else {
    return date.toLocaleDateString('fr-FR', { weekday: 'short' });
  }
};

const getAirQualityInfo = (
  aqi: number,
): { label: string; colorClass: string } => {
  switch (aqi) {
    case 1:
      return {
        label: 'Good',
        colorClass:
          'bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-300',
      };
    case 2:
      return {
        label: 'Fair',
        colorClass:
          'bg-lime-50 text-lime-700 dark:bg-lime-900/30 dark:text-lime-300',
      };
    case 3:
      return {
        label: 'Moderate',
        colorClass:
          'bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300',
      };
    case 4:
      return {
        label: 'Poor',
        colorClass:
          'bg-orange-50 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300',
      };
    case 5:
      return {
        label: 'Very Poor',
        colorClass:
          'bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-300',
      };
    default:
      return {
        label: 'Unknown',
        colorClass:
          'bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300',
      };
  }
};

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="custom-tooltip bg-white dark:bg-neutral-800 p-2 border border-neutral-200 dark:border-neutral-700 rounded-md text-xs shadow-sm">
        <p className="mb-1 font-medium">{label}</p>
        {payload.map((entry: any) => (
          <p
            key={entry.dataKey ?? entry.name ?? `${entry.color}-${entry.value}`}
            style={{ color: entry.color }}
            className="flex items-center gap-1"
          >
            <span
              className="w-2 h-2 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span>
              {entry.name}: {entry.value}
              {entry.dataKey === 'precipitation' ||
              entry.name === 'Precipitation'
                ? '%'
                : '°C'}
            </span>
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const WeatherChart: React.FC<WeatherChartProps> = memo(({ result }) => {
  const { theme, resolvedTheme } = useTheme();
  const [selectedDay, setSelectedDay] = useState<string>('');
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const [forceUpdate, setForceUpdate] = useState<number>(0);

  const isDark = resolvedTheme === 'dark' || theme === 'dark';
  const gridColor = isDark
    ? 'rgba(115, 115, 115, 0.3)'
    : 'rgba(148, 163, 184, 0.2)';
  const axisColor = isDark ? '#a3a3a3' : '#9CA3AF';
  const textColor = isDark ? '#d4d4d4' : '#6b7280';

  // Handle container resize
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Set initial width
    setContainerWidth(container.clientWidth);

    // Create resize observer
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setContainerWidth(entry.contentRect.width);
        // Force re-render of charts when container size changes
        setForceUpdate((prev) => prev + 1);

        // Dispatch a custom event for chart components to listen to
        window.dispatchEvent(
          new CustomEvent('containerResize', {
            detail: { width: entry.contentRect.width },
          }),
        );
      }
    });

    resizeObserver.observe(container);

    // Cleanup
    return () => {
      resizeObserver.unobserve(container);
    };
  }, []);

  const {
    chartData,
    hourlyDataByDay,
    currentWeather,
    minTemp,
    maxTemp,
    days,
    airPollution,
    airPollutionForecast,
    dailyForecast,
    isValidData,
    errorMessage,
  } = useMemo(() => {
    if (!result || typeof result !== 'object') {
      return {
        chartData: [],
        hourlyDataByDay: {} as { [key: string]: WeatherDataPoint[] },
        currentWeather: null,
        minTemp: 0,
        maxTemp: 0,
        days: [],
        airPollution: null,
        airPollutionForecast: [],
        dailyForecast: [],
        isValidData: false,
        errorMessage: 'Weather data is not available',
      };
    }

    if (result.error) {
      return {
        chartData: [],
        hourlyDataByDay: {} as { [key: string]: WeatherDataPoint[] },
        currentWeather: null,
        minTemp: 0,
        maxTemp: 0,
        days: [],
        airPollution: null,
        airPollutionForecast: [],
        dailyForecast: [],
        isValidData: false,
        errorMessage: result.error,
      };
    }

    if (
      !result.list ||
      !Array.isArray(result.list) ||
      result.list.length === 0
    ) {
      return {
        chartData: [],
        hourlyDataByDay: {} as { [key: string]: WeatherDataPoint[] },
        currentWeather: null,
        minTemp: 0,
        maxTemp: 0,
        days: [],
        airPollution: null,
        airPollutionForecast: [],
        dailyForecast: [],
        isValidData: false,
        errorMessage: 'No weather forecast data available',
      };
    }

    const weatherData = result.list.map((item: any) => {
      const date = new Date(item.dt * 1000);
      const dateKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

      return {
        date: dateKey,
        timestamp: item.dt,
        hour: date.getHours(),
        minTemp: Number((item.main.temp_min - 273.15).toFixed(1)),
        maxTemp: Number((item.main.temp_max - 273.15).toFixed(1)),
        temp: Number((item.main.temp - 273.15).toFixed(1)),
        feelsLike: Number((item.main.feels_like - 273.15).toFixed(1)),
        humidity: item.main.humidity,
        windSpeed: convertWindSpeed(item.wind.speed),
        description: item.weather[0].description,
        icon: item.weather[0].icon,
        pressure: item.main.pressure,
        clouds: item.clouds.all,
        pop: Math.round(item.pop * 100),
        visibility: item.visibility || null,
      };
    });

    const airPollution = result.air_pollution?.list?.[0] || null;

    const airPollutionForecast =
      result.air_pollution_forecast?.list?.map((item: AirPollutionData) => {
        return {
          ...item,
          dateTime: new Date(item.dt * 1000),
          date: new Date(item.dt * 1000).toLocaleDateString(),
          hour: new Date(item.dt * 1000).getHours(),
        };
      }) || [];

    let dailyForecast: ProcessedDailyForecast[] = [];

    if (
      result.daily_forecast?.daily &&
      Array.isArray(result.daily_forecast.daily)
    ) {
      dailyForecast = result.daily_forecast.daily
        .map((item: DailyForecastData, index: number) => {
          const timestamp = item.dt;
          const date = new Date(timestamp * 1000);
          return {
            date: date.toLocaleDateString(),
            dateFormatted: formatDate(date.toLocaleDateString()),
            timestamp: timestamp,
            day: date.getDay(),
            dayOfWeek: getDayLabel(timestamp, index),
            minTemp: Number((item.temp.min - 273.15).toFixed(1)),
            maxTemp: Number((item.temp.max - 273.15).toFixed(1)),
            dayTemp: Number((item.temp.day - 273.15).toFixed(1)),
            nightTemp: Number((item.temp.night - 273.15).toFixed(1)),
            humidity: item.humidity,
            windSpeed: convertWindSpeed(item.speed),
            description: item.weather[0].description,
            icon: item.weather[0].icon,
            pop: Math.round(item.pop * 100),
            rain: item.rain,
            snow: item.snow,
          } as ProcessedDailyForecast;
        })
        .slice(0, 8);
    } else {
      const groupedByDate: { [key: string]: any[] } = {};
      weatherData.forEach((item: any) => {
        const dateKey = item.date;
        if (!groupedByDate[dateKey]) {
          groupedByDate[dateKey] = [];
        }
        groupedByDate[dateKey].push(item);
      });

      dailyForecast = Object.entries(groupedByDate)
        .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
        .map(([dateKey, items], index) => {
          const firstItem = items[0];
          const minTemp = Math.min(...items.map((item) => item.minTemp));
          const maxTemp = Math.max(...items.map((item) => item.maxTemp));
          const avgHumidity = Math.round(
            items.reduce((sum, item) => sum + item.humidity, 0) / items.length,
          );
          const avgWindSpeed = Math.round(
            items.reduce((sum, item) => sum + item.windSpeed, 0) / items.length,
          );
          const maxPop = Math.max(...items.map((item) => item.pop));

          return {
            date: dateKey,
            dateFormatted: formatDate(dateKey),
            timestamp: firstItem.timestamp,
            day: new Date(dateKey).getDay(),
            dayOfWeek: getDayLabel(firstItem.timestamp, index),
            minTemp,
            maxTemp,
            dayTemp: maxTemp,
            nightTemp: minTemp,
            humidity: avgHumidity,
            windSpeed: avgWindSpeed,
            description: firstItem.description,
            icon: firstItem.icon,
            pop: maxPop,
          } as ProcessedDailyForecast;
        });
    }

    const groupedData: { [key: string]: WeatherDataPoint } = weatherData.reduce(
      (acc: { [key: string]: WeatherDataPoint }, curr: WeatherDataPoint) => {
        if (!acc[curr.date]) {
          acc[curr.date] = { ...curr };
        } else {
          acc[curr.date].minTemp = Math.min(
            acc[curr.date].minTemp,
            curr.minTemp,
          );
          acc[curr.date].maxTemp = Math.max(
            acc[curr.date].maxTemp,
            curr.maxTemp,
          );

          if (curr.hour >= 12 && curr.hour <= 15) {
            acc[curr.date].description = curr.description;
            acc[curr.date].icon = curr.icon;
            acc[curr.date].temp = curr.temp;
          }
        }
        return acc;
      },
      {} as { [key: string]: WeatherDataPoint },
    );

    const chartData = Object.values(groupedData)
      .sort((a, b) => a.timestamp - b.timestamp)
      .map((item, index) => ({
        ...item,
        dayLabel: getDayLabel(item.timestamp, index),
      }));

    const hourlyDataByDay: { [key: string]: WeatherDataPoint[] } =
      weatherData.reduce(
        (
          acc: { [key: string]: WeatherDataPoint[] },
          curr: WeatherDataPoint,
        ) => {
          if (!acc[curr.date]) {
            acc[curr.date] = [];
          }
          acc[curr.date].push(curr);
          return acc;
        },
        {} as { [key: string]: WeatherDataPoint[] },
      );

    Object.keys(hourlyDataByDay).forEach((date) => {
      hourlyDataByDay[date].sort((a, b) => a.timestamp - b.timestamp);
    });

    const minTemp = Math.min(
      ...weatherData.map((d: WeatherDataPoint) => d.minTemp),
    );
    const maxTemp = Math.max(
      ...weatherData.map((d: WeatherDataPoint) => d.maxTemp),
    );
    const currentWeather = weatherData[0];
    const days = Object.keys(hourlyDataByDay).sort(
      (a, b) => new Date(a).getTime() - new Date(b).getTime(),
    );

    return {
      chartData,
      hourlyDataByDay,
      currentWeather,
      minTemp,
      maxTemp,
      days,
      airPollution,
      airPollutionForecast,
      dailyForecast,
      isValidData: true,
      errorMessage: null,
    };
  }, [result]);

  useEffect(() => {
    if (days.length > 0 && !selectedDay) {
      setSelectedDay(days[0]);
    }
  }, [days, selectedDay]);

  const chartConfig: ChartConfig = useMemo(
    () => ({
      minTemp: {
        label: 'Min Temp.',
        color: 'hsl(var(--chart-1))',
      },
      maxTemp: {
        label: 'Max Temp.',
        color: 'hsl(var(--chart-2))',
      },
    }),
    [],
  );

  const renderWeatherBadge = useCallback((description: string) => {
    const getColorClass = (desc: string) => {
      const lowerDesc = desc.toLowerCase();
      if (lowerDesc.includes('rain') || lowerDesc.includes('drizzle'))
        return 'bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300';
      if (lowerDesc.includes('cloud'))
        return 'bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300';
      if (lowerDesc.includes('clear'))
        return 'bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300';
      if (lowerDesc.includes('snow'))
        return 'bg-sky-50 text-sky-700 dark:bg-sky-900/30 dark:text-sky-300';
      if (lowerDesc.includes('thunder') || lowerDesc.includes('storm'))
        return 'bg-purple-50 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300';
      if (lowerDesc.includes('mist') || lowerDesc.includes('fog'))
        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300';
      return 'bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300';
    };

    return (
      <Badge
        className={`font-normal capitalize py-0.5 text-xs ${getColorClass(description)}`}
      >
        {description}
      </Badge>
    );
  }, []);

  if (!isValidData) {
    return (
      <Card className="my-2 py-4 shadow-none bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-800">
        <CardContent className="text-center">
          <p
            className={
              errorMessage?.includes('error')
                ? 'text-red-500 dark:text-red-400'
                : 'text-neutral-500 dark:text-neutral-400'
            }
          >
            {errorMessage}
          </p>
        </CardContent>
      </Card>
    );
  }

  // Add key prop to charts to force re-render when container width changes
  const chartKey = `chart-${containerWidth}-${forceUpdate}`;

  return (
    <div className="relative">
      <Card
        className="my-2 py-0 shadow-none bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-800 gap-0"
        ref={containerRef}
        style={{ containerType: 'inline-size' }}
      >
        <CardHeader className="py-2 px-3 sm:px-4">
          <div className="flex justify-between items-start w-full">
            <div className="flex-1 min-w-0">
              <CardTitle className="text-neutral-800 dark:text-neutral-100 text-base">
                {result.geocoding?.name || result.city.name}
              </CardTitle>
              <div className="flex flex-wrap items-center gap-2 mt-1">
                {renderWeatherBadge(currentWeather.description)}
                {currentWeather.pop > 0 && (
                  <Badge className="font-normal bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 py-0.5 text-xs whitespace-nowrap">
                    {currentWeather.pop}% rain
                  </Badge>
                )}
                <div className="flex items-center gap-2">
                  {airPollution && (
                    <Badge
                      className={`font-normal py-0.5 text-xs whitespace-nowrap ${getAirQualityInfo(airPollution.main.aqi).colorClass}`}
                    >
                      AQI: {getAirQualityInfo(airPollution.main.aqi).label}
                    </Badge>
                  )}
                  <span className="text-xs text-neutral-600 dark:text-neutral-400 whitespace-nowrap">
                    Visibilité: {formatVisibility(currentWeather.visibility)}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="text-right">
                <div className="text-2xl font-light text-neutral-800 dark:text-neutral-100">
                  {currentWeather.temp}°C
                </div>
                <div className="text-[10px] text-neutral-500 dark:text-neutral-400">
                  Ressenti {currentWeather.feelsLike}°C
                </div>
              </div>
              <div className="h-10 w-10 flex-shrink-0">
                <Image
                  src={getWeatherIconUrl(currentWeather.icon)}
                  alt={currentWeather.description}
                  className="h-8 w-8"
                  width={32}
                  height={32}
                  unoptimized
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 xs:gap-3 mt-4">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-2 border border-blue-200 dark:border-blue-800">
              <div className="flex flex-col items-center text-center">
                <Droplets className="h-5 w-5 text-blue-600 dark:text-blue-400 mb-1" />
                <p className="text-[10px] text-blue-700 dark:text-blue-400 font-medium">
                  Humidité
                </p>
                <p className="text-xs font-bold text-blue-900 dark:text-blue-100">
                  {currentWeather.humidity}%
                </p>
              </div>
            </div>
            <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-2 border border-green-200 dark:border-green-800">
              <div className="flex flex-col items-center text-center">
                <Wind className="h-5 w-5 text-green-600 dark:text-green-400 mb-1" />
                <p className="text-[10px] text-green-700 dark:text-green-400 font-medium">
                  Vent
                </p>
                <p className="text-xs font-bold text-green-900 dark:text-green-100">
                  {currentWeather.windSpeed} km/h
                </p>
              </div>
            </div>
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-2 border border-purple-200 dark:border-purple-800">
              <div className="flex flex-col items-center text-center">
                <Gauge className="h-5 w-5 text-purple-600 dark:text-purple-400 mb-1" />
                <p className="text-[10px] text-purple-700 dark:text-purple-400 font-medium">
                  Pression
                </p>
                <p className="text-xs font-bold text-purple-900 dark:text-purple-100">
                  {currentWeather.pressure} hPa
                </p>
              </div>
            </div>
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/20 dark:to-gray-800/20 rounded-lg p-2 border border-gray-200 dark:border-gray-800">
              <div className="flex flex-col items-center text-center">
                <Cloud className="h-5 w-5 text-gray-600 dark:text-gray-400 mb-1" />
                <p className="text-[10px] text-gray-700 dark:text-gray-400 font-medium">
                  Nuages
                </p>
                <p className="text-xs font-bold text-gray-900 dark:text-gray-100">
                  {currentWeather.clouds}%
                </p>
              </div>
            </div>
          </div>

          <div className="mt-3 pt-3 border-t border-neutral-200 dark:border-neutral-700" />
        </CardHeader>

        <CardContent className="p-0">
          <Tabs defaultValue="chart" className="w-full">
            <TabsList className="mx-0 sm:mx-2 flex flex-wrap justify-center gap-1 py-1 px-1 bg-transparent border-b border-neutral-200 dark:border-neutral-700 rounded-none">
              <TabsTrigger
                value="chart"
                className="text-[11px] px-2 py-1 whitespace-nowrap rounded-sm data-[state=active]:bg-blue-500 data-[state=active]:text-white"
              >
                5-Day
              </TabsTrigger>
              <TabsTrigger
                value="detailed"
                className="text-[11px] px-2 py-1 whitespace-nowrap rounded-sm data-[state=active]:bg-blue-500 data-[state=active]:text-white"
              >
                Hourly
              </TabsTrigger>
              <TabsTrigger
                value="daily"
                className="text-[11px] px-2 py-1 whitespace-nowrap rounded-sm data-[state=active]:bg-blue-500 data-[state=active]:text-white"
              >
                Extended
              </TabsTrigger>
              <TabsTrigger
                value="airquality"
                className="text-[11px] px-2 py-1 whitespace-nowrap rounded-sm data-[state=active]:bg-blue-500 data-[state=active]:text-white"
              >
                Air
              </TabsTrigger>
              <TabsTrigger
                value="map"
                className="text-[11px] px-2 py-1 whitespace-nowrap rounded-sm data-[state=active]:bg-blue-500 data-[state=active]:text-white"
              >
                Map
              </TabsTrigger>
            </TabsList>

            <TabsContent
              value="chart"
              className="pt-2 px-2 sm:px-4 pb-0 weather-chart-tab"
            >
              <div className="flex items-center justify-center gap-4 mb-2">
                <div className="flex items-center gap-1.5">
                  <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-[oklch(0.488_0.243_264.376)]" />
                  <span className="text-[9px] sm:text-[10px] text-neutral-600 dark:text-neutral-400">
                    Min Temperature
                  </span>
                </div>
                <div className="flex items-center gap-1.5">
                  <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-[oklch(0.696_0.17_162.48)]" />
                  <span className="text-[9px] sm:text-[10px] text-neutral-600 dark:text-neutral-400">
                    Max Temperature
                  </span>
                </div>
              </div>

              <div className="w-full h-[180px] sm:h-[200px] bg-transparent">
                <ResponsiveContainer width="100%" height="100%" key={chartKey}>
                  <LineChart
                    data={chartData}
                    margin={{ top: 10, right: 15, left: 10, bottom: 10 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
                    <XAxis
                      dataKey="dayLabel"
                      stroke={axisColor}
                      tick={{ fontSize: 9, fill: textColor }}
                      height={30}
                    />
                    <YAxis
                      domain={[Math.floor(minTemp) - 2, Math.ceil(maxTemp) + 2]}
                      tickFormatter={(value) => `${value}°C`}
                      stroke={axisColor}
                      tick={{ fontSize: 9, fill: textColor }}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: isDark ? '#1f1f1f' : '#ffffff',
                        border: `1px solid ${isDark ? '#404040' : '#e5e5e5'}`,
                        borderRadius: '6px',
                        color: isDark ? '#ffffff' : '#000000',
                      }}
                      labelStyle={{ color: isDark ? '#ffffff' : '#000000' }}
                    />
                    <Line
                      type="monotone"
                      dataKey="minTemp"
                      stroke="#3b82f6"
                      strokeWidth={2}
                      dot={false}
                      name="Min Temp."
                    />
                    <Line
                      type="monotone"
                      dataKey="maxTemp"
                      stroke="#10b981"
                      strokeWidth={2}
                      dot={false}
                      name="Max Temp."
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>

              <div className="mt-1 mb-0 w-full overflow-x-auto overflow-y-visible px-2 sm:px-4">
                <div className="grid grid-flow-col auto-cols-[minmax(0,1fr)] gap-1 w-max min-w-full">
                  {chartData.slice(0, 5).map((day) => (
                    <div
                      key={`${day.date}-${day.timestamp}`}
                      className="flex flex-col items-center justify-start p-0 m-0 rounded-lg bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 h-[95px] w-full text-center overflow-hidden"
                    >
                      <div className="text-[10px] font-medium text-neutral-800 dark:text-neutral-200 w-full whitespace-nowrap overflow-visible px-1 pt-1">
                        {day.dayLabel}
                      </div>
                      <div className="text-[9px] text-neutral-500 dark:text-neutral-400 w-full mt-0">
                        {new Date(day.date).toLocaleDateString(undefined, {
                          day: 'numeric',
                          month: 'short',
                        })}
                      </div>
                      <div className="flex items-center justify-center h-4 my-0.5">
                        <Image
                          src={getWeatherIconUrl(day.icon)}
                          alt={day.description}
                          className="h-3.5 w-3.5"
                          width={16}
                          height={16}
                          unoptimized
                        />
                      </div>
                      <div className="flex items-center justify-center gap-1 text-[10px] w-full mt-0.5">
                        <span className="font-medium text-rose-500 dark:text-rose-400">
                          {day.maxTemp}°
                        </span>
                        <span className="text-neutral-400 dark:text-neutral-500">
                          {day.minTemp}°
                        </span>
                      </div>
                      {day.pop > 20 && (
                        <div className="mt-1 flex items-center justify-center gap-0.5 text-[8px] text-blue-500 dark:text-blue-400 w-full">
                          <Droplets className="h-1.5 w-1.5" />
                          <span className="text-[9px]">{day.pop}%</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent
              value="detailed"
              className="px-2 sm:px-4 pb-2 weather-chart-tab"
            >
              <div className="mb-3">
                <div className="flex flex-wrap justify-center gap-1 py-1">
                  {days.map((day, index) => (
                    <button
                      type="button"
                      key={day}
                      onClick={() => setSelectedDay(day)}
                      className={`px-2 py-1 text-[10px] rounded-full transition-colors ${
                        selectedDay === day
                          ? 'bg-blue-50 text-blue-700 dark:bg-blue-800/50 dark:text-blue-200 font-medium'
                          : 'bg-neutral-100 text-neutral-600 hover:bg-neutral-200 dark:bg-neutral-800/40 dark:text-neutral-400 dark:hover:bg-neutral-800/60'
                      }`}
                    >
                      {index === 0
                        ? "Aujourd'hui"
                        : index === 1
                          ? 'Demain'
                          : new Date(day).toLocaleDateString('fr-FR', {
                              weekday: 'short',
                              day: 'numeric',
                            })}
                    </button>
                  ))}
                </div>
              </div>

              {selectedDay && hourlyDataByDay[selectedDay] && (
                <div className="mt-2">
                  <div className="flex items-center justify-center gap-4 mb-2">
                    <div className="flex items-center gap-1.5">
                      <div className="w-2 h-2 rounded-full bg-[#ff9500]" />
                      <span className="text-[10px] text-neutral-600 dark:text-neutral-400">
                        Temperature
                      </span>
                    </div>
                    <div className="flex items-center gap-1.5">
                      <div className="w-2 h-2 rounded-full bg-[#0ea5e9]" />
                      <span className="text-[10px] text-neutral-600 dark:text-neutral-400">
                        Precipitation
                      </span>
                    </div>
                  </div>

                  <ResponsiveContainer width="100%" height={200} key={chartKey}>
                    <AreaChart
                      data={hourlyDataByDay[selectedDay].map((item) => ({
                        ...item,
                        time: formatTime(item.timestamp),
                        precipitation: item.pop,
                      }))}
                      margin={{ top: 5, right: 5, left: 0, bottom: 0 }}
                    >
                      <defs>
                        <linearGradient
                          id="tempGradient"
                          x1="0"
                          y1="0"
                          x2="0"
                          y2="1"
                        >
                          <stop
                            offset="5%"
                            stopColor="#ff9500"
                            stopOpacity={0.3}
                          />
                          <stop
                            offset="95%"
                            stopColor="#ff9500"
                            stopOpacity={0}
                          />
                        </linearGradient>
                        <linearGradient
                          id="precipGradient"
                          x1="0"
                          y1="0"
                          x2="0"
                          y2="1"
                        >
                          <stop
                            offset="5%"
                            stopColor="#0ea5e9"
                            stopOpacity={0.3}
                          />
                          <stop
                            offset="95%"
                            stopColor="#0ea5e9"
                            stopOpacity={0}
                          />
                        </linearGradient>
                      </defs>
                      <CartesianGrid
                        strokeDasharray="3 3"
                        stroke="rgba(148, 163, 184, 0.2)"
                      />
                      <XAxis
                        dataKey="time"
                        tick={{ fontSize: 10 }}
                        stroke="#9CA3AF"
                      />
                      <YAxis
                        yAxisId="temp"
                        domain={[
                          Math.floor(minTemp) - 2,
                          Math.ceil(maxTemp) + 2,
                        ]}
                        tickFormatter={(value) => `${value}°C`}
                        tick={{ fontSize: 10 }}
                        stroke="#9CA3AF"
                      />
                      <YAxis
                        yAxisId="precip"
                        orientation="right"
                        domain={[0, 100]}
                        tickFormatter={(value) => `${value}%`}
                        tick={{ fontSize: 10 }}
                        stroke="#9CA3AF"
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Area
                        type="monotone"
                        dataKey="temp"
                        name="Temperature"
                        stroke="#ff9500"
                        fillOpacity={1}
                        fill="url(#tempGradient)"
                        yAxisId="temp"
                      />
                      <Area
                        type="monotone"
                        dataKey="precipitation"
                        name="Precipitation (%)"
                        stroke="#0ea5e9"
                        fillOpacity={1}
                        fill="url(#precipGradient)"
                        yAxisId="precip"
                      />
                    </AreaChart>
                  </ResponsiveContainer>

                  <div className="w-full overflow-x-auto">
                    <div
                      className="inline-grid grid-flow-col auto-cols-[minmax(0,1fr)] gap-1 mt-2"
                      style={{
                        width: '100%',
                        minWidth: '100%',
                        gridTemplateColumns: `repeat(${hourlyDataByDay[selectedDay].length}, minmax(0, 1fr))`,
                      }}
                    >
                      {hourlyDataByDay[selectedDay].map((item) => (
                        <div
                          key={item.timestamp}
                          className="flex flex-col items-center p-1 w-full"
                        >
                          <div className="text-[8px] text-neutral-500 dark:text-neutral-400 whitespace-nowrap">
                            {formatTime(item.timestamp)}
                          </div>
                          <Image
                            src={getWeatherIconUrl(item.icon)}
                            alt={item.description}
                            className="h-6 w-6"
                            width={24}
                            height={24}
                            unoptimized
                          />
                          <div className="text-[10px] font-medium text-neutral-800 dark:text-neutral-200">
                            {item.temp}°C
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent
              value="daily"
              className="px-2 sm:px-4 pb-2 weather-chart-tab"
            >
              {dailyForecast && dailyForecast.length > 0 ? (
                <div className="mt-2">
                  <div className="flex items-center justify-center gap-4 mb-2">
                    <div className="flex items-center gap-1.5">
                      <div className="w-2 h-2 rounded-full bg-[#ff9500]" />
                      <span className="text-[10px] text-neutral-600 dark:text-neutral-400">
                        Max Temp
                      </span>
                    </div>
                    <div className="flex items-center gap-1.5">
                      <div className="w-2 h-2 rounded-full bg-[#0ea5e9]" />
                      <span className="text-[10px] text-neutral-600 dark:text-neutral-400">
                        Min Temp
                      </span>
                    </div>
                    <div className="flex items-center gap-1.5">
                      <div className="w-2 h-2 rounded-full bg-[#6366f1]" />
                      <span className="text-[10px] text-neutral-600 dark:text-neutral-400">
                        Precipitation
                      </span>
                    </div>
                  </div>

                  <div className="h-[180px] sm:h-[200px] mb-4">
                    <ResponsiveContainer
                      width="100%"
                      height="100%"
                      key={chartKey}
                    >
                      <AreaChart
                        data={dailyForecast}
                        margin={{ top: 5, right: 5, left: 0, bottom: 5 }}
                      >
                        <defs>
                          <linearGradient
                            id="maxTempGradient"
                            x1="0"
                            y1="0"
                            x2="0"
                            y2="1"
                          >
                            <stop
                              offset="5%"
                              stopColor="#ff9500"
                              stopOpacity={0.3}
                            />
                            <stop
                              offset="95%"
                              stopColor="#ff9500"
                              stopOpacity={0}
                            />
                          </linearGradient>
                          <linearGradient
                            id="minTempGradient"
                            x1="0"
                            y1="0"
                            x2="0"
                            y2="1"
                          >
                            <stop
                              offset="5%"
                              stopColor="#0ea5e9"
                              stopOpacity={0.3}
                            />
                            <stop
                              offset="95%"
                              stopColor="#0ea5e9"
                              stopOpacity={0}
                            />
                          </linearGradient>
                          <linearGradient
                            id="precipGradient"
                            x1="0"
                            y1="0"
                            x2="0"
                            y2="1"
                          >
                            <stop
                              offset="5%"
                              stopColor="#6366f1"
                              stopOpacity={0.3}
                            />
                            <stop
                              offset="95%"
                              stopColor="#6366f1"
                              stopOpacity={0}
                            />
                          </linearGradient>
                        </defs>
                        <CartesianGrid
                          strokeDasharray="3 3"
                          stroke="rgba(148, 163, 184, 0.2)"
                        />
                        <XAxis
                          dataKey="timestamp"
                          tickFormatter={(value) => {
                            const date = new Date(value * 1000);
                            const today = new Date();
                            const dateOnly = new Date(
                              date.getFullYear(),
                              date.getMonth(),
                              date.getDate(),
                            );
                            const todayOnly = new Date(
                              today.getFullYear(),
                              today.getMonth(),
                              today.getDate(),
                            );
                            if (dateOnly.getTime() === todayOnly.getTime()) {
                              return "Aujourd'hui";
                            }
                            return date
                              .toLocaleDateString('fr-FR', {
                                weekday: 'short',
                                day: 'numeric',
                              })
                              .replace('.', '');
                          }}
                          tick={{ fontSize: 10 }}
                          stroke="#9CA3AF"
                          interval={0}
                          angle={-45}
                          textAnchor="end"
                          height={50}
                        />
                        <YAxis
                          yAxisId="temp"
                          domain={[
                            Math.floor(
                              Math.min(
                                ...dailyForecast.map(
                                  (d: ProcessedDailyForecast) => d.minTemp,
                                ),
                              ) - 2,
                            ),
                            Math.ceil(
                              Math.max(
                                ...dailyForecast.map(
                                  (d: ProcessedDailyForecast) => d.maxTemp,
                                ),
                              ) + 2,
                            ),
                          ]}
                          tickFormatter={(value) => `${value}°C`}
                          tick={{ fontSize: 10 }}
                          stroke="#9CA3AF"
                        />
                        <YAxis
                          yAxisId="precip"
                          orientation="right"
                          domain={[0, 100]}
                          tickFormatter={(value) => `${value}%`}
                          tick={{ fontSize: 10 }}
                          stroke="#9CA3AF"
                        />
                        <Tooltip
                          content={({ active, payload }) => {
                            if (active && payload && payload.length) {
                              const data = payload[0].payload;
                              return (
                                <div className="custom-tooltip bg-white dark:bg-neutral-800 p-2 border border-neutral-200 dark:border-neutral-700 rounded-md text-xs shadow-sm">
                                  <p className="mb-1 font-medium">
                                    {new Date(
                                      data.timestamp * 1000,
                                    ).toLocaleDateString(undefined, {
                                      weekday: 'long',
                                      month: 'short',
                                      day: 'numeric',
                                    })}
                                  </p>
                                  <div className="space-y-1">
                                    <p className="flex items-center justify-between">
                                      <span className="flex items-center gap-1">
                                        <span className="w-2 h-2 rounded-full bg-[#ff9500]" />
                                        Max Temperature
                                      </span>
                                      <span className="font-medium">
                                        {data.maxTemp}°C
                                      </span>
                                    </p>
                                    <p className="flex items-center justify-between">
                                      <span className="flex items-center gap-1">
                                        <span className="w-2 h-2 rounded-full bg-[#0ea5e9]" />
                                        Min Temperature
                                      </span>
                                      <span className="font-medium">
                                        {data.minTemp}°C
                                      </span>
                                    </p>
                                    <p className="flex items-center justify-between">
                                      <span className="flex items-center gap-1">
                                        <span className="w-2 h-2 rounded-full bg-[#6366f1]" />
                                        Precipitation
                                      </span>
                                      <span className="font-medium">
                                        {data.pop}%
                                      </span>
                                    </p>
                                    {data.rain && (
                                      <p className="flex items-center justify-between text-neutral-600 dark:text-neutral-400">
                                        <span>Rain</span>
                                        <span>{data.rain} mm</span>
                                      </p>
                                    )}
                                    {data.snow && (
                                      <p className="flex items-center justify-between text-neutral-600 dark:text-neutral-400">
                                        <span>Snow</span>
                                        <span>{data.snow} mm</span>
                                      </p>
                                    )}
                                  </div>
                                </div>
                              );
                            }
                            return null;
                          }}
                        />
                        <Area
                          type="monotone"
                          dataKey="maxTemp"
                          name="Max Temperature"
                          stroke="#ff9500"
                          fillOpacity={0.3}
                          fill="url(#maxTempGradient)"
                          yAxisId="temp"
                        />
                        <Area
                          type="monotone"
                          dataKey="minTemp"
                          name="Min Temperature"
                          stroke="#0ea5e9"
                          fillOpacity={0.3}
                          fill="url(#minTempGradient)"
                          yAxisId="temp"
                        />
                        <Area
                          type="monotone"
                          dataKey="pop"
                          name="Precipitation"
                          stroke="#6366f1"
                          fillOpacity={0.3}
                          fill="url(#precipGradient)"
                          yAxisId="precip"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>

                  <div className="max-h-[400px] overflow-y-auto pr-1">
                    <div className="space-y-1.5">
                      {dailyForecast.map(
                        (day: ProcessedDailyForecast, index: number) => (
                          <div
                            key={`${day.timestamp}-${day.date}`}
                            className="flex items-center justify-between p-1.5 rounded-lg bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800"
                          >
                            <div className="flex items-center gap-2 sm:gap-3 flex-1">
                              <div className="w-12 sm:w-14 text-center flex-shrink-0">
                                <div className="text-xs font-medium text-neutral-700 dark:text-neutral-300">
                                  {day.dayOfWeek}
                                </div>
                                <div className="text-[8px] text-neutral-500 dark:text-neutral-400">
                                  {new Date(
                                    day.timestamp * 1000,
                                  ).toLocaleDateString(undefined, {
                                    day: 'numeric',
                                    month: 'short',
                                  })}
                                </div>
                              </div>
                              <div className="w-8 sm:w-10 flex-shrink-0">
                                <Image
                                  src={getWeatherIconUrl(day.icon)}
                                  alt={day.description}
                                  className="h-6 w-6"
                                  width={40}
                                  height={40}
                                  unoptimized
                                />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="text-xs capitalize text-neutral-600 dark:text-neutral-400 truncate">
                                  {day.description}
                                </div>
                                {day.pop > 20 && (
                                  <div className="text-[10px] text-blue-500 dark:text-blue-400 flex items-center gap-0.5">
                                    <Droplets className="h-3 w-3" />
                                    {day.pop}% chance of rain
                                  </div>
                                )}
                              </div>
                              <div className="flex items-center gap-2 sm:gap-3 flex-shrink-0">
                                <div className="text-right">
                                  <div className="text-xs font-semibold text-neutral-800 dark:text-neutral-200">
                                    {day.maxTemp}°
                                  </div>
                                  <div className="text-xs text-neutral-500 dark:text-neutral-400">
                                    {day.minTemp}°
                                  </div>
                                </div>
                                <div className="border-l border-neutral-200 dark:border-neutral-700 pl-2 flex flex-col items-end">
                                  <div className="text-[10px] text-neutral-500 flex items-center">
                                    <Wind className="h-3 w-3 mr-0.5 text-blue-500 dark:text-blue-400" />
                                    {day.windSpeed}
                                  </div>
                                  <div className="text-[10px] text-neutral-500 flex items-center">
                                    <Droplets className="h-3 w-3 mr-0.5 text-sky-500 dark:text-sky-400" />
                                    {day.humidity}%
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ),
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-10 text-neutral-500 dark:text-neutral-400">
                  Extended forecast data not available for this location
                </div>
              )}
            </TabsContent>

            <TabsContent
              value="airquality"
              className="px-2 sm:px-4 pb-2 weather-chart-tab"
            >
              <div className="mb-3">
                {airPollution ? (
                  <div className="space-y-4">
                    <div className="rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 p-4">
                      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 mb-3">
                        <div>
                          <h3 className="text-sm font-medium text-neutral-800 dark:text-neutral-100">
                            Current Air Quality
                          </h3>
                          <div className="mt-1">
                            {(() => {
                              const { label, colorClass } = getAirQualityInfo(
                                airPollution.main.aqi,
                              );
                              return (
                                <Badge
                                  className={`font-normal py-1 px-2 ${colorClass}`}
                                >
                                  {label} (AQI: {airPollution.main.aqi})
                                </Badge>
                              );
                            })()}
                          </div>
                        </div>
                        <div className="text-sm text-right text-neutral-500 dark:text-neutral-300">
                          {new Date(airPollution.dt * 1000).toLocaleTimeString(
                            [],
                            {
                              hour: '2-digit',
                              minute: '2-digit',
                            },
                          )}
                        </div>
                      </div>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 mt-2">
                        <div className="rounded-md bg-neutral-50 dark:bg-neutral-800 p-2">
                          <div className="text-[10px] uppercase text-neutral-500 dark:text-neutral-400">
                            PM2.5
                          </div>
                          <div className="font-medium text-sm text-neutral-800 dark:text-neutral-100">
                            {airPollution.components.pm2_5.toFixed(1)} µg/m³
                          </div>
                        </div>
                        <div className="rounded-md bg-neutral-50 dark:bg-neutral-800 p-2">
                          <div className="text-[10px] uppercase text-neutral-500 dark:text-neutral-400">
                            PM10
                          </div>
                          <div className="font-medium text-sm text-neutral-800 dark:text-neutral-100">
                            {airPollution.components.pm10.toFixed(1)} µg/m³
                          </div>
                        </div>
                        <div className="rounded-md bg-neutral-50 dark:bg-neutral-800 p-2">
                          <div className="text-[10px] uppercase text-neutral-500 dark:text-neutral-400">
                            NO₂
                          </div>
                          <div className="font-medium text-sm text-neutral-800 dark:text-neutral-100">
                            {airPollution.components.no2.toFixed(1)} µg/m³
                          </div>
                        </div>
                        <div className="rounded-md bg-neutral-50 dark:bg-neutral-800 p-2">
                          <div className="text-[10px] uppercase text-neutral-500 dark:text-neutral-400">
                            O₃
                          </div>
                          <div className="font-medium text-sm text-neutral-800 dark:text-neutral-100">
                            {airPollution.components.o3.toFixed(1)} µg/m³
                          </div>
                        </div>
                        <div className="rounded-md bg-neutral-50 dark:bg-neutral-800 p-2">
                          <div className="text-[10px] uppercase text-neutral-500 dark:text-neutral-400">
                            SO₂
                          </div>
                          <div className="font-medium text-sm text-neutral-800 dark:text-neutral-100">
                            {airPollution.components.so2.toFixed(1)} µg/m³
                          </div>
                        </div>
                        <div className="rounded-md bg-neutral-50 dark:bg-neutral-800 p-2">
                          <div className="text-[10px] uppercase text-neutral-500 dark:text-neutral-400">
                            CO
                          </div>
                          <div className="font-medium text-sm text-neutral-800 dark:text-neutral-100">
                            {airPollution.components.co.toFixed(1)} µg/m³
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 p-4">
                      <h3 className="text-sm font-medium mb-4 text-neutral-800 dark:text-neutral-200">
                        Air Quality Forecast
                      </h3>
                      {airPollutionForecast.length > 0 ? (
                        <>
                          <div className="flex items-center justify-center gap-4 mb-2">
                            <div className="flex items-center gap-1.5">
                              <div className="w-2 h-2 rounded-full bg-[#8884d8]" />
                              <span className="text-[10px] text-neutral-600 dark:text-neutral-400">
                                Air Quality Index
                              </span>
                            </div>
                          </div>
                          <div className="h-[180px] sm:h-[200px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <AreaChart
                                data={airPollutionForecast.slice(0, 24)}
                                margin={{
                                  top: 5,
                                  right: 5,
                                  left: 0,
                                  bottom: 5,
                                }}
                              >
                                <CartesianGrid
                                  strokeDasharray="3 3"
                                  stroke="rgba(148, 163, 184, 0.2)"
                                />
                                <XAxis
                                  dataKey="dt"
                                  tickFormatter={(value) =>
                                    new Date(value * 1000).toLocaleTimeString(
                                      [],
                                      {
                                        hour: '2-digit',
                                        minute: '2-digit',
                                      },
                                    )
                                  }
                                  tick={{ fontSize: 10 }}
                                  stroke="#9CA3AF"
                                />
                                <YAxis
                                  domain={[0, 5]}
                                  tickFormatter={(value) => value.toString()}
                                  tick={{ fontSize: 10 }}
                                  stroke="#9CA3AF"
                                />
                                <Tooltip
                                  content={({ active, payload }) => {
                                    if (active && payload && payload.length) {
                                      const data = payload[0].payload;
                                      const { label } = getAirQualityInfo(
                                        data.main.aqi,
                                      );
                                      return (
                                        <div className="custom-tooltip bg-white dark:bg-neutral-800 p-2 border border-neutral-200 dark:border-neutral-700 rounded-md text-xs shadow-sm">
                                          <p className="mb-1 font-medium">
                                            {new Date(
                                              data.dt * 1000,
                                            ).toLocaleTimeString([], {
                                              hour: '2-digit',
                                              minute: '2-digit',
                                            })}
                                          </p>
                                          <p>
                                            AQI: {data.main.aqi} ({label})
                                          </p>
                                          <p>
                                            PM2.5:{' '}
                                            {data.components.pm2_5.toFixed(1)}{' '}
                                            µg/m³
                                          </p>
                                          <p>
                                            PM10:{' '}
                                            {data.components.pm10.toFixed(1)}{' '}
                                            µg/m³
                                          </p>
                                        </div>
                                      );
                                    }
                                    return null;
                                  }}
                                />
                                <defs>
                                  <linearGradient
                                    id="aqiGradient"
                                    x1="0"
                                    y1="0"
                                    x2="0"
                                    y2="1"
                                  >
                                    <stop
                                      offset="5%"
                                      stopColor="#8884d8"
                                      stopOpacity={0.8}
                                    />
                                    <stop
                                      offset="95%"
                                      stopColor="#8884d8"
                                      stopOpacity={0.2}
                                    />
                                  </linearGradient>
                                </defs>
                                <Area
                                  type="monotone"
                                  dataKey="main.aqi"
                                  stroke="#8884d8"
                                  fill="url(#aqiGradient)"
                                />
                              </AreaChart>
                            </ResponsiveContainer>
                          </div>
                          <div className="mt-4 flex flex-wrap gap-2 justify-center">
                            {[1, 2, 3, 4, 5].map((aqi) => {
                              const { label, colorClass } =
                                getAirQualityInfo(aqi);
                              return (
                                <Badge
                                  key={aqi}
                                  className={`font-normal ${colorClass}`}
                                >
                                  {aqi}: {label}
                                </Badge>
                              );
                            })}
                          </div>
                        </>
                      ) : (
                        <div className="text-sm text-neutral-500 dark:text-neutral-400 text-center py-8">
                          No forecast data available
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-10 text-neutral-500 dark:text-neutral-400">
                    Air quality data not available for this location
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent
              value="map"
              className="px-2 sm:px-4 pb-2 weather-chart-tab"
            >
              <div
                className="space-y-4"
                style={{ position: 'relative', zIndex: 1 }}
              >
                {result.geocoding && (
                  <WeatherMapIntegrated
                    latitude={result.city?.coord?.lat || 0}
                    longitude={result.city?.coord?.lon || 0}
                    city={
                      result.geocoding.displayName ||
                      result.geocoding.name ||
                      'Unknown'
                    }
                    apiKey={
                      process.env.NEXT_PUBLIC_OPENWEATHERMAP_API_KEY || ''
                    }
                  />
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="border-t border-neutral-200 dark:border-neutral-800 py-0! px-4 m-0!">
          <div className="w-full flex justify-end items-center text-[9px] text-neutral-400 dark:text-neutral-500 py-1">
            OpenWeatherMap •{' '}
            {new Date().toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </div>
        </CardFooter>
      </Card>
      <style jsx global>{`
        /* Styles pour les écrans très petits */
        @media (max-width: 480px) {
          .grid-cols-5 {
            grid-template-columns: repeat(5, minmax(0, 1fr));
          }
        }
        .recharts-legend-item-text {
          font-size: 12px !important;
        }
        .hide-scrollbar::-webkit-scrollbar {
          display: none;
        }
        .hide-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        @media (min-width: 768px) {
          .hide-scrollbar {
            -ms-overflow-style: auto;
            scrollbar-width: auto;
          }
          .hide-scrollbar::-webkit-scrollbar {
            display: block;
            height: 6px;
          }
          .hide-scrollbar::-webkit-scrollbar-thumb {
            background-color: #d1d5db;
            border-radius: 3px;
          }
          .dark .hide-scrollbar::-webkit-scrollbar-thumb {
            background-color: #4b5563;
          }
        }
      `}</style>
    </div>
  );
});

WeatherChart.displayName = 'WeatherChart';
export default WeatherChart;
