'use client';

import React, { useEffect, useRef, memo, useId } from 'react';

declare global {
  interface Window {
    TradingView: {
      widget: new (
        options: any,
      ) => {
        remove: () => void;
      };
    };
  }
}

interface FinancialChartProps {
  ticker: string;
}

function FinancialChart({ ticker }: FinancialChartProps) {
  const container = useRef<HTMLDivElement>(null);
  const widgetId = useId();
  const widgetInstance = useRef<{ remove: () => void } | null>(null);
  const scriptLoaded = useRef(false);

  useEffect(() => {
    // Check if ticker is valid
    if (!ticker || typeof ticker !== 'string' || ticker.trim() === '') {
      console.error('Invalid ticker');
      return;
    }

    // Clean the ticker
    const cleanTicker = ticker.trim().toUpperCase();

    // Check if container exists
    if (!container.current) return;

    const loadWidget = () => {
      if (typeof window === 'undefined' || !('TradingView' in window)) {
        console.error('TradingView widget script not loaded');
        return;
      }

      // Widget configuration
      const widgetConfig = {
        autosize: true,
        symbol: cleanTicker,
        interval: 'D',
        timezone: 'Etc/UTC',
        theme: 'light',
        style: '1',
        locale: 'en', // Changed from 'fr' to 'en' for English
        toolbar_bg: '#f1f3f6',
        enable_publishing: false,
        allow_symbol_change: true,
        container_id: widgetId,
      };

      // Destroy previous instance if it exists
      if (widgetInstance.current) {
        try {
          widgetInstance.current.remove();
        } catch (e) {
          console.warn('Error removing previous widget instance:', e);
        }
        widgetInstance.current = null;
      }

      // Create a new container for the widget
      const widgetContainer = document.createElement('div');
      widgetContainer.id = widgetId;
      widgetContainer.style.width = '100%';
      widgetContainer.style.height = '100%';

      // Clear the main container
      if (container.current) {
        container.current.innerHTML = '';
        container.current.appendChild(widgetContainer);
      }

      // Create a new widget instance
      if (typeof window !== 'undefined' && 'TradingView' in window) {
        widgetInstance.current = new window.TradingView.widget(widgetConfig);
      } else {
        console.error('TradingView widget not available');
      }

      // Add copyright link
      const link = document.createElement('a');
      link.href = 'https://www.tradingview.com/';
      link.rel = 'noopener nofollow';
      link.target = '_blank';
      link.className = 'tradingview-widget-copyright';
      link.textContent = 'Track all markets on TradingView';

      if (container.current) {
        container.current.appendChild(link);
      }
    };

    if (typeof window !== 'undefined' && 'TradingView' in window) {
      // If script is already loaded, load the widget directly
      loadWidget();
    } else if (!scriptLoaded.current) {
      // Otherwise, load the script
      scriptLoaded.current = true;
      const script = document.createElement('script');
      script.src = 'https://s3.tradingview.com/tv.js';
      script.async = true;
      script.onload = () => {
        if (typeof window !== 'undefined' && 'TradingView' in window) {
          loadWidget();
        } else {
          console.error(
            'TradingView widget script loaded but TradingView object not found',
          );
        }
      };
      script.onerror = () => {
        console.error('Error loading TradingView script');
        if (container.current) {
          container.current.innerHTML =
            '<p>Unable to load chart. Please try again later.</p>';
        }
      };
      document.body.appendChild(script);
    }

    // Cleanup
    return () => {
      if (widgetInstance.current) {
        try {
          widgetInstance.current.remove();
        } catch (e) {
          console.warn('Error cleaning up widget:', e);
        }
        widgetInstance.current = null;
      }
    };
  }, [ticker, widgetId]);

  return (
    <div
      ref={container}
      style={{
        height: '500px',
        width: '100%',
        minHeight: '400px',
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        border: '1px solid #e0e3eb',
        overflow: 'hidden',
        padding: '10px',
        margin: '10px 0',
        position: 'relative',
      }}
      className="tradingview-widget-container"
    />
  );
}

export default memo(FinancialChart);
