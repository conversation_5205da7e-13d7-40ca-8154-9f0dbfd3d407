// Server environment variables with validation
// This provides a simple, type-safe way to access environment variables

function getEnvVar(name: string, required = false): string | undefined {
  const value = process.env[name];
  if (required && !value) {
    console.error(`❌ Required environment variable ${name} is not defined`);
  }
  return value;
}

function getRequiredEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) {
    throw new Error(`❌ Required environment variable ${name} is not defined`);
  }
  return value;
}

// Export environment variables with validation
export const serverEnv = {
  // Required variables
  EXA_API_KEY: getRequiredEnvVar('EXA_API_KEY'),
  DAYTONA_API_KEY: getRequiredEnvVar('DAYTONA_API_KEY'),

  // Optional variables
  OPENAI_API_KEY: getEnvVar('OPENAI_API_KEY'),
  ANTHROPIC_API_KEY: getEnvVar('ANTHROPIC_API_KEY'),
  XAI_API_KEY: getEnvVar('XAI_API_KEY'),
  GOOGLE_GENERATIVE_AI_API_KEY: getEnvVar('GOOGLE_GENERATIVE_AI_API_KEY'),
  TAVILY_API_KEY: getEnvVar('TAVILY_API_KEY'),
  TMDB_API_KEY: getEnvVar('TMDB_API_KEY'),
  WEATHER_API_KEY: getEnvVar('WEATHER_API_KEY'),
  GOOGLE_MAPS_API_KEY: getEnvVar('GOOGLE_MAPS_API_KEY'),
  MEM0_API_KEY: getEnvVar('MEM0_API_KEY'),
  DATABASE_URL: getEnvVar('DATABASE_URL'),
  NEXTAUTH_SECRET: getEnvVar('NEXTAUTH_SECRET'),
  NEXTAUTH_URL: getEnvVar('NEXTAUTH_URL'),
  GITHUB_CLIENT_ID: getEnvVar('GITHUB_CLIENT_ID'),
  GITHUB_CLIENT_SECRET: getEnvVar('GITHUB_CLIENT_SECRET'),
  GOOGLE_CLIENT_ID: getEnvVar('GOOGLE_CLIENT_ID'),
  GOOGLE_CLIENT_SECRET: getEnvVar('GOOGLE_CLIENT_SECRET'),
  SMITHERY_API_KEY: getEnvVar('SMITHERY_API_KEY'),
} as const;
