# Guide Complet du Streaming - Recherche Extrême

## Vue d'ensemble

Le système de streaming de la recherche extrême permet d'afficher les résultats en temps réel pendant que l'agent effectue ses recherches. Voici tout ce qu'il faut savoir pour l'implémenter.

## Architecture du Streaming

```
┌─────────────────────┐
│  API Route          │
│  (route.ts)         │
└──────────┬──────────┘
           │ createUIMessageStream
           ▼
┌─────────────────────┐
│  dataStream         │ ← UIMessageStreamWriter
│  (writer)           │
└──────────┬──────────┘
           │ dataStream.write()
           ▼
┌─────────────────────┐
│  extremeSearchTool  │
│  (extreme-search.ts)│
└──────────┬──────────┘
           │ Envoie des événements
           ▼
┌─────────────────────┐
│  Client React       │
│  (extreme-search.tsx)│
└─────────────────────┘
```

## 1. Configuration du Stream (API Route)

### Fichier: `app/api/search/route.ts`

Le streaming commence dans l'API route avec `createUIMessageStream`:

```typescript
import { createUIMessageStream } from 'ai';

export async function POST(req: Request) {
  // ... configuration initiale
  
  const stream = createUIMessageStream<ChatMessage>({
    execute: async ({ writer: dataStream }) => {
      // dataStream est le writer qui permet d'envoyer des événements
      
      // Configuration des outils avec le dataStream
      const tools = {
        extreme_search: extremeSearchTool(dataStream),
        // ... autres outils
      };
      
      // Lancement du streamText avec les outils
      const result = streamText({
        model: scira.languageModel(model),
        tools,
        // ... autres options
      });
      
      // Merge le stream de résultats avec le dataStream
      dataStream.merge(
        result.toUIMessageStream({
          sendReasoning: true,
          messageMetadata: ({ part }) => ({
            // ... métadonnées
          }),
        }),
      );
    },
  });
  
  return new Response(stream.pipeThrough(new JsonToSseTransformStream()));
}
```


## 2. Types de Données Streamées

### Fichier: `lib/types.ts`

Tous les événements streamés suivent ce type:

```typescript
export type DataExtremeSearchPart = {
  type: 'data-extreme_search';
  data:
    | {
        kind: 'plan';
        status: { title: string };
        plan?: Array<{ title: string; todos: string[] }>;
      }
    | {
        kind: 'query';
        queryId: string;
        query: string;
        status: 'started' | 'reading_content' | 'completed' | 'error';
      }
    | {
        kind: 'source';
        queryId: string;
        source: { title: string; url: string; favicon?: string };
      }
    | {
        kind: 'content';
        queryId: string;
        content: { title: string; url: string; text: string; favicon?: string };
      }
    | {
        kind: 'code';
        codeId: string;
        title: string;
        code: string;
        status: 'running' | 'completed' | 'error';
        result?: string;
        charts?: any[];
      }
    | {
        kind: 'x_search';
        xSearchId: string;
        query: string;
        startDate: string;
        endDate: string;
        handles?: string[];
        status: 'started' | 'completed' | 'error';
        result?: {
          content: string;
          citations: any[];
          sources: Array<{ text: string; link: string; title?: string }>;
          dateRange: string;
          handles: string[];
        };
      };
};
```

### Types d'événements:

1. **plan** - Plan de recherche généré
2. **query** - Requête de recherche web (started/reading_content/completed)
3. **source** - Source trouvée (URL + titre)
4. **content** - Contenu complet d'une source
5. **code** - Exécution de code Python (running/completed)
6. **x_search** - Recherche sur X/Twitter (started/completed)


## 3. Envoi des Événements (Backend)

### Fichier: `lib/tools/extreme-search.ts`

Voici comment envoyer chaque type d'événement:

### 3.1 Événement PLAN

```typescript
// Au début de la recherche
if (dataStream) {
  dataStream.write({
    type: 'data-extreme_search',
    data: {
      kind: 'plan',
      status: { title: 'Planning research' },
    },
  });
}

// Après génération du plan
if (dataStream) {
  dataStream.write({
    type: 'data-extreme_search',
    data: {
      kind: 'plan',
      status: { title: 'Research plan ready, starting up research agent' },
      plan: [
        {
          title: 'Rechercher les tendances actuelles',
          todos: ['Query 1', 'Query 2', 'Query 3']
        }
      ],
    },
  });
}

// À la fin
if (dataStream) {
  dataStream.write({
    type: 'data-extreme_search',
    data: {
      kind: 'plan',
      status: { title: 'Research completed' },
    },
  });
}
```

### 3.2 Événement QUERY (Recherche Web)

```typescript
// Début de la requête
if (dataStream) {
  dataStream.write({
    type: 'data-extreme_search',
    data: {
      kind: 'query',
      queryId: toolCallId, // ID unique de l'appel d'outil
      query: 'Ma requête de recherche',
      status: 'started',
    },
  });
}

// Lecture du contenu
if (dataStream) {
  dataStream.write({
    type: 'data-extreme_search',
    data: {
      kind: 'query',
      queryId: toolCallId,
      query: 'Ma requête de recherche',
      status: 'reading_content',
    },
  });
}

// Requête terminée
if (dataStream) {
  dataStream.write({
    type: 'data-extreme_search',
    data: {
      kind: 'query',
      queryId: toolCallId,
      query: 'Ma requête de recherche',
      status: 'completed',
    },
  });
}
```

### 3.3 Événement SOURCE

```typescript
// Pour chaque source trouvée
if (dataStream) {
  results.forEach((source) => {
    dataStream.write({
      type: 'data-extreme_search',
      data: {
        kind: 'source',
        queryId: toolCallId,
        source: {
          title: source.title,
          url: source.url,
          favicon: source.favicon,
        },
      },
    });
  });
}
```

### 3.4 Événement CONTENT

```typescript
// Pour chaque contenu complet récupéré
if (dataStream) {
  contentsResults.forEach((content) => {
    dataStream.write({
      type: 'data-extreme_search',
      data: {
        kind: 'content',
        queryId: toolCallId,
        content: {
          title: content.title,
          url: content.url,
          text: content.content.slice(0, 500) + '...',
          favicon: content.favicon,
        },
      },
    });
  });
}
```


### 3.5 Événement CODE

```typescript
// Début de l'exécution
if (dataStream) {
  dataStream.write({
    type: 'data-extreme_search',
    data: {
      kind: 'code',
      codeId: `code-${Date.now()}`,
      title: 'Analyse des données',
      code: 'import pandas as pd\n...',
      status: 'running',
    },
  });
}

// Exécution terminée
if (dataStream) {
  dataStream.write({
    type: 'data-extreme_search',
    data: {
      kind: 'code',
      codeId: `code-${Date.now()}`,
      title: 'Analyse des données',
      code: 'import pandas as pd\n...',
      status: 'completed',
      result: 'Résultat de l\'exécution',
      charts: [
        {
          type: 'line',
          title: 'Mon graphique',
          url: 'https://...',
          // ... données du graphique
        }
      ],
    },
  });
}
```

### 3.6 Événement X_SEARCH

```typescript
// Début de la recherche X
if (dataStream) {
  dataStream.write({
    type: 'data-extreme_search',
    data: {
      kind: 'x_search',
      xSearchId: toolCallId,
      query: 'Ma requête X',
      startDate: '2024-01-01',
      endDate: '2024-01-31',
      handles: ['elonmusk', 'openai'],
      status: 'started',
    },
  });
}

// Recherche terminée
if (dataStream) {
  dataStream.write({
    type: 'data-extreme_search',
    data: {
      kind: 'x_search',
      xSearchId: toolCallId,
      query: 'Ma requête X',
      startDate: '2024-01-01',
      endDate: '2024-01-31',
      handles: ['elonmusk', 'openai'],
      status: 'completed',
      result: {
        content: 'Résumé des posts trouvés',
        citations: [...],
        sources: [
          {
            text: 'Texte du tweet',
            link: 'https://x.com/...',
            title: 'Post from @user: ...'
          }
        ],
        dateRange: '2024-01-01 to 2024-01-31',
        handles: ['elonmusk', 'openai'],
      },
    },
  });
}
```


## 4. Réception des Événements (Frontend)

### Fichier: `components/extreme-search.tsx`

Le composant React reçoit les événements via les `annotations`:

```typescript
export function ExtremeSearchResults({ 
  annotations 
}: { 
  annotations: DataExtremeSearchPart[] 
}) {
  // Filtrer les événements par type
  const planAnnotations = annotations.filter(
    (ann) => ann.type === 'data-extreme_search' && ann.data.kind === 'plan'
  );
  
  const queryAnnotations = annotations.filter(
    (ann) => ann.type === 'data-extreme_search' && ann.data.kind === 'query'
  );
  
  const sourceAnnotations = annotations.filter(
    (ann) => ann.type === 'data-extreme_search' && ann.data.kind === 'source'
  );
  
  const codeAnnotations = annotations.filter(
    (ann) => ann.type === 'data-extreme_search' && ann.data.kind === 'code'
  );
  
  const xSearchAnnotations = annotations.filter(
    (ann) => ann.type === 'data-extreme_search' && ann.data.kind === 'x_search'
  );
  
  // Récupérer le dernier plan
  const latestPlan = planAnnotations[planAnnotations.length - 1];
  
  return (
    <div>
      {/* Afficher le plan */}
      {latestPlan && <PlanDisplay plan={latestPlan.data} />}
      
      {/* Afficher les requêtes */}
      {queryAnnotations.map((query) => (
        <QueryDisplay key={query.data.queryId} query={query.data} />
      ))}
      
      {/* Afficher les sources */}
      {sourceAnnotations.map((source, i) => (
        <SourceDisplay key={i} source={source.data} />
      ))}
      
      {/* Afficher le code */}
      {codeAnnotations.map((code) => (
        <CodeDisplay key={code.data.codeId} code={code.data} />
      ))}
      
      {/* Afficher les recherches X */}
      {xSearchAnnotations.map((xSearch) => (
        <XSearchDisplay key={xSearch.data.xSearchId} xSearch={xSearch.data} />
      ))}
    </div>
  );
}
```


## 5. Exemple Complet d'Implémentation

### Backend: Outil de Recherche

```typescript
// lib/tools/my-extreme-search.ts
import { tool } from 'ai';
import type { UIMessageStreamWriter } from 'ai';
import { z } from 'zod';

export function myExtremeSearchTool(
  dataStream: UIMessageStreamWriter | undefined
) {
  return tool({
    description: 'Effectue une recherche approfondie',
    inputSchema: z.object({
      prompt: z.string(),
    }),
    execute: async ({ prompt }) => {
      // 1. Envoyer le statut de planification
      if (dataStream) {
        dataStream.write({
          type: 'data-extreme_search',
          data: {
            kind: 'plan',
            status: { title: 'Démarrage de la recherche...' },
          },
        });
      }
      
      // 2. Générer un plan
      const plan = await generatePlan(prompt);
      
      if (dataStream) {
        dataStream.write({
          type: 'data-extreme_search',
          data: {
            kind: 'plan',
            status: { title: 'Plan prêt' },
            plan: plan,
          },
        });
      }
      
      // 3. Exécuter les recherches
      const results = [];
      
      for (const step of plan) {
        const queryId = `query-${Date.now()}`;
        
        // Début de la requête
        if (dataStream) {
          dataStream.write({
            type: 'data-extreme_search',
            data: {
              kind: 'query',
              queryId,
              query: step.query,
              status: 'started',
            },
          });
        }
        
        // Effectuer la recherche
        const searchResults = await performSearch(step.query);
        
        // Envoyer chaque source
        if (dataStream) {
          searchResults.forEach((source) => {
            dataStream.write({
              type: 'data-extreme_search',
              data: {
                kind: 'source',
                queryId,
                source: {
                  title: source.title,
                  url: source.url,
                  favicon: source.favicon,
                },
              },
            });
          });
        }
        
        // Marquer comme terminé
        if (dataStream) {
          dataStream.write({
            type: 'data-extreme_search',
            data: {
              kind: 'query',
              queryId,
              query: step.query,
              status: 'completed',
            },
          });
        }
        
        results.push(...searchResults);
      }
      
      // 4. Recherche terminée
      if (dataStream) {
        dataStream.write({
          type: 'data-extreme_search',
          data: {
            kind: 'plan',
            status: { title: 'Recherche terminée' },
          },
        });
      }
      
      return {
        research: {
          text: 'Résumé de la recherche',
          sources: results,
        },
      };
    },
  });
}
```


### Frontend: Composant d'Affichage

```typescript
// components/my-extreme-search-display.tsx
'use client';

import { DataExtremeSearchPart } from '@/lib/types';
import { useState, useEffect } from 'react';

export function ExtremeSearchDisplay({ 
  annotations 
}: { 
  annotations: DataExtremeSearchPart[] 
}) {
  const [currentStatus, setCurrentStatus] = useState('');
  const [plan, setPlan] = useState<any>(null);
  const [queries, setQueries] = useState<Map<string, any>>(new Map());
  const [sources, setSources] = useState<any[]>([]);
  
  useEffect(() => {
    annotations.forEach((ann) => {
      if (ann.type !== 'data-extreme_search') return;
      
      const { data } = ann;
      
      // Gérer les événements de plan
      if (data.kind === 'plan') {
        setCurrentStatus(data.status.title);
        if (data.plan) {
          setPlan(data.plan);
        }
      }
      
      // Gérer les événements de requête
      if (data.kind === 'query') {
        setQueries((prev) => {
          const newMap = new Map(prev);
          newMap.set(data.queryId, data);
          return newMap;
        });
      }
      
      // Gérer les événements de source
      if (data.kind === 'source') {
        setSources((prev) => [...prev, data.source]);
      }
    });
  }, [annotations]);
  
  return (
    <div className="space-y-4">
      {/* Statut actuel */}
      <div className="text-sm text-muted-foreground">
        {currentStatus}
      </div>
      
      {/* Plan de recherche */}
      {plan && (
        <div className="border rounded-lg p-4">
          <h3 className="font-semibold mb-2">Plan de recherche</h3>
          <ul className="space-y-2">
            {plan.map((item: any, i: number) => (
              <li key={i}>
                <strong>{item.title}</strong>
                <ul className="ml-4 mt-1">
                  {item.todos.map((todo: string, j: number) => (
                    <li key={j} className="text-sm">• {todo}</li>
                  ))}
                </ul>
              </li>
            ))}
          </ul>
        </div>
      )}
      
      {/* Requêtes en cours */}
      <div className="space-y-2">
        {Array.from(queries.values()).map((query) => (
          <div key={query.queryId} className="border rounded p-3">
            <div className="flex items-center gap-2">
              {query.status === 'started' && (
                <span className="animate-spin">⏳</span>
              )}
              {query.status === 'completed' && (
                <span>✅</span>
              )}
              <span className="text-sm">{query.query}</span>
            </div>
          </div>
        ))}
      </div>
      
      {/* Sources trouvées */}
      <div className="space-y-2">
        <h4 className="font-semibold">Sources ({sources.length})</h4>
        {sources.map((source, i) => (
          <a
            key={i}
            href={source.url}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-2 p-2 border rounded hover:bg-accent"
          >
            {source.favicon && (
              <img src={source.favicon} alt="" className="w-4 h-4" />
            )}
            <span className="text-sm">{source.title}</span>
          </a>
        ))}
      </div>
    </div>
  );
}
```


## 6. Intégration dans l'API Route

```typescript
// app/api/search/route.ts
import { createUIMessageStream, streamText } from 'ai';
import { myExtremeSearchTool } from '@/lib/tools/my-extreme-search';

export async function POST(req: Request) {
  const { messages, model } = await req.json();
  
  const stream = createUIMessageStream({
    execute: async ({ writer: dataStream }) => {
      const result = streamText({
        model: yourModel,
        messages,
        tools: {
          extreme_search: myExtremeSearchTool(dataStream),
          // ... autres outils
        },
      });
      
      result.consumeStream();
      
      dataStream.merge(
        result.toUIMessageStream({
          sendReasoning: true,
        }),
      );
    },
  });
  
  return new Response(
    stream.pipeThrough(new JsonToSseTransformStream())
  );
}
```

## 7. Utilisation dans le Chat

```typescript
// components/chat.tsx
import { useChat } from 'ai/react';
import { ExtremeSearchDisplay } from './my-extreme-search-display';

export function Chat() {
  const { messages } = useChat({
    api: '/api/search',
  });
  
  return (
    <div>
      {messages.map((message) => (
        <div key={message.id}>
          {/* Texte du message */}
          {message.content}
          
          {/* Annotations de recherche extrême */}
          {message.annotations && (
            <ExtremeSearchDisplay
              annotations={
                message.annotations.filter(
                  (ann) => ann.type === 'data-extreme_search'
                )
              }
            />
          )}
        </div>
      ))}
    </div>
  );
}
```


## 8. Points Clés à Retenir

### Backend

1. **Créer le stream** avec `createUIMessageStream` dans l'API route
2. **Passer le dataStream** à votre outil de recherche
3. **Envoyer des événements** avec `dataStream.write()` à chaque étape
4. **Utiliser des IDs uniques** pour les requêtes (toolCallId, Date.now(), etc.)
5. **Merger les streams** avec `dataStream.merge(result.toUIMessageStream())`

### Frontend

1. **Filtrer les annotations** par type et kind
2. **Utiliser useState/useEffect** pour gérer l'état des événements
3. **Grouper les événements** par ID (queryId, codeId, etc.)
4. **Afficher en temps réel** avec des animations
5. **Gérer les états** (started, running, completed, error)

### Structure des Événements

Tous les événements suivent cette structure:
```typescript
{
  type: 'data-extreme_search',
  data: {
    kind: 'plan' | 'query' | 'source' | 'content' | 'code' | 'x_search',
    // ... données spécifiques au kind
  }
}
```

### Bonnes Pratiques

1. **Toujours vérifier** si `dataStream` existe avant d'écrire
2. **Envoyer des événements de statut** pour informer l'utilisateur
3. **Utiliser des IDs uniques** pour lier les événements entre eux
4. **Gérer les erreurs** avec status: 'error'
5. **Optimiser les performances** en évitant trop d'événements
6. **Tester le streaming** avec des délais artificiels


## 9. Dépendances Requises

```json
{
  "dependencies": {
    "ai": "^3.x",
    "@ai-sdk/xai": "^1.x",
    "exa-js": "^1.x",
    "@mendable/firecrawl-js": "^1.x",
    "@daytonaio/sdk": "^1.x",
    "zod": "^3.x",
    "react": "^18.x",
    "next": "^14.x"
  }
}
```

## 10. Exemple de Flux Complet

### Scénario: Recherche sur "AI trends 2024"

```
1. Client envoie la requête → API Route
   ↓
2. API crée le stream et appelle extremeSearchTool
   ↓
3. Backend envoie: { kind: 'plan', status: 'Planning research' }
   → Frontend affiche: "Planification de la recherche..."
   ↓
4. Backend génère le plan et envoie: { kind: 'plan', plan: [...] }
   → Frontend affiche le plan avec les étapes
   ↓
5. Backend démarre une recherche: { kind: 'query', status: 'started' }
   → Frontend affiche: "🔍 Recherche en cours: AI trends 2024"
   ↓
6. Backend trouve des sources: { kind: 'source', source: {...} }
   → Frontend affiche chaque source en temps réel
   ↓
7. Backend lit le contenu: { kind: 'query', status: 'reading_content' }
   → Frontend affiche: "📖 Lecture du contenu..."
   ↓
8. Backend envoie le contenu: { kind: 'content', content: {...} }
   → Frontend affiche un aperçu du contenu
   ↓
9. Backend termine: { kind: 'query', status: 'completed' }
   → Frontend affiche: "✅ Recherche terminée"
   ↓
10. Backend exécute du code: { kind: 'code', status: 'running' }
    → Frontend affiche: "⚙️ Exécution du code..."
    ↓
11. Backend envoie les résultats: { kind: 'code', status: 'completed', charts: [...] }
    → Frontend affiche les graphiques
    ↓
12. Backend termine tout: { kind: 'plan', status: 'Research completed' }
    → Frontend affiche: "✅ Recherche terminée avec succès"
```


## 11. Debugging et Troubleshooting

### Vérifier que les événements sont envoyés

```typescript
// Backend
if (dataStream) {
  console.log('Sending event:', { kind: 'plan', status: 'Planning' });
  dataStream.write({
    type: 'data-extreme_search',
    data: { kind: 'plan', status: { title: 'Planning' } },
  });
}
```

### Vérifier que les événements sont reçus

```typescript
// Frontend
useEffect(() => {
  console.log('Received annotations:', annotations);
  annotations.forEach((ann) => {
    if (ann.type === 'data-extreme_search') {
      console.log('Extreme search event:', ann.data);
    }
  });
}, [annotations]);
```

### Problèmes Courants

1. **Les événements ne sont pas reçus**
   - Vérifier que `dataStream` est bien passé à l'outil
   - Vérifier que le type est exactement `'data-extreme_search'`
   - Vérifier que le stream est bien mergé dans l'API route

2. **Les événements arrivent dans le désordre**
   - Utiliser des IDs uniques pour lier les événements
   - Utiliser des timestamps si nécessaire
   - Grouper les événements par ID dans le frontend

3. **Performance dégradée**
   - Limiter le nombre d'événements envoyés
   - Utiliser la pagination pour les sources
   - Optimiser les re-renders avec React.memo

4. **Le stream se coupe**
   - Vérifier les timeouts du serveur
   - Gérer les erreurs avec try/catch
   - Implémenter un système de retry

## 12. Ressources Supplémentaires

- [Vercel AI SDK Documentation](https://sdk.vercel.ai)
- [Server-Sent Events (SSE)](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events)
- [React Hooks](https://react.dev/reference/react)
- [TypeScript Types](https://www.typescriptlang.org/docs/handbook/2/types-from-types.html)

## 13. Checklist d'Implémentation

- [ ] Installer les dépendances (ai, zod, etc.)
- [ ] Créer les types TypeScript pour les événements
- [ ] Configurer l'API route avec createUIMessageStream
- [ ] Créer l'outil de recherche avec dataStream
- [ ] Implémenter l'envoi des événements (plan, query, source, etc.)
- [ ] Créer le composant React pour afficher les résultats
- [ ] Filtrer et grouper les annotations dans le frontend
- [ ] Gérer les états (loading, completed, error)
- [ ] Ajouter des animations pour une meilleure UX
- [ ] Tester le flux complet end-to-end
- [ ] Gérer les erreurs et les cas limites
- [ ] Optimiser les performances
- [ ] Ajouter des logs pour le debugging

---

**Version**: 1.0  
**Dernière mise à jour**: 2025-01-08  
**Auteur**: Guide d'implémentation du streaming de recherche extrême

