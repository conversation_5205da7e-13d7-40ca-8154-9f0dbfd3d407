import { z } from 'zod';
import {
  generateObject,
  type LanguageModel,
  type UIMessageStreamWriter,
} from 'ai';
import type { ChatMessage } from '@/lib/types';

export interface ValidationResult {
  confidence: number; // 0-1 score
  hasDestination: boolean;
  hasTimeframe: boolean;
  extractedInfo: {
    destination?: string;
    country?: string;
    duration?: number;
    timeframe?: string;
  };
  missingCritical: string[];
  reasoning: string;
  shouldProceed: boolean;
}

export interface ClarifyingQuestion {
  question: string;
  type: 'destination' | 'duration' | 'both';
  suggestions?: string[];
}

export class ValidationAgent {
  private static validationCache = new Map<string, ValidationResult>();
  private static isValidating = new Set<string>();
  private static createdItineraries = new Set<string>(); // Track created itineraries per session

  constructor(private model: LanguageModel) {}

  /**
   * Mark that an itinerary has been created for a destination
   */
  static markItineraryCreated(destination: string) {
    const key = destination.toLowerCase().trim();
    ValidationAgent.createdItineraries.add(key);
    console.log('🎯 Marked itinerary as created for:', destination);
  }

  /**
   * Check if an itinerary has already been created for a destination
   */
  static hasItineraryBeenCreated(destination: string): boolean {
    const key = destination.toLowerCase().trim();
    return ValidationAgent.createdItineraries.has(key);
  }

  /**
   * Clear the created itineraries cache (for new sessions)
   */
  static clearCreatedItineraries() {
    ValidationAgent.createdItineraries.clear();
    console.log('🧹 Cleared created itineraries cache');
  }

  /**
   * Intelligent validation using LLM with loop protection
   */
  async validateTravelRequest(
    query: string,
    dataStream?: UIMessageStreamWriter<ChatMessage>,
  ): Promise<ValidationResult> {
    // Stream progress update
    if (dataStream) {
      console.log(
        '🔄 [ValidationAgent] Streaming: Starting travel request validation',
      );
    }

    // Protection contre les boucles : cache et verrous
    const cacheKey = query.toLowerCase().trim();

    // 🧠 INTELLIGENT DETECTION: Check for completion/status messages
    const completionPatterns = [
      /^created\s+.+\s+itinerary$/i,
      /^creating\s+.+\s+itinerary$/i,
      /^generated\s+.+\s+travel/i,
      /^finished\s+.+\s+plan/i,
      /^completed\s+.+\s+trip/i,
      /^your\s+.+\s+is\s+ready/i,
      /travel\s+adventure\s+is\s+ready/i,
      /itinerary\s+complete/i,
    ];

    const isCompletionMessage = completionPatterns.some((pattern) =>
      pattern.test(query.trim()),
    );

    if (isCompletionMessage) {
      console.log('🚫 Detected completion message, blocking:', query);
      return {
        confidence: 0.0,
        hasDestination: false,
        hasTimeframe: false,
        extractedInfo: {},
        missingCritical: [],
        reasoning: 'This is a completion/status message, not a travel request',
        shouldProceed: false,
      };
    }

    // 🧠 INTELLIGENT DESTINATION DETECTION: Use LLM to intelligently detect destinations
    // This replaces the hardcoded city list with flexible AI-powered recognition
    console.log(
      '🧠 Using intelligent LLM-based destination detection instead of hardcoded patterns',
    );

    // Si déjà en cours de validation, retourner un résultat optimiste
    if (ValidationAgent.isValidating.has(cacheKey)) {
      console.log(
        'Validation already in progress, returning optimistic result',
      );
      return {
        confidence: 0.8,
        hasDestination: true,
        hasTimeframe: false,
        extractedInfo: {},
        missingCritical: [],
        reasoning: 'Validation in progress - optimistic fallback',
        shouldProceed: true,
      };
    }

    // Vérifier le cache
    const cachedResult = ValidationAgent.validationCache.get(cacheKey);
    if (cachedResult) {
      console.log('Returning cached validation result');
      return cachedResult;
    }

    // Marquer comme en cours de validation
    ValidationAgent.isValidating.add(cacheKey);

    try {
      const { object: validation } = await generateObject({
        model: this.model,
        system: `You are an expert travel request validator with advanced geographical knowledge. Analyze travel requests to determine if there's enough information to create a quality travel guide.

🌍 INTELLIGENT DESTINATION RECOGNITION:
- Use your knowledge of world geography to identify ANY valid destination
- Recognize cities, regions, countries, landmarks, neighborhoods, islands, etc.
- Support multilingual destination names (Paris/París, Tokyo/東京, etc.)
- Understand context clues and local references

STRICT VALIDATION RULES:
- Only give high confidence (0.7+) if the destination is SPECIFIC and IDENTIFIABLE
- REJECT vague terms: "ville", "city", "montagne", "beach", "Europe", "somewhere nice" (too vague)
- ACCEPT specific places: "Paris", "Tokyo", "New York", "Côte d'Azur", "Alpes", "Bali", "Santorini", "Marrakech", etc.
- ACCEPT neighborhoods/districts: "Montmartre", "Shibuya", "Brooklyn", "Trastevere"
- ACCEPT regions: "Tuscany", "Provence", "Patagonia", "Scottish Highlands"
- ACCEPT landmarks as destinations: "Machu Picchu", "Angkor Wat", "Great Wall"

🎯 DESTINATION EXTRACTION:
- Extract the most specific destination mentioned
- If multiple destinations, focus on the primary one
- Normalize destination names (e.g., "NYC" → "New York")
- Identify the country when possible

CONFIDENCE SCORING:
- 0.9-1.0: Complete info (specific destination + timeframe)
- 0.7-0.8: Good info (specific destination)
- 0.3-0.6: Partial info (vague destination or unclear location)
- 0.0-0.2: Insufficient info (no clear destination)`,
        prompt: `Analyze this travel request: "${query}"

Use your geographical knowledge to identify ANY valid destination worldwide.
Be STRICT: Only give confidence ≥ 0.7 if destination is specific and identifiable.
Extract the destination name and normalize it if needed.`,
        schema: z.object({
          confidence: z.number().min(0).max(1).describe('Confidence score 0-1'),
          hasDestination: z
            .boolean()
            .describe('Is destination clearly specified?'),
          hasTimeframe: z
            .boolean()
            .describe('Is timeframe/duration specified or implied?'),
          extractedInfo: z.object({
            destination: z
              .string()
              .optional()
              .describe('Extracted destination if found'),
            country: z
              .string()
              .optional()
              .describe('Extracted country if found'),
            duration: z
              .number()
              .optional()
              .describe('Extracted duration in days if found'),
            timeframe: z
              .string()
              .optional()
              .describe('Extracted timeframe description'),
          }),
          missingCritical: z
            .array(z.string())
            .describe('List of critical missing information'),
          reasoning: z.string().describe('Explanation of the confidence score'),
          shouldProceed: z
            .boolean()
            .describe('Should we proceed with trip planning?'),
        }),
        temperature: 0.1, // Low temperature for consistent validation
        maxOutputTokens: 500, // Limite pour éviter les réponses trop longues
      });

      // Mettre en cache le résultat
      ValidationAgent.validationCache.set(cacheKey, validation);

      // Nettoyer le cache si trop grand (garder les 100 derniers)
      if (ValidationAgent.validationCache.size > 100) {
        const firstKey = ValidationAgent.validationCache.keys().next().value;
        if (firstKey) {
          ValidationAgent.validationCache.delete(firstKey);
        }
      }

      return validation;
    } catch (error) {
      console.error('Error validating travel request:', error);

      // Fallback validation optimiste
      const fallbackResult = {
        confidence: 0.7, // Optimiste mais pas trop
        hasDestination: true,
        hasTimeframe: false,
        extractedInfo: {},
        missingCritical: [],
        reasoning: 'Validation failed, using optimistic approach',
        shouldProceed: true,
      };

      // Mettre en cache même le fallback
      ValidationAgent.validationCache.set(cacheKey, fallbackResult);
      return fallbackResult;
    } finally {
      // Toujours nettoyer le verrou
      ValidationAgent.isValidating.delete(cacheKey);
    }
  }

  /**
   * Generate clarifying questions based on missing information using intelligent LLM
   */
  async generateClarifyingQuestions(
    query: string,
    missingInfo: string[],
  ): Promise<ClarifyingQuestion> {
    try {
      const { object: question } = await generateObject({
        model: this.model,
        system: `You are a helpful travel assistant. Generate natural, friendly clarifying questions to gather missing travel information. Always respond in the SAME language as the original query.`,
        prompt: `Original query: "${query}"
Missing information: ${missingInfo.join(', ')}

Generate a helpful clarifying question to gather the missing information.`,
        schema: z.object({
          question: z.string().describe('The clarifying question to ask'),
          type: z
            .enum(['destination', 'duration', 'both'])
            .describe('Type of information being requested'),
          suggestions: z
            .array(z.string())
            .optional()
            .describe('Optional helpful suggestions for the user'),
        }),
        temperature: 0.3, // Un peu de créativité pour les questions
        maxOutputTokens: 300, // Limite pour les questions
      });

      return question;
    } catch (error) {
      console.error('Error generating clarifying questions:', error);

      // Fallback intelligent multilingue simple
      const lowerQuery = query.toLowerCase();

      // Détection de langue pour le fallback
      if (/\b(je|veux|partir|aller|visiter|voyage)\b/i.test(lowerQuery)) {
        // Français
        return {
          question:
            'Pour vous aider à créer le meilleur guide de voyage, pouvez-vous me dire où vous souhaitez aller et pour combien de temps ?',
          type: 'both' as const,
          suggestions: ['Exemple: "Paris 3 jours" ou "Tokyo 2 semaines"'],
        };
      } else if (/\b(quiero|viajar|ir|visitar)\b/i.test(lowerQuery)) {
        // Espagnol
        return {
          question:
            '¿Podrías decirme a dónde quieres viajar y por cuánto tiempo?',
          type: 'both' as const,
          suggestions: ['Ejemplo: "París 3 días" o "Tokio 2 semanas"'],
        };
      } else {
        // Anglais par défaut
        return {
          question:
            'To help create the best travel guide for you, could you tell me where you want to go and for how long?',
          type: 'both' as const,
          suggestions: ['Example: "Paris 3 days" or "Tokyo 2 weeks"'],
        };
      }
    }
  }
}
