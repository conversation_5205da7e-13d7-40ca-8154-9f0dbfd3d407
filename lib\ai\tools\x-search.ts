/**
 * Outil de recherche X (Twitter) avec Grok
 */

import { generateText, tool } from 'ai';
import { z } from 'zod';
import { xai } from '@ai-sdk/xai';

// ============================================================================
// TYPES
// ============================================================================

export interface XSearchResult {
  content: string;
  citations: any[];
  dateRange: string;
  handles: string[];
}

// ============================================================================
// FONCTION PRINCIPALE
// ============================================================================

/**
 * Recherche sur X (Twitter) avec Grok
 */
export async function searchX(
  query: string,
  options: {
    startDate?: string;
    endDate?: string;
    xHandles?: string[];
    maxResults?: number;
  } = {},
): Promise<XSearchResult> {
  const { startDate, endDate, xHandles = [], maxResults = 15 } = options;

  const searchStartDate =
    startDate ||
    new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  const searchEndDate = endDate || new Date().toISOString().split('T')[0];

  console.log(`🐦 Recherche X: "${query}"`);
  console.log(`📅 Période: ${searchStartDate} à ${searchEndDate}`);
  if (xHandles.length > 0) {
    console.log(`👤 Comptes: ${xHandles.join(', ')}`);
  }

  try {
    const { text, sources } = await generateText({
      model: xai('grok-3'),
      system: `Tu es un assistant qui recherche des posts X et retourne les résultats de manière structurée. Cite les sources au format [Source No.]. Va en profondeur dans la recherche.`,
      messages: [{ role: 'user', content: query }],
      maxOutputTokens: 4096,
      providerOptions: {
        xai: {
          searchParameters: {
            mode: 'on',
            fromDate: searchStartDate,
            toDate: searchEndDate,
            maxSearchResults: maxResults < 15 ? 15 : maxResults,
            returnCitations: true,
            sources: [
              xHandles && xHandles.length > 0
                ? { type: 'x', xHandles: xHandles }
                : { type: 'x' },
            ],
          },
        },
      },
    });

    console.log(`✅ Recherche X terminée: ${sources?.length || 0} citations`);

    return {
      content: text,
      citations: sources || [],
      dateRange: `${searchStartDate} à ${searchEndDate}`,
      handles: xHandles,
    };
  } catch (error) {
    console.error('❌ Erreur X search:', error);
    throw error;
  }
}

// ============================================================================
// OUTIL POUR VERCEL AI SDK
// ============================================================================

/**
 * Crée l'outil xSearch pour l'utiliser avec Vercel AI SDK
 */
export function createXSearchTool(dataStream?: any) {
  return tool({
    description:
      'Recherche sur X (Twitter) pour des informations récentes et discussions. Utilise Grok pour accéder aux posts X.',
    inputSchema: z.object({
      query: z
        .string()
        .max(150)
        .describe('Requête de recherche pour X (Twitter)'),
      startDate: z
        .string()
        .optional()
        .describe('Date de début au format YYYY-MM-DD'),
      endDate: z
        .string()
        .optional()
        .describe('Date de fin au format YYYY-MM-DD'),
      xHandles: z
        .array(z.string())
        .optional()
        .describe('Comptes X spécifiques à rechercher (sans @)'),
      maxResults: z
        .number()
        .optional()
        .describe('Nombre maximum de résultats (défaut: 15)'),
    }),
    execute: async (
      { query, startDate, endDate, xHandles, maxResults = 15 },
      { toolCallId },
    ) => {
      const searchStartDate =
        startDate ||
        new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          .toISOString()
          .split('T')[0];
      const searchEndDate = endDate || new Date().toISOString().split('T')[0];

      // Stream le début de la recherche
      if (dataStream) {
        dataStream.write({
          type: 'data-extreme_search',
          data: {
            kind: 'x_search',
            xSearchId: toolCallId,
            query: query,
            startDate: searchStartDate,
            endDate: searchEndDate,
            handles: xHandles || [],
            status: 'started',
          },
        });
      }

      try {
        const result = await searchX(query, {
          startDate,
          endDate,
          xHandles,
          maxResults,
        });

        // Stream le résultat
        if (dataStream) {
          dataStream.write({
            type: 'data-extreme_search',
            data: {
              kind: 'x_search',
              xSearchId: toolCallId,
              query: query,
              startDate: searchStartDate,
              endDate: searchEndDate,
              handles: xHandles || [],
              status: 'completed',
              result: result,
            },
          });
        }

        return result;
      } catch (error) {
        console.error('❌ Erreur X search:', error);

        if (dataStream) {
          dataStream.write({
            type: 'data-extreme_search',
            data: {
              kind: 'x_search',
              xSearchId: toolCallId,
              query: query,
              startDate: searchStartDate,
              endDate: searchEndDate,
              handles: xHandles || [],
              status: 'error',
            },
          });
        }

        throw error;
      }
    },
  });
}

export default createXSearchTool;
