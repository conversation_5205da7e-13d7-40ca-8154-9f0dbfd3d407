'use client';
/* eslint-disable react/no-unescaped-entities */

import React from 'react';
import {
  useFloatingWidgets,
  generateWidgetId,
  WIDGET_CONFIGS,
} from '@/components/FloatingWidgetManager';
import ETFHeatmap from '@/components/ETFHeatmap';
import ForexCrossRates from '@/components/ForexCrossRates';
import SymbolInfo from '@/components/SymbolInfo';
import StockScreener from '@/components/StockScreener';
import { StockFinancials } from '@/components/StockFinancials';
import StockPrice from '@/components/StockPrice';
import MarketTrending from '@/components/MarketTrending';
import FinancialChart from '@/components/FinancialChart';
import StockNews from '@/components/StockNews';

export default function TestSmoothWidgets() {
  const { openWidget } = useFloatingWidgets();

  const openMultipleWidgets = () => {
    // Ouvrir plusieurs widgets - les positions seront calculées automatiquement
    const widgets = [
      {
        id: generateWidgetId('etf-heatmap'),
        title: 'ETF Heatmap',
        component: <ETFHeatmap />,
        position: { x: 0, y: 0 }, // Sera recalculé
        size: WIDGET_CONFIGS.etfHeatmap.size,
      },
      {
        id: generateWidgetId('forex-cross-rates'),
        title: 'Forex Cross Rates',
        component: <ForexCrossRates />,
        position: { x: 0, y: 0 }, // Sera recalculé
        size: WIDGET_CONFIGS.forexCrossRates.size,
      },
      {
        id: generateWidgetId('symbol-info'),
        title: 'Symbol Info - AAPL',
        component: <SymbolInfo symbol="NASDAQ:AAPL" />,
        position: { x: 0, y: 0 }, // Sera recalculé
        size: WIDGET_CONFIGS.symbolInfo.size,
      },
    ];

    // Ouvrir les widgets un par un avec un délai pour voir le positionnement
    widgets.forEach((widget, index) => {
      setTimeout(() => openWidget(widget), index * 300);
    });
  };

  const openSingleWidget = () => {
    openWidget({
      id: generateWidgetId('etf-heatmap'),
      title: 'Test Fluidité - ETF Heatmap',
      component: <ETFHeatmap />,
      position: { x: 0, y: 0 }, // Sera recalculé automatiquement
      size: WIDGET_CONFIGS.etfHeatmap.size, // Taille compacte
    });
  };

  const openCompactDemo = () => {
    // Démonstration des différentes tailles compactes
    const compactWidgets = [
      {
        id: generateWidgetId('symbol-info-small'),
        title: 'Small - Symbol Info',
        component: <SymbolInfo symbol="NASDAQ:AAPL" />,
        position: { x: 0, y: 0 },
        size: { width: 320, height: 240 }, // Taille YouTube mobile
      },
      {
        id: generateWidgetId('forex-medium'),
        title: 'Medium - Forex Rates',
        component: <ForexCrossRates />,
        position: { x: 0, y: 0 },
        size: { width: 400, height: 300 }, // Taille intermédiaire
      },
    ];

    compactWidgets.forEach((widget, index) => {
      setTimeout(() => openWidget(widget), index * 400);
    });
  };

  const openEconomicWidgets = () => {
    // Démonstration de tous les widgets économiques
    const economicWidgets = [
      {
        id: generateWidgetId('stock-screener'),
        title: 'Stock Screener',
        component: <StockScreener />,
        position: { x: 0, y: 0 },
        size: WIDGET_CONFIGS.stockScreener.size,
      },
      {
        id: generateWidgetId('stock-financials'),
        title: 'Stock Financials - AAPL',
        component: <StockFinancials symbol="AAPL" />,
        position: { x: 0, y: 0 },
        size: WIDGET_CONFIGS.stockFinancials.size,
      },
      {
        id: generateWidgetId('stock-price'),
        title: 'Stock Price - AAPL',
        component: <StockPrice symbol="AAPL" />,
        position: { x: 0, y: 0 },
        size: WIDGET_CONFIGS.stockPrice.size,
      },
      {
        id: generateWidgetId('market-trending'),
        title: 'Market Trending',
        component: <MarketTrending />,
        position: { x: 0, y: 0 },
        size: WIDGET_CONFIGS.marketTrending.size,
      },
    ];

    economicWidgets.forEach((widget, index) => {
      setTimeout(() => openWidget(widget), index * 500);
    });
  };

  const openAllWidgets = () => {
    // Test de performance avec tous les widgets
    const allWidgets = [
      {
        id: generateWidgetId('financial-chart'),
        title: 'Financial Chart - AAPL',
        component: <FinancialChart ticker="AAPL" />,
        position: { x: 0, y: 0 },
        size: WIDGET_CONFIGS.financialChart.size,
      },
      {
        id: generateWidgetId('stock-news'),
        title: 'Stock News - AAPL',
        component: <StockNews ticker="AAPL" />,
        position: { x: 0, y: 0 },
        size: WIDGET_CONFIGS.stockNews.size,
      },
      {
        id: generateWidgetId('etf-heatmap-test'),
        title: 'ETF Heatmap',
        component: <ETFHeatmap />,
        position: { x: 0, y: 0 },
        size: WIDGET_CONFIGS.etfHeatmap.size,
      },
      {
        id: generateWidgetId('symbol-info-test'),
        title: 'Symbol Info - TSLA',
        component: <SymbolInfo symbol="NASDAQ:TSLA" />,
        position: { x: 0, y: 0 },
        size: WIDGET_CONFIGS.symbolInfo.size,
      },
    ];

    allWidgets.forEach((widget, index) => {
      setTimeout(() => openWidget(widget), index * 300);
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">
          Test de Fluidité des Widgets Flottants
        </h1>

        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">
            Tests de Performance
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
            <button
              type="button"
              onClick={openSingleWidget}
              className="px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 transform hover:scale-105 shadow-md"
            >
              <div className="text-lg font-medium">Widget Simple</div>
              <div className="text-sm opacity-90">Taille compacte</div>
            </button>

            <button
              type="button"
              onClick={openMultipleWidgets}
              className="px-4 py-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-md"
            >
              <div className="text-lg font-medium">Positionnement Auto</div>
              <div className="text-sm opacity-90">3 widgets intelligents</div>
            </button>

            <button
              type="button"
              onClick={openCompactDemo}
              className="px-4 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 transform hover:scale-105 shadow-md"
            >
              <div className="text-lg font-medium">Tailles YouTube</div>
              <div className="text-sm opacity-90">Small & Medium</div>
            </button>

            <button
              type="button"
              onClick={openEconomicWidgets}
              className="px-4 py-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-lg hover:from-orange-600 hover:to-orange-700 transition-all duration-200 transform hover:scale-105 shadow-md"
            >
              <div className="text-lg font-medium">Widgets Économiques</div>
              <div className="text-sm opacity-90">4 widgets financiers</div>
            </button>

            <button
              type="button"
              onClick={openAllWidgets}
              className="px-4 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-200 transform hover:scale-105 shadow-md"
            >
              <div className="text-lg font-medium">Test Performance</div>
              <div className="text-sm opacity-90">Tous widgets + fluidité</div>
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">
            Nouvelles Fonctionnalités Implémentées
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium text-green-600">
                📱 Tailles Compactes
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Small: 320×240 (YouTube mobile)</li>
                <li>• Medium: 400×300 (intermédiaire)</li>
                <li>• Large: 480×360 (compact desktop)</li>
                <li>• Similaire aux lecteurs YouTube</li>
              </ul>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium text-blue-600">
                🎯 Positionnement Intelligent
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Côté droit prioritaire</li>
                <li>• Côté gauche en secondaire</li>
                <li>• Évite les chevauchements</li>
                <li>• Cascade automatique si plein</li>
              </ul>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium text-purple-600">⚡ Performance</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• RequestAnimationFrame</li>
                <li>• Accélération GPU</li>
                <li>• Événements passifs</li>
                <li>• Transitions optimisées</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-xl p-6">
          <h3 className="text-lg font-medium text-yellow-800 mb-2">
            Instructions de Test
          </h3>
          <ul className="text-yellow-700 space-y-1 text-sm">
            <li>
              • <strong>Widget Simple</strong> : Teste la taille compacte et la
              fluidité
            </li>
            <li>
              • <strong>Positionnement Auto</strong> : Observe le placement
              intelligent (droite → gauche)
            </li>
            <li>
              • <strong>Tailles YouTube</strong> : Compare avec les lecteurs
              YouTube
            </li>
            <li>
              • <strong>Widgets Économiques</strong> : Teste tous les widgets
              financiers (Screener, Financials, Price, Trending)
            </li>
            <li>
              • <strong>Test Performance</strong> : Teste la fluidité avec
              plusieurs widgets simultanés (Chart, News, Heatmap, Symbol)
            </li>
            <li>• Glissez-déposez les widgets pour tester la fluidité</li>
            <li>• Redimensionnez avec le coin inférieur droit</li>
            <li>
              • Ouvrez plusieurs widgets pour voir l'évitement des collisions
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
