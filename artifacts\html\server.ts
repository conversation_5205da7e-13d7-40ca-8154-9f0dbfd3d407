import { streamObject } from 'ai';
import { z } from 'zod';
import { myProvider } from '@/lib/ai/providers';
import { createDocumentHandler } from '@/lib/artifacts/server';
import { updateDocumentPrompt } from '@/lib/ai/prompts';
import { ValidationAgent } from '@/lib/ai/workflows/agents/validation-agent';

const htmlPrompt = `
You are an expert web developer specializing in creating beautiful, interactive web applications.
Your task is to create a visually appealing HTML page based on the user's request.

CRITICAL INSTRUCTION: You MUST return a valid JSON object with EXACTLY these three properties:
- htmlContent: The HTML structure of the page (string)
- cssContent: The CSS styling for the page (string)
- jsContent: The JavaScript code to make the page interactive (string)

RESPONSE FORMAT EXAMPLE:
{
  "htmlContent": "<div class='container'>...</div>",
  "cssContent": ".container { ... }",
  "jsContent": "document.addEventListener('DOMContentLoaded', function() { ... });"
}

DO NOT include any markdown formatting, code blocks, or explanations in your response.
DO NOT use \`\`\` or any other markdown syntax.
ONLY return the raw JSON object with the three required properties.
All three properties MUST be present and MUST be strings. Do not include any other properties.

IMPORTANT GUIDELINES FOR PROFESSIONAL PRESENTATION:
1. Create a PREMIUM, VISUALLY CONSISTENT presentation that resembles a high-end travel agency website
2. Use modern HTML5, CSS3, and ES6+ JavaScript with CONSISTENT styling throughout
3. Utilize Tailwind CSS 2.2.19 for styling (it's already included via CDN)
4. CRITICAL: Maintain CONSISTENT spacing, fonts, and color schemes throughout the entire document
5. Make the design fully responsive with STABLE layouts that don't shift or break on different screen sizes
6. Use subtle animations and transitions ONLY where they enhance understanding (avoid excessive movement)
7. Ensure ALL interactive elements have clear visual affordances and consistent behavior
8. Use a clean, professional layout with GENEROUS whitespace and CONSISTENT typography
9. Organize content in a logical, hierarchical structure with clear visual hierarchy
10. Use semantic HTML elements for better accessibility and structure
11. IMPORTANT: Ensure all cards, containers, and UI elements maintain CONSISTENT heights and widths
12. Use a PROFESSIONAL color palette with 2-3 primary colors and complementary accent colors
13. Ensure ALL text has sufficient contrast against backgrounds for readability
14. Use high-quality imagery with consistent dimensions and styling

SPECIFIC CONTENT TYPES FOR PROFESSIONAL TRAVEL ITINERARIES:
- For travel itineraries: Create a PREMIUM, VISUALLY CONSISTENT HTML travel handbook with:
  * HERO IMAGE REQUIREMENT: You MUST start with a professional hero image section at the top of the page
  * The hero image must be specific to the requested destination (e.g., Paris, Rome, Tokyo)
  * Use these specific high-quality image URLs for popular destinations:
    - Rome: https://images.pexels.com/photos/532263/pexels-photo-532263.jpeg
    - Paris: https://images.pexels.com/photos/699466/pexels-photo-699466.jpeg
    - London: https://images.pexels.com/photos/460672/pexels-photo-460672.jpeg
    - New York: https://images.pexels.com/photos/290386/pexels-photo-290386.jpeg
    - Tokyo: https://images.pexels.com/photos/1134166/pexels-photo-1134166.jpeg
  * The hero image MUST be full-width, take up 70% of the viewport height, and have a professional dark gradient overlay
  * Include the destination name in large, elegant typography and a concise, compelling tagline overlaid on the hero image
  * CONSISTENT LAYOUT: After the hero image, use a CONSISTENT grid layout with the following sections:
  * NAVIGATION: Create a sticky navigation bar with smooth-scrolling links to each major section
  * ITINERARY: A detailed, hour-by-hour itinerary for each day with specific timings in a VISUALLY CONSISTENT timeline format
  * ACTIVITIES: Include 4-6 specific activities per day with exact locations, opening hours, and admission prices in UNIFORM CARDS with CONSISTENT heights
  * ATTRACTIONS: For each attraction, provide 3-5 paragraphs of detailed information in VISUALLY CONSISTENT containers
  * MAP: Include ONE SINGLE professional map container for OpenStreetMap with all points of interest marked with CONSISTENT styling
  * RESTAURANTS: Present restaurants in UNIFORM CARDS with CONSISTENT heights, including menu recommendations, price ranges, and reservation information
  * PHRASES: A comprehensive section with at least 20 useful local phrases in a CLEAN, PROFESSIONAL table format
  * TIPS: Detailed practical tips in VISUALLY CONSISTENT sections with clear icons and formatting
  * BUDGET: A detailed budget breakdown with specific prices in a PROFESSIONAL, EASY-TO-READ table format
  * SPECIAL MOMENTS: A special moments section with CONSISTENT card styling for each experience
  * SHOPPING: Include specific shopping recommendations in UNIFORM CARDS with CONSISTENT heights
  * CUISINE: Add a section on local cuisine with specific dishes in a VISUALLY APPEALING, CONSISTENT format
  * CONSISTENT STYLING: Use the SAME styling patterns, card designs, and visual elements throughout the entire document
  * PROFESSIONAL TYPOGRAPHY: Use a consistent, professional font hierarchy with clear heading styles
  * RESPONSIVE DESIGN: Ensure all elements maintain their alignment and proportions across all screen sizes

IMPORTANT: The system will automatically detect travel-related queries, even when the user doesn't explicitly ask for an itinerary. For example, if the user simply says "Je veux partir à Rome 2 jours" or "I want to visit Tokyo", the system will create a travel handbook. This is MANDATORY for ALL travel-related requests in ANY language, especially French requests like "Je veux partir à...", "Je veux visiter...", "Je voudrais aller à...".

ENHANCED CONTENT ACCURACY AND DETAIL REQUIREMENTS:
- Include EXTREMELY DETAILED and FACTUAL information about the destination with at least 3-5 paragraphs of description for each major attraction
- For EACH attraction, provide COMPREHENSIVE historical background, architectural details, cultural significance, and visitor experience
- Include EXACT addresses, GPS coordinates, opening hours (including seasonal variations), admission prices (including all ticket options), and insider tips
- For EACH attraction, include at least 5-7 specific points of interest within the attraction itself
- Recommend at least 8-10 ACTUAL restaurants with detailed descriptions of their ambiance, specialties, signature dishes, price ranges, and chef information
- For EACH restaurant, include 3-4 specific menu recommendations with descriptions and approximate prices
- Include COMPREHENSIVE transportation information with EXACT bus/metro lines, route numbers, frequencies, operating hours, and PRECISE ticket prices
- Provide at least 25-30 GENUINE local phrases with original language, phonetic pronunciation, and contextual usage examples
- Create a DETAILED budget breakdown with SPECIFIC prices for different accommodation categories, meal types, attraction tickets, transportation options, and shopping items
- Include at least 10-15 AUTHENTIC cultural insights and local customs with explanations of their historical origins and contemporary relevance
- Provide PRECISE weather information for EACH month including average temperatures (high/low), precipitation, humidity, and appropriate clothing recommendations
- Recommend at least 15-20 REAL local dishes with detailed descriptions of ingredients, preparation methods, cultural significance, and SPECIFIC restaurants where to find them
- Include at least 10-12 SPECIFIC shopping recommendations with store names, exact locations, specialty products, price ranges, and opening hours
- Add DETAILED information about local events, festivals, and seasonal activities with EXACT dates, locations, and historical significance
- Include PRACTICAL safety information specific to the destination with emergency numbers, location of hospitals, and area-specific precautions

PROFESSIONAL TECHNICAL REQUIREMENTS:
- Use ES6+ features like arrow functions, template literals, and destructuring for CLEAN, MAINTAINABLE code
- Implement ROBUST responsive design using Tailwind CSS 2.2.19 responsive utilities (sm:, md:, lg:, xl:)
- Create SUBTLE, PROFESSIONAL animations using CSS transitions and transforms (avoid excessive movement)
- Use Flexbox and Grid for STABLE, CONSISTENT layouts that don't break at different screen sizes
- Implement dark/light mode toggle with SEAMLESS transitions and CONSISTENT styling in both modes
- Add SUBTLE micro-interactions that enhance user experience without being distracting
- CRITICAL: Ensure ALL JavaScript functionality degrades gracefully if JavaScript is disabled
- IMPORTANT: Use event delegation for better performance with multiple interactive elements
- PROFESSIONAL: Implement lazy loading for images to improve initial page load performance
- ESSENTIAL: Use CONSISTENT error handling for all interactive elements
- STABILITY: Ensure all interactive elements maintain their position and don't cause layout shifts

PROFESSIONAL TAILWIND CSS IMPLEMENTATION:
- Use Tailwind CSS 2.2.19 utility classes for CONSISTENT styling throughout (it's already included via CDN)
- For custom colors, use the provided primary color classes (.bg-primary-500, .text-primary-700, etc.) CONSISTENTLY
- Avoid using @apply or custom Tailwind configurations as they won't work in this environment
- Stick to standard Tailwind CSS 2.2.19 classes and the custom primary color classes provided
- CRITICAL: Maintain a CONSISTENT spacing system using Tailwind's spacing utilities (p-4, m-6, gap-3, etc.)
- IMPORTANT: Use a CONSISTENT typography scale with Tailwind's font size utilities (text-sm, text-lg, etc.)
- PROFESSIONAL: Implement a COHESIVE color scheme using 2-3 primary colors and complementary accent colors
- ESSENTIAL: Ensure ALL text has sufficient contrast against backgrounds using appropriate text/bg color combinations
- CONSISTENCY: Use the SAME border radius utilities (rounded-lg, rounded-xl, etc.) throughout the document
- STABILITY: Use Tailwind's container utilities and grid system for STABLE, CONSISTENT layouts
- RESPONSIVE: Implement a truly responsive design that looks PROFESSIONAL at ALL screen sizes

PROFESSIONAL VISUAL AND INTERACTIVE ELEMENTS:
- Create VISUALLY CONSISTENT collapsible sections with UNIFORM styling and behavior
- Include ONE SINGLE professional OpenStreetMap container with all points of interest marked (see PROFESSIONAL MAP INSTRUCTIONS below)
- Add image galleries with CONSISTENT dimensions and styling for all attractions
- Create a PROFESSIONAL budget calculator with CLEAN, CONSISTENT styling that matches the document
- Implement a PREMIUM day-by-day navigation system with smooth scrolling and CONSISTENT visual indicators
- Add a print-friendly stylesheet with PROFESSIONAL formatting for printed itineraries
- Include a weather widget with CONSISTENT styling that integrates seamlessly with the design
- Create a packing checklist with UNIFORM interactive checkboxes and CONSISTENT styling
- Add a language toggle for the local phrases section with PROFESSIONAL design
- Implement a favorites system with CONSISTENT visual indicators
- CRITICAL: Ensure ALL interactive elements have CONSISTENT hover states, focus states, and animations
- IMPORTANT: Use a CONSISTENT color scheme for all interactive elements
- ESSENTIAL: Maintain UNIFORM padding, margins, and spacing for all interactive elements
- PROFESSIONAL: Add subtle loading states and transitions for all interactive elements

ENHANCED INTERACTIVE MAP INSTRUCTIONS (FOLLOW EXACTLY):
- MANDATORY: Create ONE SINGLE professional map container for the entire itinerary that MUST FUNCTION CORRECTLY
- CRITICAL: Place this map container in a VISUALLY BALANCED position after the introduction section
- REQUIRED: Use a div with id="main-map" for the main map container
- ESSENTIAL: Add a data-destination attribute with the PRECISE name of the main location (e.g., data-destination="Rome, Italy")
- IMPORTANT: Make the map container LARGE (at least 600px height) and FULL WIDTH for better visibility of ALL points
- REQUIRED: Wrap the map in a container div with class="container mx-auto px-4 py-8"
- PROFESSIONAL: Add a subtle shadow and border to the map container for a premium look
- CRITICAL: Ensure the map has CONSISTENT styling with the rest of the document
- FUNCTIONAL: Include the necessary JavaScript to initialize and render the OpenStreetMap correctly

COPY THIS HTML EXACTLY FOR THE PROFESSIONAL MAP CONTAINER:
<div class="container mx-auto px-4 py-8">
  <h2 class="text-3xl font-bold mb-6">Interactive Map</h2>
  <div id="main-map" data-destination="DESTINATION_NAME" class="h-[600px] w-full rounded-lg mb-8 border border-gray-200 shadow-lg"></div>
</div>

INCLUDE THIS JAVASCRIPT TO ENSURE THE MAP FUNCTIONS CORRECTLY:
- Add a script tag in the HTML to load Leaflet.js: '<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>'
- Add a link tag for Leaflet CSS: '<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />'
- Create a script that initializes the map when the document loads
- Get the map container element with id="main-map"
- Get the destination from the data-destination attribute
- Initialize the map centered on the destination coordinates
- Add OpenStreetMap tiles with proper attribution
- Collect all elements with data-poi attributes
- For each POI element:
  * Parse the POI data from the data-poi attribute
  * Create a custom marker with numbered icon based on type and day
  * Add the marker to the map with a popup showing name, day, and description
  * Add a click event to the element to open the popup and center the map
  * Add hover styling to make the element interactive
- Fit the map to show all markers
- If no markers but destination is provided, geocode the destination and center the map

ADD THIS CSS TO STYLE THE MAP MARKERS:
- Create styles for marker containers with transparent background
- Style markers as circles with different colors based on type (blue for attractions, red for restaurants, green for hotels)
- Add hover effects for interactive elements
- Ensure markers are numbered and clearly visible
- Add subtle shadows for a professional look
- Make interactive elements have clear visual affordances

COMPREHENSIVE POINTS OF INTEREST INSTRUCTIONS:
- For ABSOLUTELY EVERY attraction, landmark, restaurant, or hotel mentioned in the itinerary, you MUST add a data-poi attribute
- CRITICAL: EVERY point mentioned in the text MUST appear on the map - this is MANDATORY
- The data-poi attribute MUST contain: name, latitude, longitude, day number, type, and a detailed description
- Format: data-poi='{"name":"ATTRACTION_NAME","lat":"LATITUDE","lng":"LONGITUDE","day":"Day X","type":"attraction|restaurant|hotel","description":"DETAILED_DESCRIPTION"}'
- Example: <h3 data-poi='{"name":"Colosseum","lat":"41.8902","lng":"12.4922","day":"Day 1","type":"attraction","description":"Ancient Roman amphitheater built in 70-80 AD, once hosting gladiatorial contests"}'>Colosseum</h3>
- IMPORTANT: Use REAL, ACCURATE coordinates for each location - VERIFY all coordinates
- CRITICAL: Always include the "type" field to properly categorize each point of interest (must be "attraction", "restaurant", or "hotel")
- CONSISTENT: Use the SAME format for ALL points of interest throughout the document
- COMPREHENSIVE: Include at least 8-10 points of interest for EACH day of the itinerary
- DETAILED: Provide a substantial description (at least 100 characters) for each point of interest
- For Rome: Colosseum (41.8902, 12.4922), Vatican (41.9022, 12.4539), Trevi Fountain (41.9009, 12.4833), Spanish Steps (41.9058, 12.4823), Pantheon (41.8986, 12.4768)
- For Paris: Eiffel Tower (48.8584, 2.2945), Louvre (48.8606, 2.3376), Notre Dame (48.8530, 2.3499), Arc de Triomphe (48.8738, 2.2950), Montmartre (48.8867, 2.3431)
- For London: Big Ben (51.5007, -0.1246), Tower Bridge (51.5055, -0.0754), Buckingham Palace (51.5014, -0.1419), British Museum (51.5194, -0.1269), London Eye (51.5033, -0.1195)
- For New York: Statue of Liberty (40.6892, -74.0445), Times Square (40.7580, -73.9855), Empire State (40.7484, -73.9857), Central Park (40.7812, -73.9665), Brooklyn Bridge (40.7061, -73.9969)
- For Tokyo: Tokyo Tower (35.6586, 139.7454), Shibuya Crossing (35.6595, 139.7004), Senso-ji Temple (35.7147, 139.7966), Meiji Shrine (35.6763, 139.6993), Tokyo Skytree (35.7101, 139.8107)
- For other locations, use accurate coordinates for major landmarks and attractions

ENHANCED RESTAURANT PRESENTATION REQUIREMENTS:
- Create a DEDICATED RESTAURANT SECTION with a clear heading and introduction
- Present at least 8-10 CAREFULLY SELECTED restaurants with DETAILED information
- Create VISUALLY CONSISTENT restaurant cards with ABSOLUTELY UNIFORM heights and widths (CRITICAL)
- Each restaurant card MUST have the EXACT SAME structure, layout, and component positioning
- Include a high-quality image for each restaurant with IDENTICAL dimensions (MANDATORY)
- ENFORCE a strict character limit for descriptions to maintain CONSISTENT card heights
- For EACH restaurant, include:
  * EXACT restaurant name and address with neighborhood location
  * SPECIFIC cuisine type with regional specialties
  * PRECISE price range with example prices for appetizers, mains, and desserts
  * DETAILED description (exactly 2-3 sentences, strictly enforced length)
  * CONSISTENT operating hours in the same format
  * UNIFORM reservation information (phone number, website)
  * 3-4 SPECIFIC signature dish recommendations with brief descriptions
  * IDENTICAL rating display format (e.g., 5-star system) with the SAME styling
- Add a "Reserve" button with IDENTICAL styling, positioning, and hover effects on EVERY card
- Display restaurant cards in a CLEAN GRID LAYOUT with:
  * CONSISTENT card spacing
  * PROPER responsive design
  * IDENTICAL card dimensions regardless of content
- CRITICAL: Restaurant cards MUST maintain PERFECT alignment in the grid at ALL screen sizes
- ESSENTIAL: Apply the EXACT SAME shadow, border, and styling to ALL restaurant cards
- MANDATORY: Restaurant cards should NEVER vary in height based on content length
- IMPORTANT: Ensure the restaurant section doesn't dominate the page or push other content too far down
- CRITICAL: ALL restaurant recommendations MUST be presented in a horizontal carousel format with navigation arrows
- REQUIRED: Use consistent card heights and professional styling for all restaurant cards in the carousel

The final result should look like a PREMIUM, PROFESSIONALLY DESIGNED website that effectively communicates the requested information in a VISUALLY CONSISTENT and engaging way.
`;

export const htmlDocumentHandler = createDocumentHandler<'html'>({
  kind: 'html',
  onCreateDocument: async ({ title, originalQuery, dataStream }) => {
    let draftContent = '';

    try {
      console.log('Starting HTML generation for:', title);

      // Créer un contenu par défaut initial pour montrer que quelque chose se passe
      const initialContent = JSON.stringify({
        htmlContent:
          '<div id="app" class="p-8 text-center"><h1 class="text-2xl font-bold mb-4">Generating your travel itinerary...</h1><p>Please wait while we create a detailed travel plan for you.</p><div class="mt-4 flex justify-center"><div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div></div></div>',
        cssContent: 'body { font-family: system-ui; }',
        jsContent: 'console.log("Generating HTML artifact...");',
      });

      dataStream.write({
        type: 'data-htmlDelta',
        data: initialContent,
        transient: true,
      });

      // Determine the best query to use (prefer original user query over generated title)
      const userQuery = (originalQuery && originalQuery.trim().length > 0)
        ? originalQuery
        : title;

      // Check if this is a travel-related query using intelligent LLM detection
      console.log('Checking if travel query:', userQuery);

      const validationAgent = new ValidationAgent(
        myProvider.languageModel('artifact-model'),
      );

      // Use the validation agent to detect travel intent
      const validationResult =
        await validationAgent.validateTravelRequest(userQuery);

      // Consider it a travel query if the LLM detected any travel intent (even low confidence)
      const isTravelQuery =
        validationResult.confidence > 0.1 ||
        validationResult.hasDestination ||
        validationResult.missingCritical.includes('destination') ||
        validationResult.missingCritical.includes('timeframe');

      console.log('Travel detection result:', {
        isTravelQuery,
        confidence: validationResult.confidence,
        hasDestination: validationResult.hasDestination,
        missingCritical: validationResult.missingCritical,
        reasoning: validationResult.reasoning,
      });

      let content: string | undefined;

      if (isTravelQuery) {
        console.log('🧳 TRAVEL QUERY DETECTED - Checking validation:', userQuery);
        console.log('Validation result:', validationResult);

        // STRICT VALIDATION: Only proceed if we have sufficient information
        if (
          validationResult.shouldProceed &&
          validationResult.confidence >= 0.7 &&
          validationResult.hasDestination &&
          validationResult.extractedInfo.destination &&
          validationResult.extractedInfo.destination.length > 2 // Au moins 3 caractères pour la destination
        ) {
          // Use ONLY the advanced trip planning workflow - but only with sufficient info
          console.log(
            '✅ SUFFICIENT INFO - Using ADVANCED workflow for:',
            userQuery,
          );

          const { generateAdvancedTripPlan } = await import(
            '@/lib/ai/workflows/advanced-trip-planning'
          );

          const tripPlanOutput = await generateAdvancedTripPlan({
            query: userQuery,
            dataStream,
          });

          // Pour les voyages, utiliser le contenu généré par le workflow
          // mais ne pas l'afficher dans le chat (sera géré par l'artifact)
          content = JSON.stringify(tripPlanOutput);
        } else {
          // Information insufficient - ask clarifying questions
          console.log(
            '❌ INSUFFICIENT INFO - Asking clarifying questions for:',
            userQuery,
            'Missing:',
            validationResult.missingCritical,
          );

          const clarifyingQuestion =
            await validationAgent.generateClarifyingQuestions(
              userQuery,
              validationResult.missingCritical,
            );

          // Create a clarifying question HTML
          const questionHtml = `
            <div id="app" class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
              <div class="max-w-2xl w-full bg-white rounded-xl shadow-lg p-8">
                <div class="text-center mb-8">
                  <div class="text-6xl mb-4">✈️</div>
                  <h1 class="text-3xl font-bold text-gray-800 mb-4">Planifions votre voyage !</h1>
                  <p class="text-gray-600 mb-6">J'ai besoin de quelques informations supplémentaires pour créer le meilleur guide de voyage possible.</p>
                </div>

                <div class="bg-blue-50 rounded-lg p-6 mb-6">
                  <h2 class="text-xl font-semibold text-blue-800 mb-3">Question :</h2>
                  <p class="text-blue-700 text-lg">${clarifyingQuestion.question}</p>
                </div>

                ${
                  clarifyingQuestion.suggestions
                    ? `
                <div class="mb-6">
                  <h3 class="text-lg font-medium text-gray-700 mb-3">Exemples :</h3>
                  <div class="grid gap-2">
                    ${clarifyingQuestion.suggestions
                      .map(
                        (suggestion) =>
                          `<div class="bg-gray-50 rounded-lg p-3 text-gray-600 border border-gray-200">
                        💡 ${suggestion}
                      </div>`,
                      )
                      .join('')}
                  </div>
                </div>
                `
                    : ''
                }

                <div class="text-center">
                  <p class="text-sm text-gray-500">
                    Répondez simplement dans le chat avec les informations demandées,
                    et je créerai immédiatement votre guide de voyage personnalisé !
                  </p>
                </div>
              </div>
            </div>
          `;

          content = JSON.stringify({
            htmlContent: questionHtml,
            cssContent:
              'body { font-family: system-ui, -apple-system, sans-serif; margin: 0; }',
            jsContent: 'console.log("Waiting for user clarification...");',
          });

          dataStream.write({
            type: 'data-htmlDelta',
            data: content,
            transient: true,
          });
        }
      } else {
        // Use the standard approach for non-travel queries
        const { fullStream } = streamObject({
          model: myProvider.languageModel('artifact-model'),
          system: htmlPrompt,
          prompt: title,
          schema: z.object({
            htmlContent: z.string().describe('HTML structure of the page'),
            cssContent: z.string().describe('CSS styling for the page'),
            jsContent: z.string().describe('JavaScript code for interactivity'),
          }),
        });

        let hasReceivedContent = false;

        for await (const delta of fullStream) {
          const { type } = delta;

          if (type === 'object') {
            const { object } = delta;
            hasReceivedContent = true;

            // Extraire les propriétés avec des valeurs par défaut
            const htmlContent =
              object.htmlContent || '<div id="app">Loading content...</div>';
            const cssContent =
              object.cssContent || 'body { font-family: system-ui; }';
            const jsContent =
              object.jsContent || 'console.log("HTML artifact loaded");';

            // Créer un objet validé
            const validatedContent = {
              htmlContent,
              cssContent,
              jsContent,
            };

            content = JSON.stringify(validatedContent);

            console.log('Sending HTML delta to client');

            dataStream.write({
              type: 'data-htmlDelta',
              data: content,
              transient: true,
            });
          }
        }

        // Si aucun contenu n'a été reçu après la boucle, c'est probablement un problème avec le modèle
        if (!hasReceivedContent) {
          console.warn('No content received from model for:', title);
          throw new Error('No content received from model');
        }
      }

      // Set the final content
      if (content) {
        draftContent = content;
      }
    } catch (error) {
      console.error('Error in HTML document handler:', error);

      // Créer un contenu d'erreur spécifique pour les itinéraires de voyage
      const isTravel =
        title.toLowerCase().includes('monaco') ||
        title.toLowerCase().includes('voyage') ||
        title.toLowerCase().includes('trip') ||
        title.toLowerCase().includes('travel') ||
        title.toLowerCase().includes('visit') ||
        title.toLowerCase().includes('partir') ||
        title.toLowerCase().includes('visiter') ||
        title.toLowerCase().includes('aller à') ||
        title.toLowerCase().includes('itinéraire') ||
        title.toLowerCase().includes('itinerary');

      let errorHtml = '<div id="app" class="p-8 bg-red-50 rounded-lg">';
      errorHtml +=
        '<h1 class="text-2xl font-bold text-red-700 mb-4">An error occurred</h1>';

      if (isTravel) {
        errorHtml += `<p class="mb-4">We couldn't generate your travel itinerary for "${title}". This might be due to:</p>`;
        errorHtml += '<ul class="list-disc pl-5 mb-4">';
        errorHtml += '<li>Temporary service unavailability</li>';
        errorHtml += '<li>Limited information about this destination</li>';
        errorHtml +=
          '<li>Connection issues with our travel data providers</li>';
        errorHtml += '</ul>';
        errorHtml +=
          '<p>Please try again with a different destination or be more specific about your travel plans.</p>';
      } else {
        errorHtml +=
          '<p>We encountered an error while generating your content. Please try again or modify your request.</p>';
      }

      errorHtml +=
        '<div class="mt-4 p-4 bg-gray-100 rounded text-sm overflow-auto max-h-32">';
      errorHtml += `<code>Error details: ${error instanceof Error ? error.message : 'Unknown error'}</code>`;
      errorHtml += '</div>';
      errorHtml += '</div>';

      // En cas d'erreur, envoyer un contenu par défaut
      const defaultContent = JSON.stringify({
        htmlContent: errorHtml,
        cssContent: 'body { font-family: system-ui; }',
        jsContent: `console.error("Error generating HTML artifact:", ${JSON.stringify(error instanceof Error ? error.message : 'Unknown error')});`,
      });

      dataStream.write({
        type: 'data-htmlDelta',
        data: defaultContent,
        transient: true,
      });

      draftContent = defaultContent;
    }

    return draftContent;
  },
  onUpdateDocument: async ({ document, description, dataStream }) => {
    let draftContent = '';
    let currentContent: {
      htmlContent: string;
      cssContent: string;
      jsContent: string;
    };

    try {
      currentContent = JSON.parse(document.content || '{}');
    } catch (e) {
      currentContent = {
        htmlContent: '<div id="app"></div>',
        cssContent: 'body { font-family: system-ui; }',
        jsContent: 'document.getElementById("app").innerHTML = "Hello World";',
      };
    }

    const { fullStream } = streamObject({
      model: myProvider.languageModel('artifact-model'),
      system: updateDocumentPrompt(
        JSON.stringify(currentContent, null, 2),
        'html',
      ),
      prompt: description,
      schema: z.object({
        htmlContent: z.string().describe('HTML structure of the page'),
        cssContent: z.string().describe('CSS styling for the page'),
        jsContent: z.string().describe('JavaScript code for interactivity'),
      }),
    });

    try {
      console.log('Starting HTML update for description:', description);

      // Créer un contenu par défaut initial pour montrer que quelque chose se passe
      const initialContent = JSON.stringify({
        htmlContent: currentContent.htmlContent.replace(
          /<\/body>/,
          '<div class="fixed top-0 right-0 m-4 p-4 bg-blue-500 text-white rounded shadow-lg z-50">Updating content...</div></body>',
        ),
        cssContent: currentContent.cssContent,
        jsContent: currentContent.jsContent,
      });

      dataStream.write({
        type: 'data-htmlDelta',
        data: initialContent,
        transient: true,
      });

      let hasReceivedContent = false;

      for await (const delta of fullStream) {
        const { type } = delta;

        if (type === 'object') {
          const { object } = delta;
          hasReceivedContent = true;

          // Extraire les propriétés avec des valeurs par défaut
          const htmlContent =
            object.htmlContent ||
            currentContent.htmlContent ||
            '<div id="app">Loading content...</div>';
          const cssContent =
            object.cssContent ||
            currentContent.cssContent ||
            'body { font-family: system-ui; }';
          const jsContent =
            object.jsContent ||
            currentContent.jsContent ||
            'console.log("HTML artifact loaded");';

          // Créer un objet validé
          const validatedContent = {
            htmlContent,
            cssContent,
            jsContent,
          };

          const content = JSON.stringify(validatedContent);

          console.log('Sending updated HTML delta to client');

          dataStream.write({
            type: 'data-htmlDelta',
            data: content,
            transient: true,
          });

          draftContent = content;
        }
      }

      // Si aucun contenu n'a été reçu après la boucle, c'est probablement un problème avec le modèle
      if (!hasReceivedContent) {
        console.warn('No content received from model for update:', description);
        throw new Error('No content received from model for update');
      }
    } catch (error) {
      console.error('Error in HTML document update handler:', error);

      // Créer un contenu d'erreur spécifique pour les itinéraires de voyage
      const isTravel =
        description.toLowerCase().includes('monaco') ||
        description.toLowerCase().includes('voyage') ||
        description.toLowerCase().includes('trip') ||
        description.toLowerCase().includes('travel') ||
        description.toLowerCase().includes('visit') ||
        description.toLowerCase().includes('partir') ||
        description.toLowerCase().includes('visiter') ||
        description.toLowerCase().includes('aller à') ||
        description.toLowerCase().includes('itinéraire') ||
        description.toLowerCase().includes('itinerary');

      let errorHtml = '';

      // Conserver le contenu existant mais ajouter un message d'erreur
      if (currentContent.htmlContent) {
        // Insérer un message d'erreur au début du body
        if (currentContent.htmlContent.includes('<body')) {
          errorHtml = currentContent.htmlContent.replace(
            /<body[^>]*>/,
            (match) =>
              `${match}<div class="p-4 bg-red-50 border border-red-200 rounded-lg mb-4">
              <h2 class="text-xl font-bold text-red-700 mb-2">Update Error</h2>
              <p>${isTravel ? "We couldn't update your travel itinerary. Please try again with different instructions." : 'We encountered an error while updating your content.'}</p>
              <details class="mt-2">
                <summary class="cursor-pointer text-sm text-gray-600">Error details</summary>
                <pre class="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">${error instanceof Error ? error.message : 'Unknown error'}</pre>
              </details>
            </div>`,
          );
        } else {
          // Si pas de balise body, ajouter le message au début
          errorHtml = `<div class="p-4 bg-red-50 border border-red-200 rounded-lg mb-4">
            <h2 class="text-xl font-bold text-red-700 mb-2">Update Error</h2>
            <p>${isTravel ? "We couldn't update your travel itinerary. Please try again with different instructions." : 'We encountered an error while updating your content.'}</p>
            <details class="mt-2">
              <summary class="cursor-pointer text-sm text-gray-600">Error details</summary>
              <pre class="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">${error instanceof Error ? error.message : 'Unknown error'}</pre>
            </details>
          </div>${currentContent.htmlContent}`;
        }
      } else {
        // Contenu HTML par défaut si aucun contenu existant
        errorHtml = `<div id="app" class="p-8 bg-red-50 rounded-lg">
          <h1 class="text-2xl font-bold text-red-700 mb-4">An error occurred</h1>
          <p>We encountered an error while updating your content. Please try again or modify your request.</p>
          <div class="mt-4 p-4 bg-gray-100 rounded text-sm overflow-auto max-h-32">
            <code>Error details: ${error instanceof Error ? error.message : 'Unknown error'}</code>
          </div>
        </div>`;
      }

      // En cas d'erreur, envoyer un contenu par défaut basé sur le contenu actuel
      const defaultContent = JSON.stringify({
        htmlContent: errorHtml,
        cssContent:
          currentContent.cssContent || 'body { font-family: system-ui; }',
        jsContent: `console.error("Error updating HTML artifact:", ${JSON.stringify(error instanceof Error ? error.message : 'Unknown error')});
                   ${currentContent.jsContent || ''}`,
      });

      dataStream.write({
        type: 'data-htmlDelta',
        data: defaultContent,
        transient: true,
      });

      draftContent = defaultContent;
    }

    return draftContent;
  },
});
