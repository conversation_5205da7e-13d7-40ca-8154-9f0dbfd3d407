/* eslint-disable @next/next/no-img-element */
'use client';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { Drawer, DrawerContent } from '@/components/ui/drawer';
import { useIsMobile } from '@/hooks/use-mobile';
import type { Research } from '@/lib/ai/tools/extreme-search';
import type { ToolUIPart } from 'ai';
import React, { useEffect, useState, memo, useMemo, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css';
import { useExtremeSearchAnnotations } from './extreme-search-annotations-provider';
import { transformCitations } from '@/components/citation-link';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronDown,
  ChevronRight,
  ArrowUpRight,
  Globe,
  Search,
  ExternalLink,
  Target,
  BarChart3,
} from 'lucide-react';
import { TextShimmer } from '@/components/core/text-shimmer';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

// Types pour les sources de recherche extrême
interface ExtremeSearchSource {
  title: string;
  url: string;
  content: string;
  favicon?: string;
  publishedDate?: string;
  published_date?: string;
  author?: string;
}

// Types for timeline items
interface SearchQuery {
  id: string;
  query: string;
  status: string | { title: string };
  sources: ExtremeSearchSource[];
  content: Array<{
    title: string;
    url: string;
    text: string;
    favicon?: string;
  }>;
}
interface CodeExecution {
  id: string;
  title: string;
  code: string;
  status: 'running' | 'completed' | 'error';
  result?: string;
  charts?: any[];
}

// Source Card Component
const ExtremeSourceCard: React.FC<{
  source: {
    title: string;
    url: string;
    content?: string;
    favicon?: string;
    publishedDate?: string;
    author?: string;
  };
  onClick?: () => void;
}> = ({ source, onClick }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const faviconUrl =
    source.favicon ||
    `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(new URL(source.url || 'example.com').hostname)}`;
  let hostname = '';
  try {
    hostname = new URL(source.url).hostname.replace('www.', '');
  } catch {
    hostname = source.url;
  }
  return (
    <div
      className={cn(
        'group relative bg-background',
        'border border-neutral-200 dark:border-neutral-800',
        'rounded-xl p-4 transition-all duration-200',
        'hover:border-neutral-300 dark:hover:border-neutral-700',
        onClick && 'cursor-pointer',
        'w-full max-w-full overflow-hidden',
      )}
      {...(onClick && {
        role: 'button',
        tabIndex: 0,
        onClick,
        onKeyDown: (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClick();
          }
        },
      })}
    >
      <div className="flex items-start gap-3 mb-3">
        <div className="relative w-10 h-10 rounded-lg bg-neutral-100 dark:bg-neutral-800 flex items-center justify-center overflow-hidden shrink-0">
          {!imageLoaded && <div className="absolute inset-0 animate-pulse" />}
          {faviconUrl ? (
            <img
              src={faviconUrl}
              alt=""
              width={24}
              height={24}
              className={cn('object-contain', !imageLoaded && 'opacity-0')}
              onLoad={() => setImageLoaded(true)}
              onError={(e) => {
                setImageLoaded(true);
                e.currentTarget.style.display = 'none';
              }}
            />
          ) : (
            <Globe className="w-5 h-5 text-neutral-400" />
          )}
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-sm text-neutral-900 dark:text-neutral-100 line-clamp-1 mb-1">
            {source.title || hostname}
          </h3>
          <div className="flex items-center gap-1.5 text-xs text-neutral-500 dark:text-neutral-400">
            <span className="truncate">{hostname}</span>
            <ExternalLink className="w-3 h-3 shrink-0 opacity-0 group-hover:opacity-100 transition-opacity" />
          </div>
        </div>
      </div>
      <p className="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-2 leading-relaxed break-words max-w-full overflow-hidden">
        {source.content || 'Loading content...'}
      </p>
    </div>
  );
};

// Sources Sheet Component
const ExtremeSourcesSheet: React.FC<{
  sources: any[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
}> = ({ sources, open, onOpenChange }) => {
  const isMobile = useIsMobile();
  const SheetWrapper = isMobile ? Drawer : Sheet;
  const SheetContentWrapper = isMobile ? DrawerContent : SheetContent;
  return (
    <SheetWrapper open={open} onOpenChange={onOpenChange}>
      <SheetContentWrapper
        className={cn(
          isMobile ? 'h-[85vh]' : 'w-[600px] sm:max-w-[600px]',
          'p-0',
        )}
      >
        <div className="flex flex-col h-full">
          <div className="px-6 py-5 border-b border-neutral-200 dark:border-neutral-800">
            <div>
              <h2 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                All Sources
              </h2>
              <p className="text-sm text-neutral-500 dark:text-neutral-400 mt-0.5">
                {sources.length} research sources
              </p>
            </div>
          </div>
          <div className="flex-1 overflow-y-auto">
            <div className="p-6 space-y-3">
              {sources.map((source) => (
                <a
                  key={source.url}
                  href={source.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block"
                >
                  <ExtremeSourceCard source={source} />
                </a>
              ))}
            </div>
          </div>
        </div>
      </SheetContentWrapper>
    </SheetWrapper>
  );
};

export function ExtremeSearchComponent({
  toolInvocation,
  state,
}: {
  toolInvocation: ToolUIPart;
  state: 'input-streaming' | 'input-available' | 'needs-input';
}) {
  const { annotations } = useExtremeSearchAnnotations();
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(
    {},
  );
  const [userExpandedItems, setUserExpandedItems] = useState<
    Record<string, boolean>
  >({});
  const [researchProcessOpen, setResearchProcessOpen] = useState(false);
  const [sourcesAccordionOpen, setSourcesAccordionOpen] = useState(true);
  const [sourcesSheetOpen, setSourcesSheetOpen] = useState(false);
  const [researchReportOpen, setResearchReportOpen] = useState(true);
  const [visualizationsOpen, setVisualizationsOpen] = useState(true);
  const timelineContainerRef = useRef<HTMLDivElement>(null);
  const prevSearchQueriesLengthRef = useRef<number>(0);

  const isCompleted = useMemo(() => {
    if ('output' in toolInvocation && toolInvocation.output) {
      return true;
    }
    if ('result' in toolInvocation && toolInvocation.result) {
      return true;
    }
    if (annotations?.length) {
      const planAnnotations = annotations.filter(
        (ann) =>
          ann?.type === 'data-extreme_search' &&
          typeof ann.data === 'object' &&
          ann.data !== null &&
          'kind' in ann.data &&
          ann.data.kind === 'plan',
      );
      const latestPlan = planAnnotations[planAnnotations.length - 1];
      const isResearchCompleted =
        latestPlan &&
        typeof latestPlan.data === 'object' &&
        latestPlan.data !== null &&
        'kind' in latestPlan.data &&
        latestPlan.data.kind === 'plan' &&
        typeof latestPlan.data.status === 'object' &&
        (latestPlan.data.status as any)?.title === 'Research completed';
      if (isResearchCompleted) {
        return true;
      }
    }
    return false;
  }, [toolInvocation, annotations]);

  const { currentStatus, planData } = useMemo(() => {
    if (isCompleted) {
      return { currentStatus: 'Research completed', planData: null };
    }
    if (!annotations?.length) {
      return {
        currentStatus:
          state === 'input-streaming' || state === 'input-available'
            ? 'Processing research...'
            : 'Initializing...',
        planData: null,
      };
    }
    const planAnnotations = annotations.filter(
      (ann) =>
        ann.type === 'data-extreme_search' &&
        typeof ann.data === 'object' &&
        ann.data !== null &&
        'kind' in ann.data &&
        ann.data.kind === 'plan',
    );
    const latestPlan = planAnnotations[planAnnotations.length - 1];
    const plan =
      latestPlan &&
      typeof latestPlan.data === 'object' &&
      latestPlan.data !== null &&
      'kind' in latestPlan.data &&
      latestPlan.data.kind === 'plan' &&
      'plan' in latestPlan.data
        ? latestPlan.data.plan
        : null;
    const queryAnnotations = annotations.filter(
      (ann) =>
        ann.type === 'data-extreme_search' &&
        typeof ann.data === 'object' &&
        ann.data !== null &&
        'kind' in ann.data &&
        ann.data.kind === 'query',
    );
    let dynamicStatus = 'Processing research...';
    if (queryAnnotations.length > 0) {
      const latestQuery = queryAnnotations[queryAnnotations.length - 1];
      if (
        latestQuery &&
        typeof latestQuery.data === 'object' &&
        latestQuery.data !== null &&
        'kind' in latestQuery.data &&
        latestQuery.data.kind === 'query'
      ) {
        const queryStatus = latestQuery.data.status;
        const queryText = latestQuery.data.query;
        switch (queryStatus) {
          case 'started':
            dynamicStatus = `Searching: "${queryText}"`;
            break;
          case 'reading_content':
            dynamicStatus = `Reading content for: "${queryText}"`;
            break;
          case 'completed':
            dynamicStatus = 'Analyzing results...';
            break;
          default:
            dynamicStatus = 'Processing research...';
        }
      }
    } else {
      const planStatus =
        latestPlan &&
        typeof latestPlan.data === 'object' &&
        latestPlan.data !== null &&
        'kind' in latestPlan.data &&
        latestPlan.data.kind === 'plan' &&
        typeof latestPlan.data.status === 'object' &&
        (latestPlan.data.status as any)?.title;
      dynamicStatus = planStatus || 'Processing research...';
    }
    return {
      currentStatus: dynamicStatus,
      planData: plan,
    };
  }, [annotations, state, isCompleted]);

  const searchQueries = useMemo(() => {
    if (!annotations?.length) return [];
    const queryMap = new Map<string, SearchQuery>();
    annotations.forEach((ann, index) => {
      if (!ann || ann.type !== 'data-extreme_search' || !ann.data) {
        return;
      }
      const data = ann.data as any;
      if (data.kind === 'query' && data.queryId && data.query && data.status) {
        const existingQuery = queryMap.get(data.queryId);
        if (existingQuery) {
          existingQuery.status = data.status as any;
        } else {
          queryMap.set(data.queryId, {
            id: data.queryId,
            query: data.query,
            status: data.status as any,
            sources: [],
            content: [],
          });
        }
      } else if (data.kind === 'source' && data.source && data.queryId) {
        const query = queryMap.get(data.queryId);
        if (query && !query.sources.find((s) => s.url === data.source?.url)) {
          query.sources.push({
            title: data.source.title || '',
            url: data.source.url,
            content: '',
            favicon:
              data.source.favicon ||
              `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(new URL(data.source.url).hostname)}`,
          });
        }
      } else if (data.kind === 'content' && data.content && data.queryId) {
        const query = queryMap.get(data.queryId);
        if (query && data.content.url) {
          const source = query.sources.find((s) => s.url === data.content?.url);
          if (source && data.content.text) {
            source.content = data.content.text;
          }
        }
      }
    });
    return Array.from(queryMap.values());
  }, [annotations]);

  useEffect(() => {
    if (isCompleted) return;
    const currentLength = searchQueries.length;
    const prevLength = prevSearchQueriesLengthRef.current;
    const totalSources = searchQueries.reduce(
      (total, query) => total + query.sources.length,
      0,
    );
    if (
      (currentLength > prevLength || totalSources > 0) &&
      timelineContainerRef.current
    ) {
      const container = timelineContainerRef.current;
      const scrollTimeout = setTimeout(() => {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth',
        });
      }, 100);
      return () => clearTimeout(scrollTimeout);
    }
    prevSearchQueriesLengthRef.current = currentLength;
  }, [searchQueries, isCompleted]);

  const allSources = useMemo(() => {
    if (isCompleted && 'output' in toolInvocation) {
      const { output } = toolInvocation;
      const researchData = output as { research?: Research } | null;
      const research = researchData?.research;
      if (research?.sources?.length) {
        return research.sources.map((s) => ({
          ...s,
          favicon:
            s.favicon ||
            `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(new URL(s.url).hostname)}`,
        }));
      }
      if (research?.toolResults) {
        return research.toolResults
          .filter((result) => result.toolName === 'webSearch')
          .flatMap((result) =>
            (result.result || result.output || []).map((source: any) => ({
              title: source.title || '',
              url: source.url || '',
              content: source.content || '',
              publishedDate: source.publishedDate || '',
              favicon:
                source.favicon ||
                `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(new URL(source.url || 'example.com').hostname)}`,
            })),
          );
      }
    }
    if (isCompleted && 'result' in toolInvocation && toolInvocation.result) {
      try {
        const resultAny = toolInvocation.result as any;
        const research = resultAny?.research as Research | undefined;
        if (research?.sources?.length) {
          return research.sources.map((s) => ({
            ...s,
            favicon:
              s.favicon ||
              `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(new URL(s.url || 'example.com').hostname)}`,
          }));
        }
        if (research?.toolResults) {
          const webSearchSources = research.toolResults
            .filter((result) => result.toolName === 'webSearch')
            .flatMap((result) =>
              (result.result || result.output || []).map((source: any) => ({
                title: source.title || '',
                url: source.url || '',
                content: source.content || '',
                publishedDate: source.publishedDate || '',
                favicon:
                  source.favicon ||
                  `https://www.google.com/s2/favicons?sz=128&domain=${encodeURIComponent(new URL(source.url || 'example.com').hostname)}`,
              })),
            );
          if (webSearchSources.length) {
            return webSearchSources;
          }
        }
      } catch (e) {}
    }
    const querySources = searchQueries.flatMap((q) => q.sources);
    return Array.from(new Map(querySources.map((s) => [s.url, s])).values());
  }, [isCompleted, toolInvocation, searchQueries]);

  const allCharts = useMemo(() => {
    if (isCompleted) {
      // Check both output and result for charts
      if ('output' in toolInvocation) {
        const { output } = toolInvocation;
        const researchData = output as { research?: Research } | null;
        const research = researchData?.research;
        if (research?.charts?.length) {
          return research.charts;
        }
      }

      if ('result' in toolInvocation && toolInvocation.result) {
        const result = toolInvocation.result as any;
        if (result?.research?.charts?.length) {
          return result.research.charts;
        }
        // Also check if charts are at the top level of result
        if (result?.charts?.length) {
          return result.charts;
        }
      }
    }
    return [];
  }, [isCompleted, toolInvocation]);

  // Create a map of visualization references to their content
  const visualizationMap = useMemo(() => {
    const map = new Map<string, any>();
    allCharts.forEach((chart: any, index: number) => {
      const figureMatch = chart.title?.match(/Figure (\d+)/);
      const chartMatch = chart.title?.match(/Chart (\d+)/);
      const tableMatch = chart.title?.match(/Table (\d+)/);
      const timelineMatch = chart.title?.match(/Timeline (\d+)/);

      if (figureMatch) {
        map.set(`Figure ${figureMatch[1]}`, chart);
        map.set(
          `Figure ${figureMatch[1]}: ${chart.type.replace('_', ' ')}`,
          chart,
        );
      }
      if (chartMatch) {
        map.set(`Chart ${chartMatch[1]}`, chart);
        map.set(
          `Chart ${chartMatch[1]}: ${chart.type.replace('_', ' ')}`,
          chart,
        );
      }
      if (tableMatch) {
        map.set(`Table ${tableMatch[1]}`, chart);
        map.set(
          `Table ${tableMatch[1]}: ${chart.type.replace('_', ' ')}`,
          chart,
        );
      }
      if (timelineMatch) {
        map.set(`Timeline ${timelineMatch[1]}`, chart);
        map.set(
          `Timeline ${timelineMatch[1]}: ${chart.type.replace('_', ' ')}`,
          chart,
        );
      }
    });
    return map;
  }, [allCharts]);

  // Custom component to render visualizations inline
  const VisualizationComponent: React.FC<{ chart: any; reference: string }> = ({
    chart,
    reference,
  }) => {
    return (
      <div className="my-4 p-4 border border-border rounded-lg bg-muted/30">
        <div className="flex items-center gap-2 mb-3">
          <div className="p-1.5 rounded-md bg-primary/10">
            <BarChart3 className="h-3.5 w-3.5 text-primary" />
          </div>
          <h4 className="font-medium text-sm">{reference}</h4>
        </div>
        {chart.description && (
          <p className="text-xs text-muted-foreground mb-3">
            {chart.description}
          </p>
        )}
        {(() => {
          switch (chart.type) {
            case 'comparative_chart':
              return (
                <div className="space-y-2">
                  <div className="text-xs font-medium">Comparative Chart</div>
                  {chart.data?.datasets?.map((dataset: any, i: number) => (
                    <div
                      key={`${dataset.label}-${i}`}
                      className="flex items-center gap-2 text-xs"
                    >
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: dataset.borderColor }}
                      />
                      <span>{dataset.label}</span>
                      <span className="text-muted-foreground">
                        ({dataset.data?.length || 0} data points)
                      </span>
                    </div>
                  ))}
                </div>
              );
            case 'synthesis_table':
              return (
                <div className="space-y-2">
                  <div className="text-xs font-medium">Synthesis Table</div>
                  {chart.data?.headers && (
                    <div className="grid grid-cols-4 gap-2 text-xs font-medium border-b pb-1">
                      {chart.data.headers.map((header: string, i: number) => (
                        <div key={`header-${i}-${header}`}>{header}</div>
                      ))}
                    </div>
                  )}
                  {chart.data?.rows
                    ?.slice(0, 5)
                    .map((row: any[], rowIndex: number) => {
                      const rowKey = `row-${rowIndex}-${row[0]?.toString().substring(0, 20) || ''}`;
                      return (
                        <div
                          key={rowKey}
                          className="grid grid-cols-4 gap-2 text-xs"
                        >
                          {row.map((cell: string, cellIndex: number) => {
                            const cellKey = `cell-${rowIndex}-${cellIndex}-${cell?.toString().substring(0, 10) || ''}`;
                            return (
                              <div key={cellKey} className="truncate">
                                {cell}
                              </div>
                            );
                          })}
                        </div>
                      );
                    })}
                  {chart.data?.rows?.length > 5 && (
                    <div className="text-xs text-muted-foreground">
                      ... and {chart.data.rows.length - 5} more rows
                    </div>
                  )}
                </div>
              );
            case 'flow_diagram':
              return (
                <div className="space-y-2">
                  <div className="text-xs font-medium">
                    Process Flow Diagram
                  </div>
                  <div className="space-y-1">
                    {chart.data?.nodes?.map((node: any, i: number) => (
                      <div
                        key={`${node.id}-${i}`}
                        className="flex items-center gap-2 text-xs"
                      >
                        <div
                          className={`w-2 h-2 rounded-full ${
                            node.type === 'input'
                              ? 'bg-green-500'
                              : node.type === 'process'
                                ? 'bg-blue-500'
                                : 'bg-red-500'
                          }`}
                        />
                        <span>{node.label}</span>
                      </div>
                    ))}
                    {chart.data?.edges && (
                      <div className="text-xs text-muted-foreground mt-2">
                        {chart.data.edges.length} connections
                      </div>
                    )}
                  </div>
                </div>
              );
            case 'timeline':
              return (
                <div className="space-y-2">
                  <div className="text-xs font-medium">Timeline</div>
                  <div className="space-y-1">
                    {chart.data?.events
                      ?.slice(0, 5)
                      .map((event: any, i: number) => (
                        <div
                          key={`${event.id}-${i}`}
                          className="flex items-center gap-2 text-xs"
                        >
                          <div className="w-1 h-1 rounded-full bg-primary" />
                          <span>{event.title}</span>
                          <span className="text-muted-foreground">
                            {event.date}
                          </span>
                        </div>
                      ))}
                    {chart.data?.events?.length > 5 && (
                      <div className="text-xs text-muted-foreground">
                        ... and {chart.data.events.length - 5} more events
                      </div>
                    )}
                  </div>
                </div>
              );
            case 'extracted_graphics':
              return (
                <div className="space-y-3">
                  <div className="text-xs font-medium">Extracted Graphics</div>
                  <div className="space-y-2">
                    {chart.data?.graphics
                      ?.slice(0, 3)
                      .map((graphic: any, i: number) => (
                        <div
                          key={`${graphic.url}-${i}`}
                          className="border rounded-lg p-3"
                        >
                          <div className="flex items-start gap-3">
                            <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
                              <img
                                src={graphic.url}
                                alt={graphic.alt || 'Extracted graphic'}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  e.currentTarget.src =
                                    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAyMEMxNS4xNjM0IDIwIDggMjcuMTYzNCA4IDM2VjM4SDQ4VjM2QzQ4IDI3LjE2MzQgNDAuODM2NiAyMCAzMiAyMCIgc3Ryb2tlPSIjOUI5QjlCIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K';
                                }}
                              />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-xs font-medium truncate">
                                {graphic.title || `Graphic ${i + 1}`}
                              </div>
                              <div className="text-xs text-muted-foreground truncate mt-1">
                                {graphic.description}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    {chart.data?.graphics?.length > 3 && (
                      <div className="text-xs text-muted-foreground">
                        ... and {chart.data.graphics.length - 3} more graphics
                      </div>
                    )}
                  </div>
                </div>
              );
            default:
              return (
                <div className="text-xs text-muted-foreground">
                  Visualization: {chart.type}
                </div>
              );
          }
        })()}
      </div>
    );
  };

  useEffect(() => {
    if (isCompleted) return;
    setExpandedItems((prev) => {
      const newExpanded = { ...prev };
      let shouldUpdate = false;
      searchQueries.forEach((query) => {
        const isActive =
          query.status === 'started' || query.status === 'reading_content';
        const wasUserControlled = userExpandedItems[query.id];
        if (isActive && !prev[query.id] && !wasUserControlled) {
          newExpanded[query.id] = true;
          shouldUpdate = true;
        }
        if (
          query.status === 'completed' &&
          prev[query.id] &&
          !wasUserControlled
        ) {
          newExpanded[query.id] = false;
          shouldUpdate = true;
        }
      });
      return shouldUpdate ? newExpanded : prev;
    });
  }, [searchQueries, userExpandedItems, isCompleted]);

  const toggleItemExpansion = (itemId: string) => {
    setExpandedItems((prev) => ({ ...prev, [itemId]: !prev[itemId] }));
    setUserExpandedItems((prev) => ({ ...prev, [itemId]: true }));
  };

  const renderTimeline = () => (
    <div className="space-y-1 relative ml-4 mb-2">
      <AnimatePresence>
        {searchQueries.map((query, itemIndex) => {
          const isLoading =
            query.status === 'started' || query.status === 'reading_content';
          const hasResults = query.sources.length > 0;
          const isReadingContent = query.status === 'reading_content';
          const isCurrentQuery =
            itemIndex === searchQueries.length - 1 && isLoading;
          const bulletColor = isCurrentQuery
            ? 'bg-green-500 animate-[pulse_0.8s_ease-in-out_infinite]'
            : hasResults
              ? 'bg-gray-500'
              : 'bg-yellow-500';
          return (
            <motion.div
              key={query.id}
              className="space-y-0 relative"
              initial={{ opacity: 0, y: 2 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.1, delay: itemIndex * 0.01 }}
            >
              <div
                className="absolute rounded-full bg-background z-5"
                style={{
                  left: '-0.6rem',
                  top: '4px',
                  width: '10px',
                  height: '10px',
                  transform: 'translateX(-50%)',
                }}
              />
              <div
                className={`absolute rounded-full ${bulletColor} transition-colors duration-300 z-10`}
                style={{
                  left: '-0.6rem',
                  top: '6px',
                  width: '8px',
                  height: '8px',
                  transform: 'translateX(-50%)',
                }}
                title={`Status: ${query.status}`}
              />
              {itemIndex > 0 && (
                <div
                  className="absolute bg-neutral-300 dark:bg-neutral-700"
                  style={{
                    left: '-0.6rem',
                    top: '-6px',
                    width: '2px',
                    height: '14px',
                    transform: 'translateX(-50%)',
                  }}
                />
              )}
              <div
                className="absolute bg-neutral-300 dark:bg-neutral-700"
                style={{
                  left: '-0.6rem',
                  top: '7px',
                  width: '2px',
                  height: expandedItems[query.id]
                    ? itemIndex === searchQueries.length - 1
                      ? 'calc(100% - 9px)'
                      : '100%'
                    : itemIndex === searchQueries.length - 1
                      ? '9px'
                      : '16px',
                  transform: 'translateX(-50%)',
                }}
              />
              <div
                className="flex items-center gap-1 cursor-pointer py-0.5 px-1 hover:bg-muted rounded-sm relative min-h-[18px]"
                role="button"
                tabIndex={0}
                onClick={() => toggleItemExpansion(query.id)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    toggleItemExpansion(query.id);
                  }
                }}
              >
                <Search className="w-2.5 h-2.5 text-muted-foreground flex-shrink-0" />
                <span className="text-foreground text-xs min-w-0 flex-1">
                  {isLoading && !isCompleted ? (
                    <TextShimmer className="w-full" duration={1.5}>
                      {query.query}
                    </TextShimmer>
                  ) : (
                    query.query
                  )}
                </span>
                {expandedItems[query.id] ? (
                  <ChevronDown className="w-2.5 h-2.5 text-muted-foreground flex-shrink-0 ml-auto" />
                ) : (
                  <ChevronRight className="w-2.5 h-2.5 text-muted-foreground flex-shrink-0 ml-auto" />
                )}
              </div>
              <AnimatePresence>
                {expandedItems[query.id] && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{
                      height: { duration: 0.2, ease: 'easeOut' },
                      opacity: { duration: 0.15 },
                    }}
                    className="overflow-hidden"
                  >
                    <div className="pl-0.5 py-0.5">
                      {query.sources.length > 0 && (
                        <motion.div
                          className="flex flex-wrap gap-1 py-0.5"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.15 }}
                        >
                          {query.sources.map((source, index) => (
                            <motion.a
                              key={`${source.url}-${index}`}
                              href={source.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-1 bg-muted px-1.5 py-0.5 rounded-full text-xs hover:bg-muted/80 transition-colors"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{
                                duration: 0.15,
                                delay: index * 0.02,
                              }}
                            >
                              <img
                                src={source.favicon}
                                alt=""
                                className="w-3 h-3 rounded-full"
                                onError={(e) => {
                                  e.currentTarget.src =
                                    'https://www.google.com/s2/favicons?sz=128&domain=example.com';
                                }}
                              />
                              <span
                                className="text-muted-foreground truncate max-w-[100px]"
                                title={source.title}
                              >
                                {source.title || 'source'}
                              </span>
                            </motion.a>
                          ))}
                        </motion.div>
                      )}
                      {(() => {
                        const isLatestQuery =
                          itemIndex === searchQueries.length - 1;
                        if (
                          isReadingContent &&
                          query.sources.length > 0 &&
                          !isCompleted &&
                          isLatestQuery
                        ) {
                          return (
                            <TextShimmer
                              className="text-xs py-0.5"
                              duration={2.5}
                            >
                              Reading content...
                            </TextShimmer>
                          );
                        } else if (isLoading && !isCompleted && isLatestQuery) {
                          return (
                            <TextShimmer
                              className="text-xs py-0.5"
                              duration={2.5}
                            >
                              Searching sources...
                            </TextShimmer>
                          );
                        } else if (query.sources.length === 0 && !isLoading) {
                          return (
                            <p className="text-xs text-muted-foreground py-1 mt-1">
                              No sources found for this query.
                            </p>
                          );
                        }
                        return null;
                      })()}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          );
        })}
      </AnimatePresence>
    </div>
  );

  const handleWheelScroll = (e: React.WheelEvent<HTMLDivElement>) => {
    const container = e.currentTarget;
    if (e.deltaY === 0) return;
    const canScrollHorizontally = container.scrollWidth > container.clientWidth;
    if (!canScrollHorizontally) return;
    e.stopPropagation();
    const isAtLeftEdge = container.scrollLeft <= 1;
    const isAtRightEdge =
      container.scrollLeft >= container.scrollWidth - container.clientWidth - 1;
    if (!isAtLeftEdge && !isAtRightEdge) {
      e.preventDefault();
      container.scrollLeft += e.deltaY;
    } else if (isAtLeftEdge && e.deltaY > 0) {
      e.preventDefault();
      container.scrollLeft += e.deltaY;
    } else if (isAtRightEdge && e.deltaY < 0) {
      e.preventDefault();
      container.scrollLeft += e.deltaY;
    }
  };

  const renderSources = (sources: any[]) => (
    <div
      className="w-full max-w-full overflow-hidden"
      style={{ maxWidth: '100% !important' }}
    >
      <div
        className={cn(
          'py-3 px-4 hover:no-underline group',
          'bg-background',
          'border border-neutral-200 dark:border-neutral-800',
          'cursor-pointer',
          sourcesAccordionOpen ? 'rounded-t-lg' : 'rounded-lg',
        )}
        role="button"
        tabIndex={0}
        onClick={() => setSourcesAccordionOpen(!sourcesAccordionOpen)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            setSourcesAccordionOpen(!sourcesAccordionOpen);
          }
        }}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-md bg-neutral-100 dark:bg-neutral-800">
              <Globe className="h-3.5 w-3.5 text-neutral-500" />
            </div>
            <h2 className="font-medium text-sm">Sources</h2>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              variant="secondary"
              className="rounded-full text-xs px-2.5 py-0.5"
            >
              {sources.length}
            </Badge>
            {sources.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-7 px-2 text-xs"
                onClick={(e) => {
                  e.stopPropagation();
                  setSourcesSheetOpen(true);
                }}
              >
                View all
                <ArrowUpRight className="w-3 h-3 ml-1" />
              </Button>
            )}
            <ChevronDown
              className={cn(
                'h-4 w-4 text-neutral-500 shrink-0 transition-transform duration-200',
                sourcesAccordionOpen ? 'rotate-180' : '',
              )}
            />
          </div>
        </div>
      </div>
      <AnimatePresence>
        {sourcesAccordionOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className={cn(
              'overflow-hidden',
              'bg-background',
              'border-x border-b border-neutral-200 dark:border-neutral-800',
              'rounded-b-lg',
            )}
            style={{
              width: '100%',
              maxWidth: '100% !important',
              boxSizing: 'border-box',
            }}
          >
            <div className="px-3 pt-3 pb-0">
              {sources.length > 0 ? (
                <div className="relative h-[160px]">
                  <div
                    className="absolute inset-0 overflow-x-auto overflow-y-hidden"
                    style={{
                      width: '100%',
                      height: '100%',
                    }}
                  >
                    <div className="flex gap-3 h-full items-start">
                      {sources.map((source) => (
                        <div
                          key={source.url}
                          className="flex-shrink-0"
                          style={{ width: '280px' }}
                        >
                          <a
                            href={source.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="block"
                          >
                            <ExtremeSourceCard source={source} />
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-neutral-500 text-sm">No sources found</p>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );

  if (isCompleted) {
    return (
      <div className="space-y-2 w-full max-w-full overflow-hidden">
        {/* Research Process */}
        <Card className="!p-0 !gap-0 rounded-lg shadow-none">
          <div
            className="flex items-center justify-between p-3 cursor-pointer"
            role="button"
            tabIndex={0}
            onClick={() => setResearchProcessOpen(!researchProcessOpen)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                setResearchProcessOpen(!researchProcessOpen);
              }
            }}
          >
            <h3 className="font-medium">Research Process</h3>
            {researchProcessOpen ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </div>
          <AnimatePresence>
            {researchProcessOpen && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
              >
                <CardContent className="mx-3 mb-0 !p-0">
                  <div className="max-h-[300px] overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-neutral-200 hover:scrollbar-thumb-neutral-300 dark:scrollbar-thumb-neutral-700 dark:hover:scrollbar-thumb-neutral-600 scrollbar-track-transparent">
                    {renderTimeline()}
                  </div>
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>

        {/* Sources - Moved here to be second */}
        {renderSources(allSources)}
        <ExtremeSourcesSheet
          sources={allSources}
          open={sourcesSheetOpen}
          onOpenChange={setSourcesSheetOpen}
        />

        {/* Direct Research Report Display in Chat - Now third */}
        {(() => {
          // Try to get research from output first, then from result
          let research: Research | null = null;

          if ('output' in toolInvocation) {
            const output = toolInvocation.output as {
              research?: Research;
            } | null;
            research = output?.research || null;
          }

          // If not found in output, try result
          if (
            !research &&
            'result' in toolInvocation &&
            toolInvocation.result
          ) {
            const result = toolInvocation.result as any;
            research = result?.research || null;
          }

          // Display research if found
          if (research?.text) {
            return (
              <div className="w-full max-w-full overflow-hidden">
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm, remarkMath]}
                    rehypePlugins={[rehypeKatex]}
                    components={{
                      h1: ({ children }) => (
                        <h1 className="text-xl font-bold mb-4">{children}</h1>
                      ),
                      h2: ({ children }) => (
                        <h2 className="text-lg font-semibold mb-3 mt-6">
                          {children}
                        </h2>
                      ),
                      h3: ({ children }) => (
                        <h3 className="text-base font-medium mb-2 mt-4">
                          {children}
                        </h3>
                      ),
                      p: ({ children }) => {
                        const transformedChildren = React.Children.map(
                          children,
                          (child) => {
                            if (typeof child === 'string') {
                              return transformCitations(child, allSources);
                            }
                            return child;
                          },
                        );
                        return (
                          <p className="mb-4 leading-relaxed">
                            {transformedChildren}
                          </p>
                        );
                      },
                      ul: ({ children }) => (
                        <ul className="list-disc ml-6 mb-4">{children}</ul>
                      ),
                      ol: ({ children }) => (
                        <ol className="list-decimal ml-6 mb-4">{children}</ol>
                      ),
                      li: ({ children }) => {
                        const transformedChildren = React.Children.map(
                          children,
                          (child) => {
                            if (typeof child === 'string') {
                              return transformCitations(child, allSources);
                            }
                            return child;
                          },
                        );
                        return <li className="mb-1">{transformedChildren}</li>;
                      },
                      blockquote: ({ children }) => (
                        <blockquote className="border-l-4 border-gray-300 pl-4 italic mb-4">
                          {children}
                        </blockquote>
                      ),
                      table: ({ children }) => (
                        <div className="overflow-x-auto mb-4">
                          <table className="w-full border-collapse border border-gray-300 text-sm">
                            {children}
                          </table>
                        </div>
                      ),
                      thead: ({ children }) => (
                        <thead className="bg-gray-50 dark:bg-gray-800">
                          {children}
                        </thead>
                      ),
                      tbody: ({ children }) => <tbody>{children}</tbody>,
                      tr: ({ children }) => (
                        <tr className="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800">
                          {children}
                        </tr>
                      ),
                      th: ({ children }) => {
                        const transformedChildren = React.Children.map(
                          children,
                          (child) => {
                            if (typeof child === 'string') {
                              return transformCitations(child, allSources);
                            }
                            return child;
                          },
                        );
                        return (
                          <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left font-semibold bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                            {transformedChildren}
                          </th>
                        );
                      },
                      td: ({ children }) => {
                        const transformedChildren = React.Children.map(
                          children,
                          (child) => {
                            if (typeof child === 'string') {
                              return transformCitations(child, allSources);
                            }
                            return child;
                          },
                        );
                        return (
                          <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-gray-900 dark:text-gray-100">
                            {transformedChildren}
                          </td>
                        );
                      },
                      code: ({ children, className }) => {
                        const match = /language-(\w+)/.exec(className || '');
                        return match ? (
                          <div className="w-full overflow-hidden">
                            <code
                              className={`block bg-gray-100 p-2 rounded mb-4 overflow-x-auto max-w-full ${className}`}
                            >
                              {children}
                            </code>
                          </div>
                        ) : (
                          <code className="bg-gray-100 px-1 rounded">
                            {children}
                          </code>
                        );
                      },
                      // Custom component to handle visualization references
                      text: (props: any) => {
                        // Handle case where this is used as an SVG text component
                        if (props.children !== undefined) {
                          return <text {...props} />;
                        }

                        const value = props.value || '';
                        if (!value || !visualizationMap.size) return value;

                        // Split text by visualization references
                        const parts = value.split(
                          /(\[Figure \d+(?::[^\]]+)?\]|\[Chart \d+(?::[^\]]+)?\]|\[Table \d+(?::[^\]]+)?\]|\[Timeline \d+(?::[^\]]+)?\])/g,
                        );

                        return (
                          <>
                            {parts.map((part: string, index: number) => {
                              // Check if this part is a visualization reference
                              const vizMatch = part.match(
                                /^\[(Figure|Chart|Table|Timeline) (\d+)(?::([^\]]+))?\]$/,
                              );
                              if (vizMatch) {
                                const [, type, number, description] = vizMatch;
                                const referenceKey = `${type} ${number}`;
                                const chart =
                                  visualizationMap.get(referenceKey);

                                if (chart) {
                                  return (
                                    <VisualizationComponent
                                      key={`viz-${type}-${number}-${chart.id || index}`}
                                      chart={chart}
                                      reference={part}
                                    />
                                  );
                                }
                              }

                              // Return regular text
                              return part;
                            })}
                          </>
                        );
                      },
                    }}
                  >
                    {research.text}
                  </ReactMarkdown>
                </div>
              </div>
            );
          }

          // Fallback: if result is a plain string, display it
          if ('result' in toolInvocation && toolInvocation.result) {
            const result = toolInvocation.result as any;
            if (typeof result === 'string' && result.length > 50) {
              return (
                <div className="w-full max-w-full overflow-hidden">
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm, remarkMath]}
                      rehypePlugins={[rehypeKatex]}
                      components={{
                        h1: ({ children }) => (
                          <h1 className="text-xl font-bold mb-4">{children}</h1>
                        ),
                        h2: ({ children }) => (
                          <h2 className="text-lg font-semibold mb-3 mt-6">
                            {children}
                          </h2>
                        ),
                        h3: ({ children }) => (
                          <h3 className="text-base font-medium mb-2 mt-4">
                            {children}
                          </h3>
                        ),
                        p: ({ children }) => {
                          const transformedChildren = React.Children.map(
                            children,
                            (child) => {
                              if (typeof child === 'string') {
                                return transformCitations(child, allSources);
                              }
                              return child;
                            },
                          );
                          return (
                            <p className="mb-4 leading-relaxed">
                              {transformedChildren}
                            </p>
                          );
                        },
                        ul: ({ children }) => (
                          <ul className="list-disc ml-6 mb-4">{children}</ul>
                        ),
                        ol: ({ children }) => (
                          <ol className="list-decimal ml-6 mb-4">{children}</ol>
                        ),
                        li: ({ children }) => {
                          const transformedChildren = React.Children.map(
                            children,
                            (child) => {
                              if (typeof child === 'string') {
                                return transformCitations(child, allSources);
                              }
                              return child;
                            },
                          );
                          return (
                            <li className="mb-1">{transformedChildren}</li>
                          );
                        },
                        blockquote: ({ children }) => (
                          <blockquote className="border-l-4 border-gray-300 pl-4 italic mb-4">
                            {children}
                          </blockquote>
                        ),
                        table: ({ children }) => (
                          <div className="overflow-x-auto mb-4">
                            <table className="w-full border-collapse border border-gray-300 text-sm">
                              {children}
                            </table>
                          </div>
                        ),
                        thead: ({ children }) => (
                          <thead className="bg-gray-50 dark:bg-gray-800">
                            {children}
                          </thead>
                        ),
                        tbody: ({ children }) => <tbody>{children}</tbody>,
                        tr: ({ children }) => (
                          <tr className="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800">
                            {children}
                          </tr>
                        ),
                        th: ({ children }) => {
                          const transformedChildren = React.Children.map(
                            children,
                            (child) => {
                              if (typeof child === 'string') {
                                return transformCitations(child, allSources);
                              }
                              return child;
                            },
                          );
                          return (
                            <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left font-semibold bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                              {transformedChildren}
                            </th>
                          );
                        },
                        td: ({ children }) => {
                          const transformedChildren = React.Children.map(
                            children,
                            (child) => {
                              if (typeof child === 'string') {
                                return transformCitations(child, allSources);
                              }
                              return child;
                            },
                          );
                          return (
                            <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-gray-900 dark:text-gray-100">
                              {transformedChildren}
                            </td>
                          );
                        },
                        code: ({ children, className }) => {
                          const match = /language-(\w+)/.exec(className || '');
                          return match ? (
                            <div className="w-full overflow-hidden">
                              <code
                                className={`block bg-gray-100 p-2 rounded mb-4 overflow-x-auto max-w-full ${className}`}
                              >
                                {children}
                              </code>
                            </div>
                          ) : (
                            <code className="bg-gray-100 px-1 rounded">
                              {children}
                            </code>
                          );
                        },
                      }}
                    >
                      {result}
                    </ReactMarkdown>
                  </div>
                </div>
              );
            }
          }
          return null;
        })()}
      </div>
    );
  }

  // In-progress view
  return (
    <Card className="!p-0 !m-0 !gap-0 rounded-lg shadow-none">
      <div className="py-3 px-4 border-b bg-neutral-50 dark:bg-neutral-900 rounded-t-lg">
        <div className="text-sm font-medium">
          {state === 'input-streaming' || state === 'input-available' ? (
            <TextShimmer duration={2}>{currentStatus}</TextShimmer>
          ) : (
            currentStatus
          )}
        </div>
      </div>
      <CardContent className="p-4">
        {/* Show plan if available and no timeline items yet */}
        {planData && searchQueries.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mb-3"
          >
            <div className="flex items-center gap-1.5 mb-2">
              <Target className="w-3.5 h-3.5 text-primary" />
              <h4 className="text-sm font-medium text-foreground">
                Research Strategy
              </h4>
            </div>
            <div className="space-y-1 relative ml-3">
              {planData.map((item: any, index: number) => (
                <motion.div
                  key={item.title || index}
                  initial={{ opacity: 0, y: 2 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="space-y-0 relative"
                >
                  {/* Background circle to prevent line showing through */}
                  <div
                    className="absolute w-1.5 h-1.5 rounded-full bg-background z-5"
                    style={{
                      left: '-0.6rem',
                      top: '5px',
                      transform: 'translateX(-50%)',
                    }}
                  />
                  {/* Timeline bullet */}
                  <div
                    className="absolute size-1 rounded-full bg-primary transition-colors duration-300 z-10"
                    style={{
                      left: '-0.6rem',
                      top: '5.5px',
                      transform: 'translateX(-50%)',
                    }}
                  />
                  {/* Vertical line above bullet */}
                  {index > 0 && (
                    <div
                      className="absolute w-0.25 bg-border"
                      style={{
                        left: '-0.6rem',
                        top: '-6px',
                        height: '12px',
                        transform: 'translateX(-50%)',
                      }}
                    />
                  )}
                  {/* Vertical line below bullet */}
                  {index < planData.length - 1 && (
                    <div
                      className="absolute w-0.25 bg-border"
                      style={{
                        left: '-0.6rem',
                        top: '6px',
                        height: '14px',
                        transform: 'translateX(-50%)',
                      }}
                    />
                  )}
                  <div className="flex items-center gap-1 py-0.5 px-1 rounded-sm relative min-h-[18px]">
                    <span className="text-foreground text-xs min-w-0 flex-1 font-medium">
                      {item.title}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {item.todos?.length || 0} tasks
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
        {/* Show loading skeletons when no plan and no items */}
        {!planData && searchQueries.length === 0 && (
          <div className="mb-3">
            <div className="flex items-center gap-1.5 mb-2">
              <Target className="w-3.5 h-3.5 text-primary/50" />
              <h4 className="text-sm font-medium text-foreground">
                Preparing Research Strategy
              </h4>
            </div>
            <div className="space-y-1 relative ml-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="space-y-0 relative">
                  {/* Background circle skeleton */}
                  <div
                    className="absolute w-1.5 h-1.5 rounded-full bg-background z-5"
                    style={{
                      left: '-0.6rem',
                      top: '5px',
                      transform: 'translateX(-50%)',
                    }}
                  />
                  {/* Timeline bullet skeleton */}
                  <Skeleton
                    className="absolute size-1 rounded-full z-10"
                    style={{
                      left: '-0.6rem',
                      top: '5.5px',
                      transform: 'translateX(-50%)',
                    }}
                  />
                  {/* Vertical line above bullet */}
                  {i > 1 && (
                    <div
                      className="absolute w-0.25 bg-border"
                      style={{
                        left: '-0.6rem',
                        top: '-6px',
                        height: '12px',
                        transform: 'translateX(-50%)',
                      }}
                    />
                  )}
                  {/* Vertical line below bullet */}
                  {i < 3 && (
                    <div
                      className="absolute w-0.25 bg-border"
                      style={{
                        left: '-0.6rem',
                        top: '6px',
                        height: '14px',
                        transform: 'translateX(-50%)',
                      }}
                    />
                  )}
                  <div className="flex items-center gap-1 py-0.5 px-1 rounded-sm relative min-h-[18px]">
                    <Skeleton className="w-2.5 h-2.5 rounded-full flex-shrink-0" />
                    <Skeleton className="h-3 flex-1" />
                    <Skeleton className="h-3 w-12" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        {/* Show timeline when items are available avec ref pour auto-scroll */}
        {searchQueries.length > 0 && (
          <div
            ref={timelineContainerRef}
            className="max-h-[300px] overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-neutral-200 hover:scrollbar-thumb-neutral-300 dark:scrollbar-thumb-neutral-700 dark:hover:scrollbar-thumb-neutral-600 scrollbar-track-transparent"
          >
            {renderTimeline()}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export const ExtremeSearch = memo(ExtremeSearchComponent);
