/**
 * API Route pour récupérer les statistiques de la recherche extrême
 * GET /api/extreme-search/stats?userId=xxx
 */

import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { extremeSearchUsage } from '@/lib/db/schema';
import { eq, and, gte, lt } from 'drizzle-orm';

// Initialize database connection
// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'userId est requis' }, { status: 400 });
    }

    const now = new Date();

    // Début du mois actuel
    const startOfCurrentMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Début du mois dernier
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Utilisation du mois actuel
    const currentMonthUsage = await db
      .select()
      .from(extremeSearchUsage)
      .where(
        and(
          eq(extremeSearchUsage.userId, userId),
          gte(extremeSearchUsage.date, startOfCurrentMonth),
        ),
      )
      .limit(1);

    const currentMonth =
      currentMonthUsage.length > 0
        ? Number.parseInt(String(currentMonthUsage[0].searchCount))
        : 0;

    // Utilisation du mois dernier
    const lastMonthUsage = await db
      .select()
      .from(extremeSearchUsage)
      .where(
        and(
          eq(extremeSearchUsage.userId, userId),
          gte(extremeSearchUsage.date, startOfLastMonth),
          lt(extremeSearchUsage.date, endOfLastMonth),
        ),
      )
      .limit(1);

    const lastMonth =
      lastMonthUsage.length > 0
        ? Number.parseInt(String(lastMonthUsage[0].searchCount))
        : 0;

    // Total de toutes les utilisations
    const allUsage = await db
      .select()
      .from(extremeSearchUsage)
      .where(eq(extremeSearchUsage.userId, userId));

    const total = allUsage.reduce(
      (acc: number, curr: any) =>
        acc + Number.parseInt(String(curr.searchCount)),
      0,
    );

    // Limite par défaut (peut être récupérée depuis les paramètres utilisateur)
    const limit = 10;
    const remainingThisMonth = Math.max(0, limit - currentMonth);

    return NextResponse.json({
      currentMonth,
      lastMonth,
      total,
      remainingThisMonth,
      limit,
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des stats:', error);
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
  }
}
