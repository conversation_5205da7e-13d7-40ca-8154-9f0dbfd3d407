import type { MessageAnnotation } from '../../types/ai-extensions';
import { YouTubeSearchAnnotation } from './youtube-search-annotation';
import MapAnnotation from './map-annotation';
import { useState, useEffect } from 'react';

interface Video {
  link: string;
  title?: string;
  thumbnail?: string;
}

interface MessageAnnotationsProps {
  annotations: MessageAnnotation[];
}

export function MessageAnnotations({ annotations }: MessageAnnotationsProps) {
  const [youtubeVideos, setYoutubeVideos] = useState<Video[]>([]);

  // Process annotations to extract YouTube videos
  useEffect(() => {
    if (!annotations || annotations.length === 0) return;

    // Find YouTube search completion annotations
    const youtubeAnnotations = annotations.filter(
      (annotation) => annotation.type === 'youtube_search_completion',
    );

    if (youtubeAnnotations.length === 0) return;

    // Find the tool result that contains the videos
    const toolResults = annotations.filter(
      (annotation) =>
        annotation.type === 'tool_result' &&
        annotation.name === 'youtube_search',
    );

    if (toolResults.length === 0) return;

    // Extract videos from the tool results
    try {
      const videos: Video[] = [];

      toolResults.forEach((result) => {
        if (result.content) {
          const parsedContent =
            typeof result.content === 'string'
              ? JSON.parse(result.content)
              : result.content;

          if (parsedContent.videos && Array.isArray(parsedContent.videos)) {
            videos.push(...parsedContent.videos);
          }
        }
      });

      setYoutubeVideos(videos);
    } catch (error) {
      console.error('Error parsing YouTube search results:', error);
    }
  }, [annotations]);

  if (!annotations || annotations.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-col gap-2">
      {annotations.map((annotation, index) => {
        if (annotation.type === 'youtube_search_completion') {
          return (
            <YouTubeSearchAnnotation
              key={`youtube-search-${annotation.id ?? `${annotation.data?.query}-${index}`}`}
              annotation={
                annotation as MessageAnnotation<{
                  query: string;
                  status: string;
                  resultsCount: number;
                }>
              }
              videos={youtubeVideos}
            />
          );
        }

        if (annotation.type === 'map_display') {
          return (
            <MapAnnotation
              key={`map-display-${annotation.id || index}`}
              annotation={annotation}
            />
          );
        }

        return null;
      })}

      {/* Les vidéos sont maintenant affichées directement dans le résultat de l'outil */}
    </div>
  );
}

// Les vidéos sont maintenant affichées directement dans le résultat de l'outil
