import { z } from 'zod';
import {
  generateObject,
  type LanguageModel,
} from 'ai';

/**
 * Enum for trip types
 */
export enum TripType {
  GENERAL = 'general',
  ADVENTURE = 'adventure',
  BEACH = 'beach',
  CITY = 'city',
  CULTURAL = 'cultural',
  CYCLING = 'cycling',
  FAMILY = 'family',
  FOOD = 'food',
  HIKING = 'hiking',
  LUXURY = 'luxury',
  NATURE = 'nature',
  RELAXATION = 'relaxation',
  ROAD_TRIP = 'road_trip',
  ROMANTIC = 'romantic',
  SKI = 'ski',
  WELLNESS = 'wellness',
  WILDLIFE = 'wildlife',
  WINTER = 'winter',
  WORK = 'work',
}

/**
 * Enum for visual themes
 */
export enum VisualTheme {
  CLASSIC = 'classic',
  MODERN = 'modern',
  MINIMALIST = 'minimalist',
  VIBRANT = 'vibrant',
  ELEGANT = 'elegant',
  RUSTIC = 'rustic',
  ADVENTURE = 'adventure',
  LUXURY = 'luxury',
  ECO = 'eco',
  ROMANTIC = 'romantic',
  FAMILY = 'family',
  BUSINESS = 'business',
}

/**
 * Interface for trip classification
 */
export interface TripClassification {
  primaryType: TripType;
  secondaryTypes: TripType[];
  visualTheme: VisualTheme;
  primaryActivity: string;
  specialRequirements: string[];
  keywords: string[];
  customizations: {
    mapFocus: boolean;
    foodFocus: boolean;
    accommodationFocus: boolean;
    budgetFocus: boolean;
    accessibilityFocus: boolean;
    familyFocus: boolean;
    sustainabilityFocus: boolean;
  };
}

/**
 * ClassifierAgent is responsible for classifying the trip request to determine
 * the appropriate visual presentation and content focus.
 */
export class ClassifierAgent {
  private model: LanguageModel;

  constructor(model: LanguageModel) {
    this.model = model;
  }

  /**
   * Classify the trip request
   */
  async classifyTripRequest(query: string): Promise<TripClassification> {
    try {
      console.log('Classifying trip request:', query);

      // Classify the trip request using the model
      const { object: classification } = await generateObject({
        model: this.model,
        system: `You are an expert travel request classifier who specializes in understanding the type of trip a user is looking for.
        Analyze the user's query to classify the type of trip and determine the appropriate visual presentation and content focus.
        Pay special attention to specific activities, interests, or requirements mentioned in the query.

        IMPORTANT: You MUST detect and classify specific activities mentioned in the query, even if they are not the main focus of the trip.

        ACTIVITY DETECTION RULES:
        1. If the user mentions "vélo", "cycling", "bike", "bicycle", "faire du vélo", or any cycling-related terms, classify this as a CYCLING trip type.
        2. If the user mentions "shopping", "boutiques", "faire les boutiques", "magasins", or any shopping-related terms, include CITY as a secondary type and set "shopping" as a primary activity.
        3. If the user mentions "hiking", "randonnée", "marche", "trekking", or any hiking-related terms, classify this as a HIKING trip type.
        4. If the user mentions "plage", "beach", "mer", "ocean", "swimming", or any beach-related terms, classify this as a BEACH trip type.
        5. If the user mentions "musée", "museum", "culture", "histoire", "history", "art", or any cultural terms, classify this as a CULTURAL trip type.
        6. If the user mentions "food", "cuisine", "gastronomie", "restaurant", "manger", or any food-related terms, classify this as a FOOD trip type.

        MULTI-ACTIVITY HANDLING:
        - If multiple activities are mentioned (e.g., "faire du vélo et faire les boutiques"), identify ALL of them.
        - Set the most emphasized activity as the primaryType and include others as secondaryTypes.
        - If no clear emphasis, prioritize in this order: CYCLING, HIKING, BEACH, FOOD, CULTURAL, CITY.
        - The primaryActivity should reflect the most specific activity mentioned (e.g., "cycling and shopping in Paris" should have "cycling" as primaryType and "shopping" as primaryActivity).

        LANGUAGE SUPPORT:
        - You can understand queries in multiple languages, including English and French.
        - For French queries like "Je voudrais faire du vélo à Paris", correctly identify "cycling" as the primary activity.
        - For French queries like "faire les boutiques à Paris", correctly identify "shopping" as the primary activity.

        VISUAL THEME SELECTION:
        - Adventure trips should have a vibrant, action-oriented theme
        - Luxury trips should have an elegant, sophisticated theme
        - Family trips should have a friendly, colorful theme
        - Cycling trips should have a sporty, outdoor theme
        - Shopping/city trips should have a modern, stylish theme
        - Cultural trips should have a classic, refined theme
        - Food trips should have a warm, rich theme

        Identify any special requirements or customizations needed for this trip.`,
        prompt: `Classify this trip request: "${query}"

        Determine:
        1. The primary trip type (the main focus of the trip)
        2. Secondary trip types (other aspects of the trip)
        3. The most appropriate visual theme for presenting this trip
        4. The primary activity the user wants to do
        5. Any special requirements mentioned
        6. Important keywords from the request
        7. Customizations needed for the presentation (e.g., focus on maps, food, accommodation, etc.)

        Be particularly attentive to specific activities or interests mentioned by the user.`,
        schema: z.object({
          primaryType: z
            .enum(Object.values(TripType) as [string, ...string[]])
            .describe('The primary type of trip'),
          secondaryTypes: z
            .array(z.enum(Object.values(TripType) as [string, ...string[]]))
            .describe('Secondary types of trip'),
          visualTheme: z
            .enum(Object.values(VisualTheme) as [string, ...string[]])
            .describe('The visual theme for presenting this trip'),
          primaryActivity: z
            .string()
            .describe('The primary activity the user wants to do'),
          specialRequirements: z
            .array(z.string())
            .describe('Special requirements mentioned in the query'),
          keywords: z
            .array(z.string())
            .describe('Important keywords from the request'),
          customizations: z
            .object({
              mapFocus: z
                .boolean()
                .describe('Whether to focus on maps and locations'),
              foodFocus: z
                .boolean()
                .describe('Whether to focus on food and dining'),
              accommodationFocus: z
                .boolean()
                .describe('Whether to focus on accommodation'),
              budgetFocus: z
                .boolean()
                .describe('Whether to focus on budget considerations'),
              accessibilityFocus: z
                .boolean()
                .describe('Whether to focus on accessibility'),
              familyFocus: z
                .boolean()
                .describe('Whether to focus on family-friendly aspects'),
              sustainabilityFocus: z
                .boolean()
                .describe('Whether to focus on sustainability'),
            })
            .describe('Customizations needed for the presentation'),
        }),
        temperature: 0.3, // Lower temperature for more consistent classification
      });

      console.log(
        'Trip classification:',
        JSON.stringify(classification, null, 2),
      );

      // Convert string values to enum values to ensure type safety
      const typeSafeClassification: TripClassification = {
        ...classification,
        primaryType: classification.primaryType as TripType,
        secondaryTypes: classification.secondaryTypes.map(
          (type) => type as TripType,
        ),
        visualTheme: classification.visualTheme as VisualTheme,
      };

      return typeSafeClassification;
    } catch (error) {
      console.error('Error classifying trip request:', error);

      // Return default classification if classification fails
      return this.getDefaultClassification();
    }
  }

  /**
   * Get default classification when classification fails
   */
  private getDefaultClassification(): TripClassification {
    return {
      primaryType: TripType.GENERAL,
      secondaryTypes: [TripType.CULTURAL, TripType.CITY],
      visualTheme: VisualTheme.CLASSIC,
      primaryActivity: 'sightseeing',
      specialRequirements: [],
      keywords: ['travel', 'vacation', 'trip'],
      customizations: {
        mapFocus: true,
        foodFocus: true,
        accommodationFocus: true,
        budgetFocus: false,
        accessibilityFocus: false,
        familyFocus: false,
        sustainabilityFocus: false,
      },
    };
  }
}
