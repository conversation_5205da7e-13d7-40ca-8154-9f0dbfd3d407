export type PlaceDetails = {
  rating?: number;
  reviews_count?: number;
  reviews?: Array<{
    author_name?: string;
    rating?: number;
    text?: string;
    time_description?: string;
    relative_time_description?: string;
  }>;
  opening_hours?: string[];
  price_level?: number;
  formatted_address?: string;
  website?: string;
  phone?: string;
  photos?: Array<{
    photo_reference?: string;
    width?: number;
    height?: number;
    url?: string;
  }>;
};

const inMemoryCache: Record<string, PlaceDetails> = {};

export async function getPlaceDetails(placeId: string): Promise<PlaceDetails | null> {
  if (!placeId) return null;
  if (inMemoryCache[placeId]) return inMemoryCache[placeId];

  const url = `/api/places/details?place_id=${encodeURIComponent(placeId)}`;
  try {
    const res = await fetch(url, { method: 'GET', cache: 'no-store' });
    if (!res.ok) return null;
    const data = await res.json();
    const details: PlaceDetails | undefined = data?.details;
    if (details) inMemoryCache[placeId] = details;
    return details ?? null;
  } catch {
    return null;
  }
}
