import { z } from 'zod';
import {
  generateObject,
  type LanguageModel,
} from 'ai';
import type { TripPlan } from '../types';
import {
  withAIModelResilience,
  createMockGenerateObjectResult,
} from '../api-resilience';

/**
 * EvaluatorAgent is responsible for evaluating and improving the trip plan
 * to ensure it meets quality standards and is logistically feasible.
 */
export class EvaluatorAgent {
  private model: LanguageModel;

  constructor(model: LanguageModel) {
    this.model = model;
  }

  /**
   * Evaluate and improve the trip plan
   */
  async evaluateAndImprove(tripPlan: TripPlan) {
    try {
      // Check if duration is null or undefined
      if (
        tripPlan.destination.duration === null ||
        tripPlan.destination.duration === undefined
      ) {
        console.log(
          'Duration is null or undefined. Using default duration for evaluation.',
        );
        // We can still evaluate the trip plan without a duration
      }

      // Get the duration text for the prompt
      const durationText =
        tripPlan.destination.duration !== null
          ? `for ${tripPlan.destination.duration} days`
          : `(duration not specified)`;

      // 1. Evaluate the trip plan
      const { object: evaluation } = await generateObject({
        model: this.model,
        system: `You are an expert travel consultant who evaluates travel itineraries for quality and feasibility.
        Evaluate the provided trip plan for:
        1. Logical flow and timing of activities
        2. Variety of experiences
        3. Realistic travel times between locations
        4. Completeness of information
        5. Accuracy of POI coordinates
        6. Overall quality and value

        Be thorough and critical in your evaluation.`,
        prompt: `Evaluate this trip plan for ${tripPlan.destination.destination}, ${tripPlan.destination.country} ${durationText}:

        ${JSON.stringify(tripPlan, null, 2)}`,
        schema: z.object({
          logicalFlow: z
            .number()
            .min(1)
            .max(10)
            .describe('Score for logical flow and timing (1-10)'),
          variety: z
            .number()
            .min(1)
            .max(10)
            .describe('Score for variety of experiences (1-10)'),
          travelTimes: z
            .number()
            .min(1)
            .max(10)
            .describe('Score for realistic travel times (1-10)'),
          completeness: z
            .number()
            .min(1)
            .max(10)
            .describe('Score for completeness of information (1-10)'),
          coordinateAccuracy: z
            .number()
            .min(1)
            .max(10)
            .describe('Score for accuracy of POI coordinates (1-10)'),
          overallQuality: z
            .number()
            .min(1)
            .max(10)
            .describe('Score for overall quality (1-10)'),
          strengths: z.array(z.string()).describe('Strengths of the trip plan'),
          weaknesses: z
            .array(z.string())
            .describe('Weaknesses of the trip plan'),
          suggestedImprovements: z
            .array(z.string())
            .describe('Suggested improvements'),
        }),
      });

      // 2. If the evaluation indicates issues, improve the trip plan
      if (
        evaluation.logicalFlow < 7 ||
        evaluation.variety < 7 ||
        evaluation.travelTimes < 7 ||
        evaluation.completeness < 7 ||
        evaluation.coordinateAccuracy < 7 ||
        evaluation.overallQuality < 7
      ) {
        return await this.improveTripPlan(tripPlan, evaluation);
      }

      // If the plan is already good, return it as is
      return {
        evaluation,
        improvedPlan: tripPlan,
        wasImproved: false,
      };
    } catch (error) {
      console.error('Error evaluating trip plan:', error);

      // Return the original plan if evaluation fails
      return {
        evaluation: {
          logicalFlow: 7,
          variety: 7,
          travelTimes: 7,
          completeness: 7,
          coordinateAccuracy: 7,
          overallQuality: 7,
          strengths: ['Comprehensive itinerary'],
          weaknesses: ['Could not fully evaluate due to an error'],
          suggestedImprovements: ['Review the plan manually'],
        },
        improvedPlan: tripPlan,
        wasImproved: false,
      };
    }
  }

  /**
   * Improve the trip plan based on the evaluation
   */
  private async improveTripPlan(tripPlan: TripPlan, evaluation: any) {
    try {
      // Get the duration text for the prompt
      const durationText =
        tripPlan.destination.duration !== null
          ? `for ${tripPlan.destination.duration} days`
          : `(duration not specified)`;

      // Generate an improved trip plan with enhanced error handling
      const generateObjectResult = await withAIModelResilience(
        () =>
          generateObject({
            model: this.model,
            system: `You are an expert travel planner who specializes in optimizing travel itineraries.
          Improve the provided trip plan based on the evaluation feedback.
          Focus on addressing the weaknesses and implementing the suggested improvements.
          Ensure logical flow, variety of experiences, realistic travel times, and accurate information.
          Maintain the same structure but enhance the content.`,
            prompt: `Improve this trip plan for ${tripPlan.destination.destination}, ${tripPlan.destination.country} ${durationText} based on the following evaluation:

        Evaluation:
        ${JSON.stringify(evaluation, null, 2)}

        Original Trip Plan:
        ${JSON.stringify(tripPlan, null, 2)}

        Please provide an improved version of the trip plan that addresses the weaknesses and implements the suggested improvements.`,
            schema: z.object({
              destination: z.object({
                destination: z.string(),
                country: z.string(),
                duration: z.number().nullable(),
                coordinates: z.object({
                  lat: z.string(),
                  lng: z.string(),
                }),
              }),
              days: z.array(
                z.object({
                  day: z.number(),
                  activities: z.array(
                    z.object({
                      time: z.string(),
                      activity: z.string(),
                      location: z.string(),
                      description: z.string(),
                      poiIndex: z.number().optional(),
                    }),
                  ),
                }),
              ),
              pois: z.array(
                z.object({
                  name: z.string(),
                  lat: z.string(),
                  lng: z.string(),
                  day: z.string(),
                  type: z.enum(['attraction', 'restaurant', 'hotel']),
                  description: z.string(),
                }),
              ),
              localPhrases: z.array(
                z.object({
                  phrase: z.string(),
                  translation: z.string(),
                  pronunciation: z.string(),
                }),
              ),
              travelTips: z.array(
                z.object({
                  category: z.string(),
                  tips: z.array(z.string()),
                }),
              ),
              budget: z.array(
                z.object({
                  category: z.string(),
                  estimatedCost: z.string(),
                  notes: z.string(),
                }),
              ),
            }),
          }),
        'gemini-2.0-flash',
        'trip-plan-improvement',
        () => Promise.resolve(createMockGenerateObjectResult(tripPlan)),
      );

      // Link activities to POIs where possible
      const linkedPlan = this.linkActivitiesToPOIs(generateObjectResult.object);

      return {
        evaluation,
        improvedPlan: linkedPlan,
        wasImproved: true,
      };
    } catch (error) {
      console.error('Error improving trip plan:', error);

      // Return the original plan if improvement fails
      return {
        evaluation,
        improvedPlan: tripPlan,
        wasImproved: false,
      };
    }
  }

  /**
   * Link activities to POIs where possible to improve the integration
   */
  private linkActivitiesToPOIs(plan: TripPlan): TripPlan {
    // Create a copy of the plan to modify
    const linkedPlan = JSON.parse(JSON.stringify(plan)) as TripPlan;

    // For each day and activity, try to find a matching POI
    linkedPlan.days.forEach((day) => {
      day.activities.forEach((activity, activityIndex) => {
        // Find a POI that matches this activity
        const poiIndex = linkedPlan.pois.findIndex(
          (poi) =>
            poi.day === `Day ${day.day}` &&
            (poi.name.includes(activity.activity) ||
              activity.activity.includes(poi.name) ||
              poi.name.includes(activity.location) ||
              activity.location.includes(poi.name)),
        );

        if (poiIndex !== -1) {
          // Link the activity to the POI
          day.activities[activityIndex].poiIndex = poiIndex;
        }
      });
    });

    return linkedPlan;
  }
}
