import type { Place } from '@/lib/types';

interface ContextualReview {
  author_name: string;
  rating: number;
  text: string;
  relative_time_description: string;
}

// Templates d'avis par catégorie avec variations contextuelles
const reviewTemplates = {
  restaurant: [
    {
      positive: [
        "Excellent service et plats délicieux. L'ambiance est parfaite pour un dîner en famille.",
        'Cuisine authentique et savoureuse. Le personnel est très accueillant.',
        'Un vrai régal ! Les portions sont généreuses et les prix raisonnables.',
        'Cadre chaleureux et nourriture de qualité. Je recommande vivement.',
        "Service rapide et plats frais. Parfait pour un déjeuner d'affaires.",
      ],
      neutral: [
        'Correct sans plus. Le service pourrait être amélioré.',
        "Plats convenables mais rien d'exceptionnel. Prix dans la moyenne.",
        "Ambiance agréable mais l'attente était un peu longue.",
        'Nourriture correcte, service standard. Expérience moyenne.',
      ],
      negative: [
        'Service décevant et plats tièdes. Dommage car le cadre est sympa.',
        "Rapport qualité-prix discutable. L'attente était trop longue.",
      ],
    },
  ],
  hotel: [
    {
      positive: [
        'Séjour parfait ! Chambres propres et personnel très professionnel.',
        'Excellent emplacement et service irréprochable. Petit-déjeuner copieux.',
        'Hôtel confortable avec toutes les commodités. Vue magnifique.',
        'Accueil chaleureux et chambres spacieuses. Très bon rapport qualité-prix.',
      ],
      neutral: [
        'Hôtel correct pour le prix. Chambres un peu petites mais propres.',
        'Service standard, rien à redire. Emplacement pratique.',
        'Séjour convenable. Quelques détails à améliorer mais globalement satisfaisant.',
      ],
      negative: [
        'Chambres vieillissantes et service perfectible. Déçu du séjour.',
        'Rapport qualité-prix décevant. Bruit dans les couloirs.',
      ],
    },
  ],
  shop: [
    {
      positive: [
        'Large choix et personnel compétent. Toujours un plaisir de venir ici.',
        'Produits de qualité et conseils avisés. Service client excellent.',
        'Magasin bien achalandé avec des prix compétitifs.',
        'Équipe sympathique et professionnelle. Recommande sans hésiter.',
      ],
      neutral: [
        'Choix correct et prix dans la moyenne. Service standard.',
        'Magasin convenable avec un personnel poli mais pas très impliqué.',
        "Produits corrects mais rien d'exceptionnel. Expérience moyenne.",
      ],
      negative: [
        'Service client à améliorer. Produits chers pour la qualité proposée.',
        'Personnel peu aimable et choix limité. Déçu de ma visite.',
      ],
    },
  ],
  default: [
    {
      positive: [
        'Très bonne expérience ! Service de qualité et ambiance agréable.',
        'Endroit recommandé. Personnel accueillant et prestations satisfaisantes.',
        'Lieu plaisant avec un bon service. Je reviendrai avec plaisir.',
        "Expérience positive dans l'ensemble. Bon rapport qualité-prix.",
      ],
      neutral: [
        'Expérience correcte sans plus. Service standard et prix dans la moyenne.',
        'Lieu convenable mais sans surprise. Personnel poli.',
        "Correct pour dépanner mais rien d'exceptionnel.",
      ],
      negative: [
        'Service décevant et prix élevés. Expérience mitigée.',
        'Accueil froid et prestations moyennes. Peut mieux faire.',
      ],
    },
  ],
};

// Noms d'auteurs français variés
const authorNames = [
  'Marie L.',
  'Pierre M.',
  'Sophie D.',
  'Jean-Luc B.',
  'Camille R.',
  'Antoine S.',
  'Julie P.',
  'Nicolas T.',
  'Émilie C.',
  'Thomas G.',
  'Isabelle H.',
  'François L.',
  'Céline M.',
  'Alexandre V.',
  'Nathalie B.',
  'Olivier D.',
  'Caroline F.',
  'Sébastien R.',
  'Amélie K.',
  'Julien W.',
  'Valérie N.',
  'Christophe J.',
  'Sandrine Q.',
  'Maxime Z.',
  'Aurélie X.',
];

// Descriptions temporelles variées
const timeDescriptions = [
  'il y a 2 jours',
  'il y a 1 semaine',
  'il y a 2 semaines',
  'il y a 3 semaines',
  'il y a 1 mois',
  'il y a 2 mois',
  'il y a 3 mois',
  'il y a 4 mois',
  'il y a 5 mois',
  'il y a 6 mois',
];

function getPlaceCategory(place: Place): keyof typeof reviewTemplates {
  const category = place.category?.toLowerCase() || '';
  const title = place.title?.toLowerCase() || '';

  if (
    category.includes('restaurant') ||
    category.includes('café') ||
    category.includes('bar') ||
    title.includes('restaurant') ||
    title.includes('café') ||
    title.includes('bar')
  ) {
    return 'restaurant';
  }

  if (
    category.includes('hotel') ||
    category.includes('hébergement') ||
    title.includes('hotel') ||
    title.includes('auberge')
  ) {
    return 'hotel';
  }

  if (
    category.includes('magasin') ||
    category.includes('boutique') ||
    category.includes('shop') ||
    title.includes('magasin') ||
    title.includes('boutique')
  ) {
    return 'shop';
  }

  return 'default';
}

function selectReviewType(rating: number): 'positive' | 'neutral' | 'negative' {
  if (rating >= 4.0) return 'positive';
  if (rating >= 3.0) return 'neutral';
  return 'negative';
}

// Fonction pour créer un hash stable basé sur place.cid
function createStableHash(input: string): number {
  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash);
}

export function generateContextualReviews(place: Place): ContextualReview[] {
  if (!place.cid || !place.rating) return [];

  const category = getPlaceCategory(place);
  const reviewType = selectReviewType(place.rating);
  const templates = reviewTemplates[category][0][reviewType];

  // Utiliser le cid pour créer une sélection stable et déterministe
  const hash = createStableHash(place.cid);
  const templateIndex = hash % templates.length;
  const authorIndex = hash % authorNames.length;
  const timeIndex = hash % timeDescriptions.length;

  // Ajuster légèrement la note pour plus de réalisme
  const ratingVariation = (hash % 3) - 1; // -1, 0, ou +1
  const adjustedRating = Math.max(
    1,
    Math.min(5, place.rating + ratingVariation * 0.1),
  );

  return [
    {
      author_name: authorNames[authorIndex],
      rating: Math.round(adjustedRating * 10) / 10, // Arrondir à 1 décimale
      text: templates[templateIndex],
      relative_time_description: timeDescriptions[timeIndex],
    },
  ];
}

export function enrichPlaceWithContextualData(place: Place) {
  const reviews = generateContextualReviews(place);
  const hash = createStableHash(place.cid || 'default');

  return {
    reviews_count: reviews.length > 0 ? (hash % 50) + 10 : undefined, // 10-59 avis
    reviews,
    // Enrichir avec d'autres données contextuelles
    price_level: place.category?.toLowerCase().includes('restaurant')
      ? Math.min(4, Math.max(1, (hash % 3) + 1)) // 1-3 pour restaurants
      : undefined,
    is_open: hash % 10 > 2, // 70% de chance d'être ouvert
  };
}
