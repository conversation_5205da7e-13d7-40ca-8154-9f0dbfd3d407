import React, { useEffect, useRef } from 'react';
import { Streamdown } from 'streamdown';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

export function MarkdownRenderer({
  content,
  className = '',
}: MarkdownRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  // Utiliser useEffect pour s'assurer que le formatage persiste
  useEffect(() => {
    // Forcer un re-rendu du contenu Markdown si nécessaire
    if (containerRef.current) {
      // Optionnel: ajouter une classe pour indiquer que le contenu est formaté
      containerRef.current.classList.add('markdown-formatted');
    }
  }, [content]);

  return (
    <div
      ref={containerRef}
      className={`prose dark:prose-invert max-w-none markdown-container ${className}`}
    >
      <Streamdown>{content}</Streamdown>
    </div>
  );
}
