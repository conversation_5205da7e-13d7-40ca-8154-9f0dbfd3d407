export type Entry<K extends string | number | symbol = string, V = unknown> = [K, V];

/**
 * Safe alternative to Object.entries that handles null/undefined gracefully.
 * Returns an empty array when the input is nullish or not an object.
 */
export function safeEntries<T extends object>(obj: T | null | undefined): Array<[keyof T & string, T[keyof T]]> {
  if (!obj || typeof obj !== 'object') return [] as any;
  return Object.entries(obj as Record<string, unknown>) as Array<[
    keyof T & string,
    T[keyof T]
  ]>;
}

/**
 * Typed convenience wrapper for `safeEntries`.
 * Use `entries(obj)` instead of `Object.entries(obj)` when `obj` may be null/undefined.
 */
export function entries<T extends object>(obj: T | null | undefined): Array<[keyof T & string, T[keyof T]]> {
  return safeEntries(obj);
}
