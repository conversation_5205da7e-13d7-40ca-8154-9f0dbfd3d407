import { z } from 'zod';
import {
  generateObject,
  type LanguageModel,
  type UIMessageStreamWriter,
} from 'ai';
import type { DestinationInfo, DayItinerary } from '../types';
import type { ChatMessage } from '@/lib/types';

/**
 * ItineraryAgent is responsible for generating a day-by-day itinerary
 * for the trip, including activities, local phrases, travel tips, and budget information.
 */
export class ItineraryAgent {
  private model: LanguageModel;

  constructor(model: LanguageModel) {
    this.model = model;
  }

  /**
   * Generate a complete itinerary for the trip
   */
  async generateItinerary(
    destinationInfo: DestinationInfo,
    dataStream?: UIMessageStreamWriter<ChatMessage>,
  ) {
    try {
      const { destination, country, duration } = destinationInfo;

      // Stream progress update
      if (dataStream) {
        console.log(
          '🔄 [ItineraryAgent] Streaming: Starting itinerary generation',
        );
      }

      // Check if duration is null or undefined
      if (duration === null || duration === undefined) {
        console.error(
          'Duration is null or undefined. Cannot generate itinerary.',
        );

        if (dataStream) {
          console.log(
            '🔄 [ItineraryAgent] Streaming: Using fallback itinerary due to missing duration',
          );
        }

        return this.generateFallbackItinerary(destinationInfo);
      }

      // Stream LLM generation start
      if (dataStream) {
        console.log(
          `🔄 [ItineraryAgent] Streaming: Generating ${duration}-day itinerary for ${destination}`,
        );
      }

      // Generate the day-by-day itinerary
      const { object: itineraryData } = await generateObject({
        model: this.model,
        system: `You are an expert travel planner with deep knowledge of destinations worldwide.
        
        🚨 CRITICAL REQUIREMENT: You MUST generate EXACTLY ${duration} days of itinerary. NO MORE, NO LESS.
        
        The user has specifically requested ${duration} days and this number is ABSOLUTE and NON-NEGOTIABLE.
        
        STRICT RULES:
        - Generate exactly ${duration} days in the "days" array
        - Each day must be numbered from 1 to ${duration}
        - Do not add extra days "just in case" or for "flexibility"
        - Do not suggest additional days beyond the requested ${duration}
        - The user's request for ${duration} days is final and must be respected
        
        Create a detailed day-by-day itinerary for the specified destination and duration.
        Include a variety of activities for each day, with specific times, locations, and descriptions.
        Also include local phrases, travel tips, and budget information.
        Be specific, realistic, and consider the logistics of the destination.
        Ensure activities are properly timed with reasonable travel times between locations.`,
        prompt: `Create a detailed travel itinerary for ${destination}, ${country} for EXACTLY ${duration} days.
        
        IMPORTANT: The user has specifically requested ${duration} days. You must respect this exact number.
        
        Include:
        1. A day-by-day schedule with specific times and activities for EXACTLY ${duration} days
        2. Local phrases with translations and pronunciations
        3. Travel tips for the destination
        4. Budget information with estimated costs
        
        Remember: Generate EXACTLY ${duration} days, numbered from 1 to ${duration}.`,
        schema: z.object({
          days: z
            .array(
              z.object({
                day: z.number().describe('Day number'),
                activities: z
                  .array(
                    z.object({
                      time: z
                        .string()
                        .describe('Time of the activity (e.g., "09:00")'),
                      activity: z.string().describe('Name of the activity'),
                      location: z.string().describe('Location of the activity'),
                      description: z
                        .string()
                        .describe('Description of the activity'),
                    }),
                  )
                  .min(4)
                  .describe('Activities for the day'),
              }),
            )
            .length(duration)
            .describe(
              `CRITICAL: Generate EXACTLY ${duration} days, no more, no less. This is a strict requirement.`,
            ),
          localPhrases: z
            .array(
              z.object({
                phrase: z.string().describe('Phrase in local language'),
                translation: z.string().describe('English translation'),
                pronunciation: z.string().describe('Pronunciation guide'),
              }),
            )
            .min(5)
            .describe('Useful local phrases'),
          travelTips: z
            .array(
              z.object({
                category: z
                  .string()
                  .describe(
                    'Category of the tip (e.g., "Transportation", "Safety")',
                  ),
                tips: z
                  .array(z.string())
                  .min(2)
                  .describe('Tips in this category'),
              }),
            )
            .min(3)
            .describe('Travel tips for the destination'),
          budget: z
            .array(
              z.object({
                category: z
                  .string()
                  .describe('Expense category (e.g., "Accommodation", "Food")'),
                estimatedCost: z.string().describe('Estimated cost range'),
                notes: z
                  .string()
                  .describe('Additional notes about this expense category'),
              }),
            )
            .min(4)
            .describe('Budget information'),
        }),
      });

      // Stream completion notification
      if (dataStream) {
        console.log(
          '🔄 [ItineraryAgent] Streaming: Itinerary generation completed successfully',
        );
      }

      return itineraryData;
    } catch (error) {
      console.error('Error generating itinerary:', error);

      // Stream error notification
      if (dataStream) {
        console.log(
          '🔄 [ItineraryAgent] Streaming: Error occurred, using fallback itinerary',
        );
      }

      // Return a fallback itinerary if generation fails
      return this.generateFallbackItinerary(destinationInfo);
    }
  }

  /**
   * Generate a fallback itinerary in case the main generation fails
   */
  private generateFallbackItinerary(destinationInfo: DestinationInfo) {
    const { destination, duration } = destinationInfo;

    // Use a default duration of 3 days if duration is null
    const actualDuration = duration !== null ? duration : 3;
    console.log(
      `Using fallback duration of ${actualDuration} days for itinerary generation`,
    );

    // Create a basic itinerary structure
    const days: DayItinerary[] = [];

    for (let i = 1; i <= actualDuration; i++) {
      days.push({
        day: i,
        activities: [
          {
            time: '09:00',
            activity: 'Breakfast',
            location: 'Local café',
            description: 'Start your day with a delicious local breakfast',
          },
          {
            time: '10:30',
            activity: 'Sightseeing',
            location: `${destination} city center`,
            description: 'Explore the main attractions in the city center',
          },
          {
            time: '13:00',
            activity: 'Lunch',
            location: 'Local restaurant',
            description: 'Enjoy local cuisine for lunch',
          },
          {
            time: '15:00',
            activity: 'Cultural visit',
            location: 'Museum or historical site',
            description: 'Visit a cultural or historical landmark',
          },
          {
            time: '19:00',
            activity: 'Dinner',
            location: 'Restaurant',
            description: 'Dinner at a recommended restaurant',
          },
        ],
      });
    }

    return {
      days,
      localPhrases: [
        {
          phrase: 'Hello',
          translation: 'Hello',
          pronunciation: 'Hello',
        },
        {
          phrase: 'Thank you',
          translation: 'Thank you',
          pronunciation: 'Thank you',
        },
        {
          phrase: 'Yes',
          translation: 'Yes',
          pronunciation: 'Yes',
        },
        {
          phrase: 'No',
          translation: 'No',
          pronunciation: 'No',
        },
        {
          phrase: 'Excuse me',
          translation: 'Excuse me',
          pronunciation: 'Excuse me',
        },
      ],
      travelTips: [
        {
          category: 'Transportation',
          tips: [
            'Use public transportation when possible',
            'Taxis are available throughout the city',
          ],
        },
        {
          category: 'Safety',
          tips: [
            'Keep your belongings secure',
            'Be aware of your surroundings',
          ],
        },
        {
          category: 'Weather',
          tips: [
            'Check the weather forecast before heading out',
            'Bring appropriate clothing',
          ],
        },
      ],
      budget: [
        {
          category: 'Accommodation',
          estimatedCost: '$100-200 per night',
          notes: 'Prices vary depending on location and season',
        },
        {
          category: 'Food',
          estimatedCost: '$30-50 per day',
          notes: 'Street food is cheaper than restaurants',
        },
        {
          category: 'Transportation',
          estimatedCost: '$10-20 per day',
          notes: 'Public transportation is the most economical option',
        },
        {
          category: 'Activities',
          estimatedCost: '$20-50 per day',
          notes: 'Many attractions offer discounts for students and seniors',
        },
      ],
    };
  }
}
