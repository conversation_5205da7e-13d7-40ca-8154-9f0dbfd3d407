import { z } from 'zod';
import { tavily } from '@tavily/core';
import type { Session } from 'next-auth';
import { tool, generateId, type UIMessageStreamWriter } from 'ai';
import type { ChatMessage } from '@/lib/types';

function sanitizeUrl(url: string, strictMode = false): string {
  try {
    // Vérifier si l'URL est déjà encodée pour éviter un double encodage
    const decoded = decodeURI(url);
    if (decoded !== url && !strictMode) {
      return url; // L'URL est déjà encodée et on n'est pas en mode strict
    }

    // Analyser l'URL pour encoder correctement chaque partie
    const urlObj = new URL(decoded); // Utiliser decoded pour éviter double encodage

    // Encoder le chemin mais préserver les caractères / et :
    urlObj.pathname = urlObj.pathname
      .split('/')
      .map((segment) => encodeURIComponent(segment).replace(/%3A/g, ':'))
      .join('/');

    return urlObj.toString();
  } catch (e) {
    // Si l'URL n'est pas valide pour être parsée, utiliser encodeURI
    console.log(`Impossible de parser l'URL: ${url}, utilisation d'encodeURI`);
    return encodeURI(url);
  }
}

async function isValidImageUrl(
  url: string,
): Promise<{ valid: boolean; redirectedUrl?: string }> {
  const MAX_RETRIES = 2;
  const RETRY_DELAY = 1000;

  // Hard blocklist for known-problematic domains/endpoints
  const blockedDomains = [
    'lookaside.fbsbx.com', // Facebook crawler endpoints often return non-image or null
    'facebook.com',
    'fbcdn.net',
    'eat-list.fr', // frequently 403, blocks hotlinking
  ];
  const isBlocked = (urlToCheck: string) => {
    try {
      const u = new URL(urlToCheck);
      const host = u.hostname.toLowerCase();
      if (blockedDomains.some((d) => host.endsWith(d))) return true;
      // Block specific Facebook crawler path pattern
      if (host.includes('fbsbx.com') && u.pathname.includes('/crawler/media'))
        return true;
    } catch {
      // ignore parse errors
    }
    return false;
  };

  if (isBlocked(url)) {
    console.log(`Blocked image domain, skipping: ${url}`);
    return { valid: false };
  }

  // Fonction utilitaire pour vérifier si l'URL a une extension d'image
  const hasImageExtension = (urlToCheck: string) => {
    const imageExtensions = [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.webp',
      '.svg',
      '.bmp',
    ];
    const lowerUrl = urlToCheck.toLowerCase();
    return imageExtensions.some((ext) => lowerUrl.endsWith(ext));
  };

  // Si l'URL a une extension d'image, on la considère valide directement
  if (hasImageExtension(url)) {
    console.log(`URL has image extension, considering valid: ${url}`);
    return { valid: true };
  }

  // Fonction utilitaire pour essayer via le proxy
  async function tryFetchViaProxy(
    urlToProxy: string,
  ): Promise<{ valid: boolean; redirectedUrl?: string }> {
    try {
      const controller = new AbortController();
      const proxyTimeout = setTimeout(() => controller.abort(), 5000);

      // Utiliser une URL absolue pour le proxy
      const baseUrl = process.env.VERCEL_URL
        ? `https://${process.env.VERCEL_URL}`
        : process.env.NODE_ENV === 'development'
          ? 'http://localhost:3000'
          : '';

      const proxyUrl = `${baseUrl}/api/proxy-image?url=${encodeURIComponent(urlToProxy)}`;

      console.log(`Trying proxy with URL: ${proxyUrl}`);

      const proxyResponse = await fetch(proxyUrl, {
        method: 'HEAD',
        signal: controller.signal,
      });

      clearTimeout(proxyTimeout);

      if (proxyResponse.ok) {
        const contentType = proxyResponse.headers?.get('content-type');
        const proxyRedirectedUrl =
          proxyResponse.headers?.get('x-final-url') || undefined;

        if (
          contentType?.startsWith?.('image/') ||
          hasImageExtension(urlToProxy)
        ) {
          console.log(`Proxy validation successful for ${urlToProxy}`);
          return { valid: true, redirectedUrl: proxyRedirectedUrl };
        }
      }

      return { valid: false };
    } catch (proxyError) {
      console.error(`Proxy validation failed for ${urlToProxy}:`, proxyError);
      return { valid: false };
    }
  }

  for (let attempt = 0; attempt <= MAX_RETRIES; attempt++) {
    try {
      const controller = new AbortController();
      const timeout = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(url, {
        method: 'GET', // Utiliser GET au lieu de HEAD pour être plus fiable
        signal: controller.signal,
        headers: {
          Accept: 'image/*',
          'User-Agent': 'Mozilla/5.0 (compatible; ImageValidator/1.0)',
        },
        redirect: 'follow',
      });

      clearTimeout(timeout);

      const contentType = response.headers?.get('content-type');
      const redirectedUrl = response.redirected ? response.url : undefined;

      console.log(
        `Image validation [${url}]: status=${response.status}, content-type=${contentType}`,
      );

      if (response.redirected) {
        console.log(`Image was redirected from ${url} to ${redirectedUrl}`);
      }

      if (response.status === 404) {
        console.log(`Image not found (404): ${url}`);
        return { valid: false };
      }

      if (response.status === 403) {
        console.log(`Access forbidden (403) - likely CORS issue: ${url}`);
        return await tryFetchViaProxy(url);
      }

      if (response.status >= 400) {
        console.log(
          `Image request failed with status ${response.status}: ${url}`,
        );
        return { valid: false };
      }

      // Si le content-type est null ou undefined mais que le statut est 200
      if (!contentType && response.status === 200) {
        // Vérifier si c'est une image en examinant les premiers octets
        try {
          const blob = await response.blob();
          // Si le blob a une taille raisonnable pour une image, on considère que c'est valide
          if (blob.size > 0 && blob.size < 10 * 1024 * 1024) {
            // Max 10MB
            console.log(
              `No content-type but blob size looks like an image (${blob.size} bytes): ${url}`,
            );
            return { valid: true, redirectedUrl };
          }
        } catch (blobError) {
          console.error(`Error reading blob for ${url}:`, blobError);
        }

        // Si l'URL a une extension d'image, on la considère valide
        if (hasImageExtension(url)) {
          console.log(`No content-type but URL has image extension: ${url}`);
          return { valid: true, redirectedUrl };
        }

        console.log(
          `Invalid content type for image: ${contentType}, url: ${url}`,
        );
        return await tryFetchViaProxy(url);
      }

      if (!contentType?.startsWith?.('image/')) {
        console.log(
          `Invalid content type for image: ${contentType}, url: ${url}`,
        );
        return { valid: false };
      }

      return { valid: true, redirectedUrl };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);

      if (
        errorMsg.includes('CORS') ||
        errorMsg.includes('blocked by CORS policy')
      ) {
        console.error(`CORS error for ${url}:`, errorMsg);
        return await tryFetchViaProxy(url);
      }

      // Si l'opération a été abandonnée (timeout), mais que l'URL a une extension d'image
      if (errorMsg.includes('aborted') && hasImageExtension(url)) {
        console.log(
          `Operation aborted but URL has image extension, considering valid: ${url}`,
        );
        return { valid: true };
      }

      // Retry pour les erreurs réseau (timeout, connection refused, etc.)
      if (attempt < MAX_RETRIES) {
        console.log(
          `Network error, retrying (${attempt + 1}/${MAX_RETRIES}): ${url}`,
        );
        await new Promise((resolve) =>
          setTimeout(resolve, RETRY_DELAY * (attempt + 1)),
        );
        continue;
      }

      console.error(`Image validation error for ${url}:`, errorMsg);
      return { valid: false };
    }
  }

  return { valid: false };
}

const extractDomain = (url: string): string => {
  const urlPattern = /^https?:\/\/([^/?#]+)(?:[/?#]|$)/i;
  return url.match(urlPattern)?.[1] || url;
};

const deduplicateByDomainAndUrl = <T extends { url: string }>(
  items: T[],
): T[] => {
  // Utiliser Map au lieu de Set pour de meilleures performances
  const seenDomains = new Map<string, boolean>();
  const seenUrls = new Map<string, boolean>();
  const results: T[] = [];

  for (const item of items) {
    const domain = extractDomain(item.url);

    // Vérification plus rapide avec Map.has() au lieu de Set.has()
    if (!seenUrls.has(item.url) && !seenDomains.has(domain)) {
      seenUrls.set(item.url, true);
      seenDomains.set(domain, true);
      results.push(item);
    }
  }

  return results;
};

export const web_search = ({
  session,
  dataStream,
}: {
  session: Session;
  dataStream: UIMessageStreamWriter<ChatMessage>;
}) =>
  tool({
    description:
      'Search the web comprehensively with multiple targeted queries to gather detailed, precise information from diverse sources. Optimized for generating comprehensive, well-structured responses with specific data, citations, and authoritative sources.',
    inputSchema: z.object({
      queries: z.array(
        z
          .string()
          .describe(
            'Array of comprehensive, targeted search queries. Create 6-10 diverse queries covering different aspects: current events, background context, expert analysis, statistical data, official sources, and multiple perspectives. Each query should be specific and designed to gather unique information.',
          ),
      ),
      maxResults: z
        .array(
          z
            .number()
            .describe(
              'Array of maximum results per query. Use 12-15 results per query for comprehensive coverage. Higher numbers ensure diverse, authoritative sources.',
            ),
        )
        .optional(),
      topics: z
        .array(
          z
            .enum(['general', 'news', 'finance'])
            .describe(
              'Array of topic types to search for. Default is general.',
            ),
        )
        .optional(),
      searchDepth: z
        .array(
          z
            .enum(['basic', 'advanced'])
            .describe(
              'Array of search depths. Use "advanced" for detailed, comprehensive results with full content extraction and authoritative sources.',
            ),
        )
        .optional(),
      exclude_domains: z
        .array(z.string())
        .describe(
          'A list of domains to exclude from all search results. Default is an empty list.',
        )
        .optional(),
      language: z
        .string()
        .describe('Preferred language for search results (e.g., "fr", "en").')
        .optional(),
    }),
    execute: async ({
      queries,
      maxResults = [10],
      topics = ['general'],
      searchDepth = ['basic'],
      exclude_domains = [],
      language,
    }: {
      queries: string[];
      maxResults?: number[];
      topics?: ('general' | 'news' | 'finance')[];
      searchDepth?: ('basic' | 'advanced')[];
      exclude_domains?: string[];
      language?: string;
    }) => {
      console.log('\n🌐 ========================================');
      console.log('🌐 WEB_SEARCH (STANDARD) CALLED');
      console.log('🌐 Number of queries:', queries.length);
      console.log('🌐 ========================================\n');

      const apiKey = process.env.TAVILY_API_KEY || '';
      const tvly = tavily({ apiKey });
      const includeImageDescriptions = true;

      // Vérifier si la clé API est définie
      if (!apiKey) {
        console.warn('TAVILY_API_KEY is not set in environment variables');
      }

      // Enhanced defaults for comprehensive search
      const enhancedMaxResults = maxResults?.length
        ? maxResults
        : Array(queries.length).fill(15); // Increased default for better coverage
      const enhancedSearchDepth = searchDepth?.length
        ? searchDepth
        : Array(queries.length).fill('advanced'); // Default to advanced for quality
      const enhancedTopics = topics?.length
        ? topics
        : Array(queries.length).fill('general');

      console.log('🔍 ENHANCED WEB SEARCH CONFIGURATION:');
      console.log('Queries:', queries.length, 'queries');
      console.log('Max Results per query:', enhancedMaxResults);
      console.log('Search Depths:', enhancedSearchDepth);
      console.log('Topics:', enhancedTopics);
      console.log('Exclude Domains:', exclude_domains);
      console.log('Language:', language);

      // Execute searches in parallel with enhanced parameters
      const searchPromises = queries.map(async (query, index) => {
        // Enhanced search parameters for comprehensive results
        const searchParams: any = {
          topic: enhancedTopics[index] || 'general',
          days: enhancedTopics[index] === 'news' ? 30 : undefined, // Extended to 30 days for better news coverage
          maxResults: enhancedMaxResults[index] || 15,
          searchDepth: enhancedSearchDepth[index] || 'advanced',
          includeAnswer: true,
          includeImages: true,
          includeImageDescriptions: includeImageDescriptions,
          excludeDomains: exclude_domains,
          includeRawContent: true, // Enhanced content extraction
        };

        // Add language parameter if specified
        if (language) {
          searchParams.language = language;
        }

        console.log(
          `🔎 Executing enhanced search ${index + 1}/${queries.length}: "${query}"`,
        );

        const data = await tvly.search(query, searchParams);

        // Enhanced result processing with metadata
        const processedResults = deduplicateByDomainAndUrl(
          data.results || [],
        ).map((obj: any) => ({
          url: obj.url,
          title: obj.title || 'Untitled',
          content: obj.content || '',
          published_date:
            enhancedTopics[index] === 'news' ? obj.published_date : undefined,
          score: obj.score || 0, // Relevance score
          raw_content: obj.raw_content || obj.content, // Full content when available
        }));

        // Add comprehensive search completion annotation
        dataStream.write({
          type: 'data-status',
          id: generateId(),
          data: `✅ Query ${index + 1}/${queries.length} completed: "${query.length > 50 ? `${query.substring(0, 50)}...` : query}" - ${processedResults.length} sources found with ${enhancedSearchDepth[index]} depth`,
        });

        return {
          query,
          searchParams, // Include search parameters for transparency
          results: processedResults,
          images: includeImageDescriptions
            ? await Promise.all(
                deduplicateByDomainAndUrl(data.images).map(
                  async ({
                    url,
                    description,
                  }: {
                    url: string;
                    description?: string;
                  }) => {
                    const sanitizedUrl = sanitizeUrl(url);
                    const imageValidation = await isValidImageUrl(sanitizedUrl);
                    return imageValidation.valid
                      ? {
                          url: imageValidation.redirectedUrl || sanitizedUrl,
                          description: description ?? '',
                        }
                      : null;
                  },
                ),
              ).then((results) =>
                results.filter(
                  (image): image is { url: string; description: string } =>
                    image !== null &&
                    typeof image === 'object' &&
                    typeof image.description === 'string' &&
                    image.description !== '',
                ),
              )
            : await Promise.all(
                deduplicateByDomainAndUrl(data.images).map(
                  async ({ url }: { url: string }) => {
                    const sanitizedUrl = sanitizeUrl(url);
                    const imageValidation = await isValidImageUrl(sanitizedUrl);
                    return imageValidation.valid
                      ? imageValidation.redirectedUrl || sanitizedUrl
                      : null;
                  },
                ),
              ).then(
                (results) => results.filter((url) => url !== null) as string[],
              ),
        };
      });

      const searchResults = await Promise.all(searchPromises);

      return {
        searches: searchResults,
      };
    },
  });
