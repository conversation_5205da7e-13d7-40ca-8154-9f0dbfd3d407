import { tool } from 'ai';
import { z } from 'zod';

export const getStockNews = tool({
  description: 'Get recent news for a given stock ticker symbol',
  inputSchema: z.object({
    ticker: z
      .string()
      .describe('Stock ticker symbol (e.g. "AAPL" for Apple, "MSFT" for Microsoft)'),
  }),
  execute: async ({ ticker }) => {
    // Check if ticker is valid
    if (!ticker || typeof ticker !== 'string' || ticker.trim() === '') {
      return { error: 'Please provide a valid stock ticker symbol' };
    }

    // Clean the ticker
    const cleanTicker = ticker.trim().toUpperCase();

    // List of known tickers for validation (optional)
    const knownTickers = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'TSLA', 'NVDA', 'JPM', 'V', 'WMT'];
    
    if (!knownTickers.includes(cleanTicker)) {
      console.log(`Warning: The symbol ${cleanTicker} is not in the list of known tickers.`);
    }

    // Return the data needed to display the news
    return {
      ticker: cleanTicker,
      newsUrl: `https://www.tradingview.com/symbols/${cleanTicker}/news/`,
      timestamp: new Date().toISOString(),
    };
  },
});
