import { z } from 'zod';
import { tool } from 'ai';

// Define and export the tool configuration
export const getTechnicalAnalysis = tool({
  description:
    'Display technical analysis for a specific stock, cryptocurrency, forex pair, or other financial symbol including technical indicators, oscillators, and moving averages with buy/sell/neutral signals. Supports major US stocks (just use ticker like AAPL, MSFT, TSLA), crypto (BINANCE:BTCUSDT), and forex (FX:EURUSD).',
  inputSchema: z.object({
    symbol: z
      .string()
      .describe(
        'The trading symbol to analyze. For US stocks, just use the ticker (e.g., AAPL, MSFT, TSLA, GOOGL). For crypto use BINANCE:BTCUSDT format. For forex use FX:EURUSD format. Exchange prefix will be added automatically for US stocks.',
      ),
    interval: z
      .string()
      .optional()
      .describe(
        'Time interval for analysis (e.g., 1m, 5m, 15m, 1h, 4h, 1D, 1W, 1M). Defaults to 1m if not specified.',
      ),
  }),
  execute: async ({
    symbol,
    interval = '1m',
  }: { symbol: string; interval?: string }) => {
    // Normalize symbol format for TradingView
    let normalizedSymbol = symbol.toUpperCase();

    // If symbol doesn't contain exchange prefix, try to add appropriate one
    if (!normalizedSymbol.includes(':')) {
      // Common US stocks - try NASDAQ first, then NYSE
      const nasdaqStocks = [
        'AAPL',
        'MSFT',
        'GOOGL',
        'GOOG',
        'AMZN',
        'TSLA',
        'META',
        'NVDA',
        'NFLX',
        'ADBE',
      ];
      const nyseStocks = [
        'JPM',
        'JNJ',
        'WMT',
        'PG',
        'UNH',
        'V',
        'HD',
        'MA',
        'DIS',
        'KO',
      ];

      if (nasdaqStocks.includes(normalizedSymbol)) {
        normalizedSymbol = `NASDAQ:${normalizedSymbol}`;
      } else if (nyseStocks.includes(normalizedSymbol)) {
        normalizedSymbol = `NYSE:${normalizedSymbol}`;
      } else {
        // Default to NASDAQ for unknown US stocks
        normalizedSymbol = `NASDAQ:${normalizedSymbol}`;
      }
    }

    // Return a simple object that will be used by the frontend to render the component
    return {
      type: 'technical_analysis',
      data: {
        symbol: normalizedSymbol,
        interval: interval,
      },
    };
  },
});
