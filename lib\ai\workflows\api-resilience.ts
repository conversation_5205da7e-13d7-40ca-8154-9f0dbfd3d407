/**
 * API Resilience utilities for handling API overload and failures
 * Combines retry logic, circuit breakers, and graceful degradation
 */

import { withEnhancedRetry, } from './timeout-config';
import {
  withCircuitBreaker,
  type CircuitBreakerConfig,
} from './circuit-breaker';
import type { GenerateObjectResult } from 'ai';

/**
 * Create a mock GenerateObjectResult for fallback scenarios
 */
export function createMockGenerateObjectResult<T>(object: T): GenerateObjectResult<T> {
  // Return a minimal structure and cast to the expected type to avoid version-specific field mismatches
  return ({
    object,
    finishReason: 'stop' as const,
  } as unknown) as GenerateObjectResult<T>;
}

interface ResilienceOptions {
  serviceName: string;
  operationName: string;
  retryOptions?: {
    maxAttempts?: number;
    baseDelay?: number;
    maxDelay?: number;
    jitterFactor?: number;
  };
  circuitBreakerConfig?: Partial<CircuitBreakerConfig>;
  fallbackOperation?: () => Promise<any>;
  enableCircuitBreaker?: boolean;
  enableRetry?: boolean;
}

/**
 * Execute operation with full resilience (retry + circuit breaker + fallback)
 */
export async function withFullResilience<T>(
  operation: () => Promise<T>,
  options: ResilienceOptions,
): Promise<T> {
  const {
    serviceName,
    operationName,
    retryOptions,
    circuitBreakerConfig,
    fallbackOperation,
    enableCircuitBreaker = true,
    enableRetry = true,
  } = options;

  try {
    // Wrap operation with circuit breaker if enabled
    const protectedOperation = enableCircuitBreaker
      ? () => withCircuitBreaker(operation, serviceName, circuitBreakerConfig)
      : operation;

    // Wrap with retry logic if enabled
    const resilientOperation = enableRetry
      ? () =>
          withEnhancedRetry(
            protectedOperation,
            `${serviceName}:${operationName}`,
            retryOptions,
          )
      : protectedOperation;

    return await resilientOperation();
  } catch (error) {
    console.error(
      `All resilience strategies failed for ${serviceName}:${operationName}:`,
      error,
    );

    // Try fallback operation if available
    if (fallbackOperation) {
      console.log(`Attempting fallback for ${serviceName}:${operationName}`);
      try {
        return await fallbackOperation();
      } catch (fallbackError) {
        console.error(
          `Fallback also failed for ${serviceName}:${operationName}:`,
          fallbackError,
        );
        throw error; // Throw original error, not fallback error
      }
    }

    throw error;
  }
}

/**
 * Specialized resilience for AI model operations
 */
export async function withAIModelResilience<T>(
  operation: () => Promise<T>,
  modelName: string,
  operationName: string,
  fallbackOperation?: () => Promise<T>,
): Promise<T> {
  // Check if this is a long trip operation that needs extended timeouts
  const isLongTripOperation = operationName.includes('long-trip');

  return withFullResilience(operation, {
    serviceName: `ai-model-${modelName}`,
    operationName,
    retryOptions: {
      maxAttempts: isLongTripOperation ? 3 : 5, // Fewer retries for long trips to avoid total timeout
      baseDelay: isLongTripOperation ? 5000 : 3000, // Longer initial delay for long trips
      maxDelay: isLongTripOperation ? 60000 : 45000, // Extended max delay for long trips
      jitterFactor: 0.4, // More jitter for AI models
    },
    circuitBreakerConfig: {
      failureThreshold: isLongTripOperation ? 2 : 3, // Lower threshold for long trips
      recoveryTimeout: isLongTripOperation ? 120000 : 90000, // Longer recovery for long trips
      halfOpenMaxCalls: 2,
    },
    fallbackOperation,
    enableCircuitBreaker: true,
    enableRetry: true,
  });
}

/**
 * Specialized resilience for web search operations
 */
export async function withWebSearchResilience<T>(
  operation: () => Promise<T>,
  searchProvider: string,
  operationName: string,
  fallbackOperation?: () => Promise<T>,
): Promise<T> {
  return withFullResilience(operation, {
    serviceName: `web-search-${searchProvider}`,
    operationName,
    retryOptions: {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 15000,
      jitterFactor: 0.2,
    },
    circuitBreakerConfig: {
      failureThreshold: 5,
      recoveryTimeout: 30000, // 30 seconds
      halfOpenMaxCalls: 3,
    },
    fallbackOperation,
    enableCircuitBreaker: true,
    enableRetry: true,
  });
}

/**
 * Rate limiter to prevent overwhelming APIs
 */
class RateLimiter {
  private requests: number[] = [];
  private maxRequests: number;
  private windowMs: number;

  constructor(maxRequests: number, windowMs: number) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  async acquire(): Promise<void> {
    const now = Date.now();

    // Remove old requests outside the window
    this.requests = this.requests.filter((time) => now - time < this.windowMs);

    if (this.requests.length >= this.maxRequests) {
      // Calculate how long to wait
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.windowMs - (now - oldestRequest) + 100; // Add 100ms buffer

      console.log(`Rate limit reached, waiting ${waitTime}ms`);
      await new Promise((resolve) => setTimeout(resolve, waitTime));

      // Try again after waiting
      return this.acquire();
    }

    this.requests.push(now);
  }
}

// Global rate limiters
const rateLimiters = new Map<string, RateLimiter>();

/**
 * Get or create a rate limiter for a service
 */
export function getRateLimiter(
  serviceName: string,
  maxRequests: number,
  windowMs: number,
): RateLimiter {
  const key = `${serviceName}-${maxRequests}-${windowMs}`;
  const existing = rateLimiters.get(key);
  if (existing) return existing;
  const created = new RateLimiter(maxRequests, windowMs);
  rateLimiters.set(key, created);
  return created;
}

/**
 * Execute operation with rate limiting
 */
export async function withRateLimit<T>(
  operation: () => Promise<T>,
  serviceName: string,
  maxRequests = 10,
  windowMs = 60000, // 1 minute
): Promise<T> {
  const rateLimiter = getRateLimiter(serviceName, maxRequests, windowMs);
  await rateLimiter.acquire();
  return operation();
}

/**
 * Complete resilience wrapper with rate limiting
 */
export async function withCompleteResilience<T>(
  operation: () => Promise<T>,
  options: ResilienceOptions & {
    rateLimit?: {
      maxRequests: number;
      windowMs: number;
    };
  },
): Promise<T> {
  const { rateLimit, ...resilienceOptions } = options;

  // Apply rate limiting if configured
  const rateLimitedOperation = rateLimit
    ? () =>
        withRateLimit(
          operation,
          options.serviceName,
          rateLimit.maxRequests,
          rateLimit.windowMs,
        )
    : operation;

  return withFullResilience(rateLimitedOperation, resilienceOptions);
}

export { RateLimiter };
