import { generateUUID } from '@/lib/utils';
import { tool, type UIMessageStreamWriter } from 'ai';
import { z } from 'zod';
import type { Session } from 'next-auth';
import {
  artifactKinds,
  documentHandlersByArtifactKind,
} from '@/lib/artifacts/server';
import { enqueueMemory } from '@/lib/memoryBatcher';
import type { ChatMessage } from '@/lib/types';

async function saveToMemoryAsync(
  id: string,
  userId: string,
  data: string,
  metadata: Record<string, any>,
  label: string,
) {
  enqueueMemory({
    data,
    userId,
    metadata,
  });
}

function saveGenerationToMemory({
  id,
  title,
  kind,
  prompt,
  userId,
  disableMemory = false,
}: {
  id: string;
  title: string;
  kind: string;
  prompt: string;
  userId: string;
  disableMemory?: boolean;
}) {
  if (disableMemory) return;

  const memoryContent = `LLM generation request for document: "${title}" (${kind})\nPrompt: ${prompt}`;
  saveToMemoryAsync(
    id,
    userId,
    memoryContent,
    {
      documentId: id,
      documentTitle: title,
      documentKind: kind,
      action: 'generation',
    },
    'saveGeneration',
  );
}

function saveDocumentContentToMemory({
  id,
  title,
  kind,
  content,
  userId,
  disableMemory = false,
}: {
  id: string;
  title: string;
  kind: string;
  content: string;
  userId: string;
  disableMemory?: boolean;
}) {
  if (disableMemory || !content.trim()) return;

  saveToMemoryAsync(
    id,
    userId,
    content,
    {
      documentId: id,
      documentTitle: title,
      documentKind: kind,
      action: 'content',
      isDocumentContent: true,
    },
    'saveContent',
  );
}

interface CreateDocumentProps {
  session: Session;
  dataStream: UIMessageStreamWriter<ChatMessage>;
}

export const createDocument = ({ session, dataStream }: CreateDocumentProps) =>
  tool({
    description:
      'Create a document for a writing or content creation activities. This tool will call other functions that will generate the contents of the document based on the title and kind.',
    inputSchema: z.object({
      title: z.string(),
      kind: z.enum(artifactKinds),
      originalQuery: z.string().optional(),
    }),
    execute: async ({ title, kind, originalQuery }) => {
      const id = generateUUID();

      // Clear first to force clients to reset the current artifact view
      dataStream.write({
        type: 'data-clear',
        data: null,
        transient: true,
      });
      // Then stream new document metadata in a stable order
      dataStream.write({
        type: 'data-id',
        data: id,
        transient: true,
      });
      dataStream.write({
        type: 'data-kind',
        data: kind,
        transient: true,
      });
      dataStream.write({
        type: 'data-title',
        data: title,
        transient: true,
      });

      if (session.user?.id) {
        saveGenerationToMemory({
          id,
          title,
          kind,
          prompt: originalQuery ?? title,
          userId: session.user.id,
        });
      }

      const documentHandler = documentHandlersByArtifactKind.find(
        (h) => h.kind === kind,
      );
      if (!documentHandler)
        throw new Error(`No document handler found for kind: ${kind}`);

      const documentContent = await documentHandler.onCreateDocument({
        id,
        title,
        originalQuery,
        dataStream,
        session,
      });

      if (session.user?.id && documentContent) {
        console.log('Saving document content to memory:', {
          id,
          title,
          kind,
          contentLength: documentContent.length,
        });

        saveDocumentContentToMemory({
          id,
          title,
          kind,
          content: documentContent,
          userId: session.user.id,
        });
      }

      dataStream.write({ type: 'data-finish', data: null, transient: true });

      return {
        id,
        title,
        kind,
        content:
          documentContent ||
          'Document created successfully, but content is not yet available.',
      };
    },
  });
