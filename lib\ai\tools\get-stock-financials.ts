import { tool } from 'ai';
import { z } from 'zod';

export const getStockFinancials = tool({
  description: 'Get financial information and key metrics for a given stock ticker symbol',
  inputSchema: z.object({
    ticker: z
      .string()
      .describe('Stock ticker symbol (e.g. "AAPL" for Apple, "MSFT" for Microsoft)'),
  }),
  execute: async ({ ticker }) => {
    // Basic ticker validation
    if (!ticker || typeof ticker !== 'string' || ticker.trim() === '') {
      return { error: 'Please provide a valid stock ticker symbol' };
    }

    // Clean and format the ticker
    const cleanTicker = ticker.trim().toUpperCase();

    // Optional: List of known tickers for validation
    const knownTickers = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'TSLA', 'NVDA', 'JPM', 'V', 'WMT'];
    
    if (!knownTickers.includes(cleanTicker)) {
      console.log(`Warning: The symbol ${cleanTicker} is not in the list of known tickers.`);
    }

    // Return the data needed to display the financials widget
    return {
      ticker: cleanTicker,
      timestamp: new Date().toISOString(),
      widgetType: 'financials',
      // Additional metadata can be added here if needed
    };
  },
});
