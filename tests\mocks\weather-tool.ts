/**
 * Mock Weather Tool
 * 
 * This module provides a mock implementation of the weather tool for testing.
 * It returns predefined weather data based on the location in the query.
 */

/**
 * Interface for weather data
 */
import { entries } from '@/lib/safe-entries';
export interface WeatherData {
  location: string;
  temperature: {
    current: number;
    min: number;
    max: number;
    feelsLike: number;
    unit: 'celsius' | 'fahrenheit';
  };
  conditions: string;
  humidity: number;
  windSpeed: number;
  windDirection: string;
  precipitation: number;
  forecast: Array<{
    day: string;
    conditions: string;
    temperature: {
      min: number;
      max: number;
    };
  }>;
  lastUpdated: string;
}

/**
 * Mock weather data for different locations
 */
const mockWeatherData: Record<string, WeatherData> = {
  'san francisco': {
    location: 'San Francisco, CA',
    temperature: {
      current: 65,
      min: 58,
      max: 70,
      feelsLike: 63,
      unit: 'fahrenheit'
    },
    conditions: 'Sunny',
    humidity: 72,
    windSpeed: 8,
    windDirection: 'West',
    precipitation: 0,
    forecast: [
      { day: 'Today', conditions: 'Sunny', temperature: { min: 58, max: 70 } },
      { day: 'Tomorrow', conditions: 'Partly Cloudy', temperature: { min: 57, max: 68 } },
      { day: 'Wednesday', conditions: 'Foggy Morning', temperature: { min: 56, max: 67 } },
      { day: 'Thursday', conditions: 'Sunny', temperature: { min: 59, max: 72 } },
      { day: 'Friday', conditions: 'Sunny', temperature: { min: 60, max: 73 } }
    ],
    lastUpdated: new Date().toISOString()
  },
  'new york': {
    location: 'New York, NY',
    temperature: {
      current: 72,
      min: 65,
      max: 78,
      feelsLike: 74,
      unit: 'fahrenheit'
    },
    conditions: 'Partly Cloudy',
    humidity: 65,
    windSpeed: 5,
    windDirection: 'South',
    precipitation: 10,
    forecast: [
      { day: 'Today', conditions: 'Partly Cloudy', temperature: { min: 65, max: 78 } },
      { day: 'Tomorrow', conditions: 'Thunderstorms', temperature: { min: 68, max: 80 } },
      { day: 'Wednesday', conditions: 'Rain', temperature: { min: 66, max: 75 } },
      { day: 'Thursday', conditions: 'Partly Cloudy', temperature: { min: 64, max: 76 } },
      { day: 'Friday', conditions: 'Sunny', temperature: { min: 67, max: 79 } }
    ],
    lastUpdated: new Date().toISOString()
  },
  'london': {
    location: 'London, UK',
    temperature: {
      current: 15,
      min: 12,
      max: 18,
      feelsLike: 14,
      unit: 'celsius'
    },
    conditions: 'Rainy',
    humidity: 85,
    windSpeed: 12,
    windDirection: 'Southwest',
    precipitation: 80,
    forecast: [
      { day: 'Today', conditions: 'Rainy', temperature: { min: 12, max: 18 } },
      { day: 'Tomorrow', conditions: 'Overcast', temperature: { min: 11, max: 16 } },
      { day: 'Wednesday', conditions: 'Light Rain', temperature: { min: 10, max: 15 } },
      { day: 'Thursday', conditions: 'Partly Cloudy', temperature: { min: 12, max: 17 } },
      { day: 'Friday', conditions: 'Cloudy', temperature: { min: 13, max: 18 } }
    ],
    lastUpdated: new Date().toISOString()
  },
  'tokyo': {
    location: 'Tokyo, Japan',
    temperature: {
      current: 26,
      min: 22,
      max: 29,
      feelsLike: 28,
      unit: 'celsius'
    },
    conditions: 'Clear',
    humidity: 70,
    windSpeed: 6,
    windDirection: 'East',
    precipitation: 0,
    forecast: [
      { day: 'Today', conditions: 'Clear', temperature: { min: 22, max: 29 } },
      { day: 'Tomorrow', conditions: 'Sunny', temperature: { min: 23, max: 30 } },
      { day: 'Wednesday', conditions: 'Partly Cloudy', temperature: { min: 24, max: 31 } },
      { day: 'Thursday', conditions: 'Rain', temperature: { min: 22, max: 27 } },
      { day: 'Friday', conditions: 'Thunderstorms', temperature: { min: 21, max: 26 } }
    ],
    lastUpdated: new Date().toISOString()
  }
};

/**
 * Default weather data for unknown locations
 */
const defaultWeatherData: WeatherData = {
  location: 'Unknown Location',
  temperature: {
    current: 70,
    min: 65,
    max: 75,
    feelsLike: 70,
    unit: 'fahrenheit'
  },
  conditions: 'Clear',
  humidity: 60,
  windSpeed: 5,
  windDirection: 'North',
  precipitation: 0,
  forecast: [
    { day: 'Today', conditions: 'Clear', temperature: { min: 65, max: 75 } },
    { day: 'Tomorrow', conditions: 'Sunny', temperature: { min: 66, max: 76 } },
    { day: 'Wednesday', conditions: 'Sunny', temperature: { min: 67, max: 77 } },
    { day: 'Thursday', conditions: 'Partly Cloudy', temperature: { min: 65, max: 75 } },
    { day: 'Friday', conditions: 'Sunny', temperature: { min: 68, max: 78 } }
  ],
  lastUpdated: new Date().toISOString()
};

/**
 * Extract location from a weather query
 * @param query The weather query
 * @returns The extracted location
 */
export function extractLocationFromQuery(query: string): string {
  // Common patterns for weather queries
  const patterns = [
    /weather\s+in\s+([a-zA-Z\s]+)/i,
    /weather\s+for\s+([a-zA-Z\s]+)/i,
    /weather\s+at\s+([a-zA-Z\s]+)/i,
    /weather\s+([a-zA-Z\s]+)/i,
    /([a-zA-Z\s]+)\s+weather/i,
    /temperature\s+in\s+([a-zA-Z\s]+)/i,
    /forecast\s+for\s+([a-zA-Z\s]+)/i
  ];

  for (const pattern of patterns) {
    const match = query.match(pattern);
    if (match?.[1]) {
      return match[1].trim().toLowerCase();
    }
  }

  return 'unknown';
}

/**
 * Get weather data for a location
 * @param location The location to get weather data for
 * @returns The weather data for the location
 */
export function getWeatherData(location: string): WeatherData {
  const normalizedLocation = location.toLowerCase();
  
  // Check if we have mock data for this location
  for (const [key, data] of entries(mockWeatherData)) {
    if (normalizedLocation.includes(key)) {
      return data;
    }
  }
  
  // Return default data if no match
  return {
    ...defaultWeatherData,
    location: location || 'Unknown Location'
  };
}

/**
 * Format weather data as a human-readable string
 * @param data The weather data to format
 * @returns A formatted string with the weather information
 */
export function formatWeatherResponse(data: WeatherData): string {
  const tempUnit = data.temperature.unit === 'celsius' ? '°C' : '°F';
  
  let response = `Current weather in ${data.location}:\n\n`;
  response += `Temperature: ${data.temperature.current}${tempUnit} (feels like ${data.temperature.feelsLike}${tempUnit})\n`;
  response += `Conditions: ${data.conditions}\n`;
  response += `Humidity: ${data.humidity}%\n`;
  response += `Wind: ${data.windSpeed} mph from the ${data.windDirection}\n\n`;
  
  response += `Forecast:\n`;
  data.forecast.forEach(day => {
    response += `- ${day.day}: ${day.conditions}, ${day.temperature.min}-${day.temperature.max}${tempUnit}\n`;
  });
  
  response += `\nLast updated: ${new Date(data.lastUpdated).toLocaleString()}`;
  
  return response;
}

/**
 * Mock weather tool function
 * @param query The weather query
 * @returns A promise that resolves to a formatted weather response
 */
export async function mockWeatherTool(query: string): Promise<string> {
  // Add a small delay to simulate network latency
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const location = extractLocationFromQuery(query);
  const weatherData = getWeatherData(location);
  return formatWeatherResponse(weatherData);
}
