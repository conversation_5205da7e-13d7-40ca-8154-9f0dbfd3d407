'use client';

import { useState, useEffect } from 'react';
import type { Place } from '@/lib/types';
import {
  ChevronUp,
  ChevronDown,
  MapPin,
  ToggleLeft,
  ToggleRight,
} from 'lucide-react';

interface LocationElevatorProps {
  places: Place[];
  selectedPlaceId?: string | null;
  onLocationClick?: (place: Place) => void;
  useElevator?: boolean;
  onToggleMode?: (useElevator: boolean) => void;
}

const LocationElevator: React.FC<LocationElevatorProps> = ({
  places,
  selectedPlaceId,
  onLocationClick,
  useElevator = true,
  onToggleMode,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showMore, setShowMore] = useState(false);

  // Sélectionner automatiquement la première location au chargement
  useEffect(() => {
    if (useElevator && places.length > 0 && onLocationClick) {
      onLocationClick(places[0]);
    }
  }, [useElevator, places, onLocationClick]);

  // Synchroniser l'index de l'ascenseur avec la location sélectionnée depuis la carte
  useEffect(() => {
    if (useElevator && selectedPlaceId && places.length > 0) {
      const selectedIndex = places.findIndex(
        (place) => place.cid === selectedPlaceId,
      );
      if (selectedIndex !== -1 && selectedIndex !== currentIndex) {
        setCurrentIndex(selectedIndex);
      }
    }
  }, [selectedPlaceId, places, useElevator, currentIndex]);

  // Mode ascenseur
  if (useElevator && places.length > 1) {
    const currentPlace = places[currentIndex];

    const goUp = () => {
      if (currentIndex > 0) {
        const newIndex = currentIndex - 1;
        setCurrentIndex(newIndex);
        onLocationClick?.(places[newIndex]);
      }
    };

    const goDown = () => {
      if (currentIndex < places.length - 1) {
        const newIndex = currentIndex + 1;
        setCurrentIndex(newIndex);
        onLocationClick?.(places[newIndex]);
      }
    };

    const goToIndex = (index: number) => {
      setCurrentIndex(index);
      onLocationClick?.(places[index]);
    };

    return (
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-2 sm:p-3 mt-4 max-w-full overflow-hidden">
        {/* Header compact avec toggle - responsive */}
        <div className="flex items-center justify-between mb-2 sm:mb-3">
          <h2 className="text-xs sm:text-sm font-semibold text-black dark:text-white truncate">
            Navigation ({currentIndex + 1}/{places.length})
          </h2>
          {onToggleMode && (
            <button
              type="button"
              onClick={() => onToggleMode(false)}
              className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors flex-shrink-0"
              title="Passer au mode liste"
            >
              <ToggleRight className="w-3 h-3 sm:w-4 sm:h-4 text-blue-500" />
              <span className="hidden xs:inline">Liste</span>
            </button>
          )}
        </div>

        {/* Layout adaptatif selon la largeur d'écran */}
        <div className="flex flex-col xs:flex-row items-start xs:items-center gap-2 xs:gap-3">
          {/* Contrôles - horizontaux sur très petits écrans, verticaux sur écrans plus larges */}
          <div className="flex xs:flex-col items-center gap-1 w-full xs:w-auto justify-center xs:justify-start">
            <button
              type="button"
              onClick={goUp}
              disabled={currentIndex === 0}
              className="p-1.5 sm:p-2 rounded-md bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              aria-label="Location précédente"
            >
              <ChevronUp className="w-3 h-3 sm:w-4 sm:h-4 text-gray-700 dark:text-gray-200" />
            </button>

            <div className="flex xs:flex-col items-center py-1 px-2 xs:px-0">
              <div className="h-1 w-6 xs:w-1 xs:h-6 sm:h-8 bg-gray-200 dark:bg-gray-600 rounded-full relative overflow-hidden">
                <div
                  className="bg-blue-500 rounded-full transition-all duration-300 ease-out h-1 xs:w-1 xs:h-auto"
                  style={{
                    width: `${((currentIndex + 1) / places.length) * 100}%`,
                    height: `${((currentIndex + 1) / places.length) * 100}%`,
                  }}
                />
              </div>
            </div>

            <button
              type="button"
              onClick={goDown}
              disabled={currentIndex === places.length - 1}
              className="p-1.5 sm:p-2 rounded-md bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              aria-label="Location suivante"
            >
              <ChevronDown className="w-3 h-3 sm:w-4 sm:h-4 text-gray-700 dark:text-gray-200" />
            </button>
          </div>

          {/* Informations de la location courante - responsive */}
          <div className="flex-1 min-w-0 w-full xs:w-auto">
            <div className="flex items-start gap-1.5 sm:gap-2">
              <MapPin className="w-3 h-3 sm:w-4 sm:h-4 text-blue-500 mt-0.5 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-sm sm:text-base text-black dark:text-white mb-1 leading-tight line-clamp-2">
                  {currentPlace.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-xs mb-1.5 sm:mb-2 leading-relaxed overflow-hidden line-clamp-2">
                  {currentPlace.address}
                </p>

                {/* Rating et category - stack sur très petits écrans */}
                <div className="flex flex-col xs:flex-row xs:items-center gap-1.5 xs:gap-3 mb-1.5 sm:mb-2">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, index) => (
                      <svg
                        key={`star-${currentPlace.cid}-${index}`}
                        className={`w-2.5 h-2.5 sm:w-3 sm:h-3 ${
                          index < Math.floor(currentPlace.rating)
                            ? 'text-yellow-400'
                            : 'text-gray-300 dark:text-gray-500'
                        }`}
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 15.585l-5.293 2.776.1-5.867L.416 8.222l5.875-.855L10 2.415l3.709 4.952 5.875.855-4.391 4.272.1 5.867L10 15.585z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ))}
                    <span className="ml-1 text-xs text-gray-700 dark:text-gray-300 font-medium">
                      {currentPlace.rating.toFixed(1)}
                    </span>
                  </div>
                  <span className="inline-block px-1.5 sm:px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs rounded-full font-medium truncate max-w-[120px] sm:max-w-none">
                    {currentPlace.category}
                  </span>
                </div>

                {/* Contact info compact - responsive */}
                <div className="flex flex-wrap gap-1">
                  {currentPlace.phoneNumber && (
                    <a
                      href={`tel:${currentPlace.phoneNumber}`}
                      className="inline-flex items-center text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 text-xs font-medium py-0.5 sm:py-1 px-1.5 sm:px-2 rounded bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                    >
                      <span className="mr-0.5 sm:mr-1">📞</span>
                      <span className="hidden xs:inline">Appeler</span>
                      <span className="xs:hidden">Tel</span>
                    </a>
                  )}
                  {currentPlace.website && (
                    <a
                      href={currentPlace.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 text-xs font-medium py-0.5 sm:py-1 px-1.5 sm:px-2 rounded bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                    >
                      <span className="mr-0.5 sm:mr-1">🌐</span>
                      <span className="hidden xs:inline">Site</span>
                      <span className="xs:hidden">Web</span>
                    </a>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Navigation rapide par points - responsive */}
          <div className="flex gap-0.5 sm:gap-1 justify-center xs:justify-start w-full xs:w-auto flex-wrap">
            {places.slice(0, 8).map((place, idx) => (
              <button
                key={`nav-dot-${place.cid}`}
                type="button"
                onClick={() => goToIndex(idx)}
                className={`w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full transition-all duration-200 ${
                  idx === currentIndex
                    ? 'bg-blue-500 scale-125'
                    : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                }`}
                aria-label={`Aller à ${place.title}`}
                title={place.title}
              />
            ))}
            {/* Show remaining dots on larger screens */}
            <div className="hidden xs:flex gap-0.5 sm:gap-1 flex-wrap">
              {places.slice(8).map((place, idx) => (
                <button
                  key={`nav-dot-${place.cid}`}
                  type="button"
                  onClick={() => goToIndex(idx + 8)}
                  className={`w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full transition-all duration-200 ${
                    idx + 8 === currentIndex
                      ? 'bg-blue-500 scale-125'
                      : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                  }`}
                  aria-label={`Aller à ${place.title}`}
                  title={place.title}
                />
              ))}
            </div>
            {places.length > 8 && (
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-1 xs:hidden">
                +{places.length - 8}
              </span>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Mode liste classique (code existant de LocationSidebar)
  const displayPlaces = places.slice(0, showMore ? places.length : 3);

  return (
    <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-2 sm:p-3 lg:p-4 mt-4 max-w-full overflow-hidden">
      {/* Header avec toggle - responsive */}
      <div className="flex items-center justify-between mb-3 sm:mb-4">
        <h2 className="text-sm sm:text-base lg:text-lg font-semibold text-black dark:text-white truncate">
          Location Details
        </h2>
        <div className="flex items-center gap-1 sm:gap-2 lg:gap-3 flex-shrink-0">
          {places.length > 1 && onToggleMode && (
            <button
              type="button"
              onClick={() => onToggleMode(true)}
              className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              title="Passer au mode ascenseur"
            >
              <ToggleLeft className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
            </button>
          )}
          {places.length > 3 && (
            <button
              type="button"
              className="text-black dark:text-white focus:outline-none p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
              onClick={() => setShowMore(!showMore)}
              aria-label={
                showMore ? 'Show less locations' : 'Show more locations'
              }
            >
              {showMore ? '−' : '+'}
            </button>
          )}
        </div>
      </div>

      {/* Liste des locations - responsive */}
      <div
        className={`space-y-2 sm:space-y-3 lg:space-y-4 transition-all duration-500 ${showMore ? 'max-h-[5000px]' : 'max-h-[250px] sm:max-h-[300px]'} overflow-hidden`}
      >
        {displayPlaces.map((place: Place) => {
          const isSelected = selectedPlaceId === place.cid;

          return (
            <div
              key={place.cid}
              className={`
                rounded-lg p-2 sm:p-3 lg:p-4 cursor-pointer transition-all duration-200 max-w-full overflow-hidden
                ${
                  isSelected
                    ? 'bg-blue-100 dark:bg-blue-900/30 border-2 border-blue-500 dark:border-blue-400 shadow-md'
                    : 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 border-2 border-transparent'
                }
              `}
              onClick={() => onLocationClick?.(place)}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  onLocationClick?.(place);
                }
              }}
              aria-label={`View ${place.title} on map`}
            >
              <h3 className="text-sm sm:text-base lg:text-lg font-semibold mb-1 sm:mb-2 text-black dark:text-white leading-tight line-clamp-2">
                {place.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-xs sm:text-sm mb-1 sm:mb-2 leading-relaxed line-clamp-2">
                {place.address}
              </p>
              <div className="flex flex-col xs:flex-row xs:items-center gap-1 xs:gap-2 mb-1 sm:mb-2">
                <div className="flex items-center">
                  <span className="text-gray-600 dark:text-gray-400 text-xs sm:text-sm mr-1">
                    Rating:
                  </span>
                  <div className="flex items-center">
                    {[...Array(5)].map((_, index) => (
                      <svg
                        key={`star-${place.cid}-${index}`}
                        className={`w-2.5 h-2.5 sm:w-3 sm:h-3 lg:w-4 lg:h-4 ${
                          index < Math.floor(place.rating)
                            ? 'text-yellow-400'
                            : 'text-gray-300 dark:text-gray-500'
                        }`}
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 15.585l-5.293 2.776.1-5.867L.416 8.222l5.875-.855L10 2.415l3.709 4.952 5.875.855-4.391 4.272.1 5.867L10 15.585z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ))}
                    <span className="ml-1 text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                      {place.rating.toFixed(1)}
                    </span>
                  </div>
                </div>
                <span className="text-gray-600 dark:text-gray-400 text-xs sm:text-sm truncate">
                  Category: {place.category}
                </span>
              </div>

              {/* Contact info - responsive layout */}
              <div className="flex flex-wrap gap-1 sm:gap-2">
                {place.phoneNumber && (
                  <a
                    href={`tel:${place.phoneNumber}`}
                    className="inline-flex items-center text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 text-xs sm:text-sm font-medium py-0.5 sm:py-1 px-1.5 sm:px-2 rounded bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                  >
                    <span className="mr-0.5 sm:mr-1">📞</span>
                    <span className="hidden sm:inline">
                      {place.phoneNumber}
                    </span>
                    <span className="sm:hidden">Tel</span>
                  </a>
                )}
                {place.website && (
                  <a
                    href={place.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 text-xs sm:text-sm font-medium py-0.5 sm:py-1 px-1.5 sm:px-2 rounded bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                  >
                    <span className="mr-0.5 sm:mr-1">🌐</span>
                    <span className="hidden sm:inline truncate max-w-[120px] lg:max-w-[200px]">
                      Visit Website
                    </span>
                    <span className="sm:hidden">Web</span>
                  </a>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default LocationElevator;
