import { tool } from 'ai';
import { z } from 'zod';

export const imageSearchTool = tool({
  description: `Search for images on the web using Tavily Image Search API. 
  Use this tool to find relevant images that illustrate or support your written content.
  Create specific, detailed search queries for better results.
  Examples: "French macaron pastries close-up", "Eiffel Tower Paris sunset", "quantum computer laboratory"`,
  inputSchema: z.object({
    query: z
      .string()
      .describe(
        'The search query for finding images. Be specific and descriptive. Include relevant adjectives and context.',
      ),
    maxResults: z
      .number()
      .optional()
      .describe('Maximum number of images to return (default: 5, max: 10)'),
  }),
  execute: async ({ query, maxResults }) => {
    try {
      const apiKey = process.env.TAVILY_API_KEY;

      if (!apiKey) {
        console.error('TAVILY_API_KEY is not configured');
        return {
          success: false,
          error: 'Image search is not configured',
          images: [],
        };
      }

      // Limit maxResults to 10
      const limitedMaxResults = Math.min(maxResults || 5, 10);

      const response = await fetch('https://api.tavily.com/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          api_key: apiKey,
          query: query,
          search_depth: 'basic',
          include_images: true,
          include_answer: false,
          max_results: limitedMaxResults,
        }),
      });

      if (!response.ok) {
        throw new Error(`Tavily API error: ${response.statusText}`);
      }

      const data = await response.json();

      // Extract images from the response
      const images = data.images || [];

      return {
        success: true,
        query: query,
        images: images.slice(0, limitedMaxResults).map((url: string) => ({
          url,
          alt: query,
        })),
        count: images.length,
      };
    } catch (error) {
      console.error('Image search error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        images: [],
      };
    }
  },
});
