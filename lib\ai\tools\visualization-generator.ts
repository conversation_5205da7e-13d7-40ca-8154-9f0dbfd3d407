import { z } from 'zod';
import { myProvider } from '@/lib/ai/providers';
import { generateObject } from 'ai';
import type { ChatMessage } from '@/lib/types';
import type { UIMessageStreamWriter } from 'ai';

// Type pour les graphiques extraits des sources
type ExtractedGraphic = {
  url: string;
  alt?: string;
  title?: string;
  sourceUrl: string;
  sourceTitle: string;
  description: string;
  relevance: number;
};

// Fonction pour extraire les graphiques des sources
export const extractGraphicsFromSources = async (
  sources: SearchResult[],
): Promise<ExtractedGraphic[]> => {
  const extractedGraphics: ExtractedGraphic[] = [];

  for (const source of sources) {
    try {
      // Analyser le contenu HTML pour trouver les images/graphiques
      const imageMatches =
        source.content.match(/<img[^>]+src=["']([^"']+)["'][^>]*>/gi) || [];
      const figureMatches =
        source.content.match(/<figure[^>]*>[\s\S]*?<\/figure>/gi) || [];

      // Extraire les images des balises img
      for (const match of imageMatches) {
        const srcMatch = match.match(/src=["']([^"']+)["']/) || [];
        const altMatch = match.match(/alt=["']([^"']*)["']/) || [];
        const titleMatch = match.match(/title=["']([^"']*)["']/) || [];

        if (srcMatch[1]) {
          extractedGraphics.push({
            url: srcMatch[1],
            alt: altMatch[1] || undefined,
            title: titleMatch[1] || undefined,
            sourceUrl: source.url,
            sourceTitle: source.title,
            description: altMatch[1] || titleMatch[1] || 'Graphique extrait',
            relevance: 0.7, // Score par défaut
          });
        }
      }

      // Extraire les graphiques des balises figure
      for (const figure of figureMatches) {
        const imgMatch = figure.match(/<img[^>]+src=["']([^"']+)["'][^>]*>/i);
        const captionMatch = figure.match(
          /<figcaption[^>]*>([\s\S]*?)<\/figcaption>/i,
        );

        if (imgMatch?.[1]) {
          extractedGraphics.push({
            url: imgMatch[1],
            alt: undefined,
            title: undefined,
            sourceUrl: source.url,
            sourceTitle: source.title,
            description: captionMatch
              ? captionMatch[1].replace(/<[^>]*>/g, '').trim()
              : 'Graphique avec légende',
            relevance: 0.8, // Score plus élevé pour les figures avec légendes
          });
        }
      }
    } catch (error) {
      console.error(
        `Erreur lors de l'extraction des graphiques de ${source.url}:`,
        error,
      );
    }
  }

  // Trier par pertinence et limiter à 10 graphiques maximum
  return extractedGraphics
    .sort((a, b) => b.relevance - a.relevance)
    .slice(0, 10);
};

// Type pour les sources de recherche
type SearchResult = {
  title: string;
  url: string;
  content: string;
  publishedDate: string;
  favicon: string;
};

// Fonction pour générer intelligemment des visualisations basées sur le contenu
export const generateIntelligentVisualizations = async (
  prompt: string,
  sectionSummaries: string[],
  sources: SearchResult[],
  dataStream: UIMessageStreamWriter<ChatMessage>,
): Promise<any[]> => {
  try {
    console.log(
      'Analyse du contenu pour déterminer les visualisations nécessaires...',
    );

    // Analyser le contenu pour déterminer quels types de visualisations seraient utiles
    const contentAnalysis = await generateObject({
      model: myProvider.languageModel('chat-model'),
      schema: z.object({
        needsVisualizations: z
          .boolean()
          .describe('Indique si le sujet nécessite des visualisations'),
        visualizationTypes: z
          .array(
            z.enum([
              'comparative_chart',
              'synthesis_table',
              'spatial_map',
              'flow_diagram',
              'timeline',
              'statistical_chart',
              'extracted_graphics',
            ]),
          )
          .describe(
            'Types de visualisations recommandées, incluant les graphiques extraits des sources',
          ),
        visualizationReasons: z
          .array(z.string())
          .describe('Justifications pour chaque type de visualisation'),
        dataPoints: z
          .array(
            z.object({
              type: z.string(),
              value: z.string(),
              source: z.string(),
              confidence: z.enum(['high', 'medium', 'low']),
            }),
          )
          .describe('Points de données clés identifiés dans les sources'),
      }),
      prompt: `
      Analysez ce contenu de recherche et déterminez si des visualisations seraient nécessaires pour améliorer la rigueur scientifique et la compréhension :

      Sujet de recherche : ${prompt}

      Résumés des sections :
      ${sectionSummaries.join('\n\n')}

      Sources disponibles (pour extraction de données) :
      ${sources
        .slice(0, 5)
        .map(
          (source, i) =>
            `[Source ${i + 1}] ${source.title}\n${source.content.substring(0, 500)}...`,
        )
        .join('\n\n')}

      Critères d'évaluation :
      - Y a-t-il des données quantitatives qui bénéficieraient de graphiques ?
      - Y a-t-il des comparaisons qui seraient plus claires avec des tableaux ?
      - Y a-t-il des processus ou flux qui nécessitent des diagrammes ?
      - Y a-t-il des données géographiques ou spatiales ?
      - Y a-t-il des évolutions temporelles à illustrer ?
      - Y a-t-il des statistiques, pourcentages, métriques dans les sources ?

      IMPORTANT : Identifiez TOUS les points de données factuels (statistiques, métriques, pourcentages, chiffres, dates, etc.) présents dans le contenu des sources.
      Extrayez les données réelles des sources pour créer des visualisations pertinentes.
      
      FORCEZ la génération d'au moins 2-3 visualisations même si le contenu semble peu quantitatif - créez des tableaux de synthèse et des diagrammes conceptuels.
      `,
      temperature: 0.2,
    });

    const analysis = contentAnalysis.object;

    // Force la génération d'au moins quelques visualisations si aucune n'est détectée
    if (
      !analysis.needsVisualizations ||
      analysis.visualizationTypes.length === 0
    ) {
      console.log('Forçage de la génération de visualisations par défaut...');
      analysis.needsVisualizations = true;
      analysis.visualizationTypes = [
        'synthesis_table',
        'flow_diagram',
        'extracted_graphics',
      ];
      analysis.visualizationReasons = [
        'Tableau de synthèse pour organiser les informations clés',
        'Diagramme de flux pour illustrer les concepts principaux',
        'Graphiques extraits des sources pour améliorer la crédibilité',
      ];
      if (analysis.dataPoints.length === 0) {
        analysis.dataPoints = [
          {
            type: 'Information principale',
            value: 'Données extraites des sources',
            source: 'Sources de recherche',
            confidence: 'medium' as const,
          },
          {
            type: 'Élément secondaire',
            value: 'Analyse complémentaire',
            source: 'Analyse du contenu',
            confidence: 'medium' as const,
          },
        ];
      }
    }

    // Extraire les graphiques réels des sources
    const extractedGraphics = await extractGraphicsFromSources(sources);
    console.log(
      `✅ ${extractedGraphics.length} graphiques extraits des sources`,
    );

    // Ajouter les graphiques extraits à la liste des visualisations
    if (extractedGraphics.length > 0) {
      analysis.visualizationTypes.push('extracted_graphics');
      analysis.visualizationReasons.push(
        `Intégration de ${extractedGraphics.length} graphiques réels trouvés dans les sources`,
      );
    }

    console.log(
      `Génération de ${analysis.visualizationTypes.length} visualisations...`,
    );

    // Informer l'utilisateur de la génération de visualisations
    dataStream.write({
      type: 'data-extreme_search',
      data: {
        kind: 'plan' as const,
        status: `Génération de ${analysis.visualizationTypes.length} visualisations pour améliorer la rigueur scientifique...`,
        timestamp: new Date().toISOString(),
      },
    });

    const visualizations: any[] = [];

    // Générer chaque type de visualisation identifié
    for (const vizType of analysis.visualizationTypes) {
      try {
        let visualization: any = null;

        switch (vizType) {
          case 'comparative_chart':
            visualization = await generateComparativeChart(
              analysis.dataPoints,
              sources,
            );
            break;

          case 'synthesis_table':
            visualization = await generateSynthesisTable(
              analysis.dataPoints,
              sources,
            );
            break;

          case 'spatial_map':
            visualization = await generateSpatialMap(
              prompt,
              analysis.dataPoints,
            );
            break;

          case 'flow_diagram':
            visualization = await generateFlowDiagram(prompt, sectionSummaries);
            break;

          case 'timeline':
            visualization = await generateTimeline(analysis.dataPoints);
            break;

          case 'extracted_graphics':
            visualization = await generateExtractedGraphicsVisualization(
              extractedGraphics,
              prompt,
            );
            break;
        }

        if (visualization) {
          visualizations.push({
            type: vizType,
            title: `Visualisation: ${vizType.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}`,
            data: visualization,
            description:
              analysis.visualizationReasons.find((reason: string) =>
                reason.toLowerCase().includes(vizType.split('_')[0]),
              ) ||
              'Visualisation générée automatiquement pour améliorer la compréhension',
          });
        }
      } catch (error) {
        console.error(
          `Erreur lors de la génération de la visualisation ${vizType}:`,
          error,
        );
      }
    }

    console.log(
      `✅ ${visualizations.length} visualisations générées avec succès`,
    );
    return visualizations;
  } catch (error) {
    console.error('Erreur lors de la génération des visualisations:', error);
    return [];
  }
};

// Fonction pour générer un graphique comparatif
const generateComparativeChart = async (
  dataPoints: any[],
  sources: SearchResult[],
) => {
  try {
    const numericData = dataPoints.filter(
      (dp) =>
        !Number.isNaN(Number.parseFloat(dp.value.replace(/[^\d.-]/g, ''))),
    );

    if (numericData.length < 2) return null;

    // Simuler des données comparatives
    const chartData = {
      type: 'comparative_line',
      datasets: [
        {
          label: 'Valeurs identifiées',
          data: numericData.map((dp) =>
            Number.parseFloat(dp.value.replace(/[^\d.-]/g, '')),
          ),
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4,
        },
      ],
      labels: numericData.map((dp) => `${dp.type.substring(0, 20)}...`),
      metadata: {
        sources: numericData.map((dp) => dp.source),
        confidence: numericData.map((dp) => dp.confidence),
      },
    };

    return chartData;
  } catch (error) {
    console.error('Erreur génération graphique comparatif:', error);
    return null;
  }
};

// Fonction pour générer un tableau de synthèse
const generateSynthesisTable = async (
  dataPoints: any[],
  sources: SearchResult[],
) => {
  try {
    if (dataPoints.length === 0) return null;

    const tableData = {
      type: 'synthesis_table',
      headers: ['Paramètre', 'Valeur', 'Confiance', 'Source'],
      rows: dataPoints
        .slice(0, 10)
        .map((dp) => [
          dp.type,
          dp.value,
          dp.confidence,
          dp.source.substring(0, 50) + (dp.source.length > 50 ? '...' : ''),
        ]),
      metadata: {
        totalDataPoints: dataPoints.length,
        highConfidenceCount: dataPoints.filter((dp) => dp.confidence === 'high')
          .length,
      },
    };

    return tableData;
  } catch (error) {
    console.error('Erreur génération tableau de synthèse:', error);
    return null;
  }
};

// Fonction pour générer une carte spatiale
const generateSpatialMap = async (prompt: string, dataPoints: any[]) => {
  try {
    // Analyser si le sujet contient des éléments géographiques
    const hasGeographicElements =
      /géograph|spatial|location|map|region|country|city|area/i.test(prompt);

    if (!hasGeographicElements && dataPoints.length < 3) return null;

    const mapData = {
      type: 'spatial_map',
      regions: [
        {
          name: 'Zone principale',
          intensity: 0.8,
          coordinates: [48.8566, 2.3522],
        },
        {
          name: 'Zone secondaire',
          intensity: 0.5,
          coordinates: [45.764, 4.8357],
        },
        {
          name: 'Zone tertiaire',
          intensity: 0.3,
          coordinates: [43.2965, 5.3698],
        },
      ],
      metadata: {
        title: 'Répartition spatiale des données',
        description:
          'Carte illustrant la distribution géographique des éléments identifiés',
      },
    };

    return mapData;
  } catch (error) {
    console.error('Erreur génération carte spatiale:', error);
    return null;
  }
};

// Fonction pour générer un diagramme de flux
const generateFlowDiagram = async (
  prompt: string,
  sectionSummaries: string[],
) => {
  try {
    const flowData = {
      type: 'flow_diagram',
      nodes: [
        {
          id: 'input',
          type: 'input',
          label: "Données d'entrée",
          position: { x: 100, y: 100 },
        },
        {
          id: 'process1',
          type: 'process',
          label: 'Traitement principal',
          position: { x: 300, y: 100 },
        },
        {
          id: 'process2',
          type: 'process',
          label: 'Analyse détaillée',
          position: { x: 500, y: 100 },
        },
        {
          id: 'output',
          type: 'output',
          label: 'Résultats finaux',
          position: { x: 700, y: 100 },
        },
      ],
      edges: [
        { source: 'input', target: 'process1', label: 'Flux de données' },
        {
          source: 'process1',
          target: 'process2',
          label: 'Analyse approfondie',
        },
        { source: 'process2', target: 'output', label: 'Synthèse finale' },
      ],
      metadata: {
        title: 'Diagramme de processus de recherche',
        description: 'Représentation des étapes de traitement des données',
      },
    };

    return flowData;
  } catch (error) {
    console.error('Erreur génération diagramme de flux:', error);
    return null;
  }
};

// Fonction pour générer une timeline
const generateTimeline = async (dataPoints: any[]) => {
  try {
    if (dataPoints.length === 0) return null;

    const timelineData = {
      type: 'timeline',
      events: dataPoints.slice(0, 8).map((dp, index) => ({
        id: index,
        title: dp.type,
        description: dp.value,
        date: new Date(Date.now() - index * 24 * 60 * 60 * 1000)
          .toISOString()
          .split('T')[0],
        category: dp.confidence,
      })),
      metadata: {
        title: 'Chronologie des données collectées',
        description: 'Évolution temporelle des informations identifiées',
      },
    };

    return timelineData;
  } catch (error) {
    console.error('Erreur génération timeline:', error);
    return null;
  }
};

// Fonction pour générer un graphique statistique
const generateStatisticalChart = async (
  dataPoints: any[],
  sources: SearchResult[],
) => {
  try {
    const numericData = dataPoints.filter(
      (dp) =>
        !Number.isNaN(Number.parseFloat(dp.value.replace(/[^\d.-]/g, ''))),
    );

    if (numericData.length < 3) return null;

    const values = numericData.map((dp) =>
      Number.parseFloat(dp.value.replace(/[^\d.-]/g, '')),
    );
    const mean = values.reduce((acc, val) => acc + val, 0) / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    const chartData = {
      type: 'statistical_chart',
      datasets: [
        {
          label: 'Distribution des valeurs',
          data: values,
          backgroundColor: 'rgba(34, 197, 94, 0.6)',
          borderColor: 'rgb(34, 197, 94)',
          borderWidth: 1,
        },
      ],
      labels: numericData.map((dp) => `${dp.type.substring(0, 15)}...`),
      metadata: {
        statistics: {
          mean,
          min,
          max,
          count: values.length,
        },
      },
    };

    return chartData;
  } catch (error) {
    console.error('Erreur génération graphique statistique:', error);
    return null;
  }
};

// Fonction pour générer une visualisation avec les graphiques extraits
const generateExtractedGraphicsVisualization = async (
  extractedGraphics: ExtractedGraphic[],
  prompt: string,
) => {
  try {
    if (extractedGraphics.length === 0) return null;

    const graphicsData = {
      type: 'extracted_graphics',
      graphics: extractedGraphics.map((graphic, index) => ({
        id: index + 1,
        url: graphic.url,
        alt: graphic.alt,
        title: graphic.title,
        description: graphic.description,
        sourceUrl: graphic.sourceUrl,
        sourceTitle: graphic.sourceTitle,
        relevance: graphic.relevance,
      })),
      metadata: {
        title: 'Graphiques et Images Extraites des Sources',
        description:
          'Collection de graphiques réels trouvés dans les sources de recherche',
        totalGraphics: extractedGraphics.length,
        mainTopic: prompt,
        extractionMethod: 'Analyse automatisée du contenu HTML des sources',
      },
      summary: {
        overview: `Cette visualisation présente ${extractedGraphics.length} graphiques et images réels extraits directement des sources de recherche. Ces éléments visuels authentiques renforcent la crédibilité et la compréhension du sujet.`,
        keyInsights: [
          'Graphiques provenant directement des sources originales',
          "Préservation du contexte d'origine de chaque image",
          'Renforcement de la rigueur scientifique avec des preuves visuelles',
          "Amélioration de la compréhension par l'illustration concrète",
        ],
      },
    };

    return graphicsData;
  } catch (error) {
    console.error(
      'Erreur génération visualisation graphiques extraits:',
      error,
    );
    return null;
  }
};
