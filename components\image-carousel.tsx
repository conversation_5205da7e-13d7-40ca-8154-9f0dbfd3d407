'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { Button } from './ui/button';

interface ImageType {
  url: string;
  description?: string;
}

interface ImageCarouselProps {
  images: (ImageType | string)[];
  title?: string;
  maxHeight?: number;
}

export function ImageCarousel({
  images,
  title,
  maxHeight = 300,
}: ImageCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovering, setIsHovering] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Convertir toutes les images en format uniforme
  const normalizedImages: ImageType[] = images.map((img) =>
    typeof img === 'string' ? { url: img } : img,
  );

  const totalImages = normalizedImages.length;
  const hasSingleImage = totalImages <= 1;

  const nextSlide = useCallback(() => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % totalImages);
  }, [totalImages]);

  const prevSlide = useCallback(() => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + totalImages) % totalImages);
  }, [totalImages]);

  // Gérer les touches fléchées pour la navigation
  useEffect(() => {
    // Skip event listeners if there's only one image
    if (hasSingleImage) {
      return;
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      // Utiliser les touches fléchées pour naviguer dans le carrousel
      if (e.key === 'ArrowRight') {
        nextSlide();
      } else if (e.key === 'ArrowLeft') {
        prevSlide();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [totalImages, nextSlide, prevSlide, hasSingleImage]);

  // Pas de carrousel nécessaire pour une seule image
  if (hasSingleImage) {
    const image = normalizedImages[0];
    return (
      <div className="relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 mb-4">
        <div className="aspect-video w-full relative" style={{ maxHeight }}>
          <Image
            src={image.url}
            alt={image.description || 'Image'}
            fill
            className="object-contain"
            onError={(e) => {
              const imgElement = e.target as HTMLImageElement;

              // Utiliser un attribut data pour suivre les tentatives
              const attempts = imgElement.getAttribute('data-attempts') || '0';
              const attemptCount = Number.parseInt(attempts, 10);

              // Limiter à une seule tentative avec le proxy
              if (
                !imgElement.src.includes('/api/proxy-image') &&
                attemptCount < 1
              ) {
                imgElement.setAttribute('data-attempts', '1');

                // Nettoyer l'URL si elle contient déjà des caractères encodés
                let urlToProxy = image.url;
                if (urlToProxy.includes('%')) {
                  try {
                    urlToProxy = decodeURIComponent(urlToProxy);
                  } catch (e) {
                    // Ignorer les erreurs de décodage
                  }
                }

                imgElement.src = `/api/proxy-image?url=${encodeURIComponent(urlToProxy)}`;
              } else {
                imgElement.src = '/images/placeholder-article.jpg';
                imgElement.onerror = null;
              }
            }}
          />
        </div>
        {image.description && (
          <div className="p-2 text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            {image.description}
          </div>
        )}
      </div>
    );
  }

  const currentImage = normalizedImages[currentIndex];

  return (
    <div
      ref={containerRef}
      className="relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 mb-4"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      role="region"
      aria-label="Carrousel d'images"
    >
      {title && (
        <div className="p-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <h4 className="text-sm font-medium">{title}</h4>
        </div>
      )}

      <div className="aspect-video w-full relative" style={{ maxHeight }}>
        <Image
          src={currentImage.url}
          alt={
            currentImage.description ||
            `Image ${currentIndex + 1} sur ${totalImages}`
          }
          fill
          className="object-contain"
          onError={(e) => {
            const imgElement = e.target as HTMLImageElement;

            // Utiliser un attribut data pour suivre les tentatives
            const attempts = imgElement.getAttribute('data-attempts') || '0';
            const attemptCount = Number.parseInt(attempts, 10);

            // Limiter à une seule tentative avec le proxy
            if (
              !imgElement.src.includes('/api/proxy-image') &&
              attemptCount < 1
            ) {
              imgElement.setAttribute('data-attempts', '1');

              // Nettoyer l'URL si elle contient déjà des caractères encodés
              let urlToProxy = currentImage.url;
              if (urlToProxy.includes('%')) {
                try {
                  urlToProxy = decodeURIComponent(urlToProxy);
                } catch (e) {
                  // Ignorer les erreurs de décodage
                }
              }

              imgElement.src = `/api/proxy-image?url=${encodeURIComponent(urlToProxy)}`;
            } else {
              imgElement.src = '/images/placeholder-article.jpg';
              imgElement.onerror = null;
            }
          }}
        />

        {/* Indicateur de position */}
        <div className="absolute bottom-2 inset-x-0 flex justify-center">
          <div className="bg-black/50 text-white text-xs px-2 py-1 rounded-full">
            {currentIndex + 1} / {totalImages}
          </div>
        </div>
      </div>

      {/* Description de l'image */}
      {currentImage.description && (
        <div className="p-2 text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          {currentImage.description}
        </div>
      )}

      {/* Boutons de navigation */}
      <Button
        onClick={prevSlide}
        className={`absolute left-2 top-1/2 -translate-y-1/2 rounded-full p-1 bg-black/50 hover:bg-black/70 transition-opacity ${
          isHovering ? 'opacity-100' : 'opacity-0'
        }`}
        size="icon"
        aria-label="Image précédente"
      >
        &#10094;
      </Button>

      <Button
        onClick={nextSlide}
        className={`absolute right-2 top-1/2 -translate-y-1/2 rounded-full p-1 bg-black/50 hover:bg-black/70 transition-opacity ${
          isHovering ? 'opacity-100' : 'opacity-0'
        }`}
        size="icon"
        aria-label="Image suivante"
      >
        &#10095;
      </Button>
    </div>
  );
}
