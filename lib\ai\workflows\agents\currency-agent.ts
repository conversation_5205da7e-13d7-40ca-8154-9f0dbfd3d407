import { z } from 'zod';
import {
  generateObject,
  type LanguageModel,
  type UIMessageStreamWriter,
} from 'ai';
import type { DestinationInfo, CurrencyInfo } from '../types';
import type { ChatMessage } from '@/lib/types';
import { web_search } from '@/lib/ai/tools/web-search';

/**
 * CurrencyAgent is responsible for detecting and providing currency information
 * for travel destinations using LLM inference.
 */
export class CurrencyAgent {
  private model: LanguageModel;
  private cache: Map<string, CurrencyInfo>;

  constructor(model: LanguageModel) {
    this.model = model;
    this.cache = new Map();
  }

  /**
   * Get currency information for a destination
   */
  async getCurrencyForDestination(
    destinationInfo: DestinationInfo,
    dataStream?: UIMessageStreamWriter<ChatMessage>,
  ): Promise<CurrencyInfo> {
    console.log('🔍 [CurrencyAgent] Getting currency for destination:', {
      destination: destinationInfo.destination,
      country: destinationInfo.country,
    });

    // Stream progress update
    if (dataStream) {
      console.log(
        '🔄 [CurrencyAgent] Streaming: Starting currency search for',
        destinationInfo.destination,
      );
      // For now, we'll use console logs. In a full implementation,
      // this would stream progress updates to the UI
    }

    const cacheKey = `${destinationInfo.destination.toLowerCase()}_${destinationInfo.country?.toLowerCase() || ''}`;

    // Check cache first
    const cachedCurrency = this.cache.get(cacheKey);
    if (cachedCurrency) {
      console.log('💾 [CurrencyAgent] Using cached currency:', cachedCurrency);

      // Stream cache hit notification
      if (dataStream) {
        console.log(
          '🔄 [CurrencyAgent] Streaming: Cache hit for currency data',
        );
      }

      return cachedCurrency;
    }

    try {
      // Stream web search start
      if (dataStream) {
        console.log(
          '🔄 [CurrencyAgent] Streaming: Starting web search for currency info',
        );
      }

      // First, get comprehensive financial information using enhanced web search
      const webSearchResults = await this.searchComprehensiveFinancialInfo(
        destinationInfo,
        dataStream,
      );

      // Stream image search start
      if (dataStream) {
        console.log(
          '🔄 [CurrencyAgent] Streaming: Starting image search for currency',
        );
      }

      // Search for images of currency denominations
      const currencyImages = await this.searchCurrencyImages(destinationInfo);

      // Get current date for the prompt
      const currentDate = new Date();
      const currentMonth = currentDate.toLocaleString('en', { month: 'long' });
      const currentYear = currentDate.getFullYear();
      const currentDateString = `${currentMonth} ${currentYear}`;

      // Then use LLM to structure and enhance the information
      const { object: currencyData } = await generateObject({
        model: this.model,
        system: `You are a professional travel financial advisor and currency expert.
        Your task is to provide comprehensive, practical, and actionable financial information for travelers.

        CRITICAL REQUIREMENTS:
        1. Provide REAL, SPECIFIC, and CURRENT information - no generic placeholders
        2. Include actual exchange rates, fees, and practical details
        3. Give specific bank names, ATM networks, and locations when possible
        4. Provide real tipping amounts and cultural context
        5. Include actual costs for common items/services
        6. Use web search results to ensure accuracy and current information
        7. Do NOT use placeholder text like "Check with bank" or "Varies"
        8. Do NOT assign imageUrl values - images will be matched separately

        Focus on practical traveler needs: real costs, specific locations, actual fees, cultural money etiquette.`,
        prompt: `You are analyzing financial information for ${destinationInfo.destination}, ${destinationInfo.country || destinationInfo.destination}.

        CURRENT DATE: ${currentDateString}

        WEB SEARCH RESULTS WITH REAL DATA:
        ${webSearchResults}

        CURRENCY IMAGES AVAILABLE:
        ${currencyImages}

        MANDATORY REQUIREMENTS - Extract COMPREHENSIVE and CURRENT information from the search results above:

        🏦 EXCHANGE & BANKS:
        - Specific bank names, exchange offices, and their exact locations
        - Real fees, margins, and commissions with actual percentages
        - DETAILED OPENING HOURS: weekdays, weekends, holidays, lunch breaks
        - Which providers offer the best rates (avoid generic "banks are good")

        🏧 ATM INFORMATION:
        - Specific ATM networks (Plus, Cirrus, local networks)
        - Exact withdrawal fees and daily limits in local currency
        - 24/7 availability and common locations (airports, convenience stores, banks)
        - LANGUAGE SUPPORT: "English interface available" or "Japanese only, bring translation app"
        - CONCRETE EXAMPLE: "Withdrawing 10,000 JPY costs 220 JPY fee + 3% foreign exchange fee = total 520 JPY"
        - Reliability for international cards (Visa, Mastercard, etc.)

        💳 PAYMENT METHODS:
        - Detailed card acceptance by brand (Visa, Mastercard, Amex, etc.)
        - Specific foreign transaction fees and restrictions
        - Places that are cash-only vs card-friendly
        - Contactless payment availability and adoption
        - TRANSPORT CARDS: "Suica/Pasmo available, 500 JPY deposit, refundable, works on trains/buses/some shops"

        💵 CASH USAGE:
        - How critical cash is for daily transactions
        - Recommended amounts and most useful denominations
        - Specific places that only accept cash (transport, small shops, etc.)

        📱 DIGITAL PAYMENTS:
        - Local mobile payment apps and their popularity
        - REAL ACCESSIBILITY: "PayPay requires Japanese phone number and bank account" or "Alipay accepts international cards"
        - Specific setup requirements for tourists (phone numbers, verification, etc.)
        - Modern solutions (Wise, Revolut, N26) acceptance
        - QR code payment systems availability

        🔒 SAFETY & PRECAUTIONS:
        - How to avoid Dynamic Currency Conversion (DCC) and unnecessary fees
        - International card activation requirements before travel
        - Money safety tips and security precautions
        - Importance of carrying multiple payment methods
        - LOCAL SAFETY SPECIFICS: "ATMs inside banks safer than street machines", "No cash limits for tourists", "Card skimming rare but check for loose parts"
        - EMERGENCY ACCESS: "Embassy can help with emergency funds", "Western Union locations available"

        💡 PRACTICAL TIPS:
        - Best money exchange and withdrawal strategies
        - How to avoid tourist trap exchange rates
        - Special considerations unique to this destination
        - Specific cost minimization techniques

        🎯 CONCRETE EXAMPLES REQUIRED:
        - ATM withdrawal: "Withdraw 100 USD (≈15,000 JPY): 220 JPY ATM fee + 450 JPY foreign exchange = 670 JPY total cost"
        - Restaurant: "Dinner for 2: 4,500 JPY, pay by card (no tip needed), total charged: 4,500 JPY"
        - Transport: "Tokyo Metro day pass: 800 JPY, buy with IC card or cash at station"
        - Shopping: "Convenience store: accepts cash, IC cards, some accept PayPay"
        - Hotel: "Check-in deposit: 10,000 JPY hold on card, released at checkout"

        EXTRACT FROM SEARCH RESULTS (AS OF ${currentDateString}):
        1. Real exchange rates with actual numbers and provider names
        2. Specific bank/ATM names with exact fees, locations, and OPENING HOURS
        3. Actual meal, transport, and accommodation prices in local currency
        4. Cultural practices with specific amounts and customs
        5. CONCRETE EXAMPLES of real transactions with exact costs
        6. REAL ACCESSIBILITY details for mobile apps (not just "available to tourists")

        DO NOT USE GENERIC PHRASES:
        ❌ "Check with bank"
        ❌ "Fees vary"
        ❌ "Contact your bank"
        ❌ "Varies by location"

        USE SPECIFIC DATA EXAMPLES:
        ✅ "Local Bank Name: X% margin + local currency fee"
        ✅ "ATM Network: specific fee per withdrawal"
        ✅ "Local dish: price range in local currency"
        ✅ "Tipping: specific cultural practice and amounts"`,
        schema: z.object({
          code: z.string().describe('Currency code'),
          symbol: z.string().describe('Currency symbol'),
          name: z.string().describe('Currency name'),
          country: z.string().describe('Country name'),

          // Simplified financial information to avoid schema complexity
          exchangeInfo: z.object({
            currentRate: z.object({
              usd: z.number().describe('USD to local rate'),
              eur: z.number().describe('EUR to local rate'),
              lastUpdated: z.string().describe('Last update date'),
            }),
            bestExchangeLocations: z.array(
              z.object({
                type: z.string().describe('Location type'),
                name: z.string().describe('Specific name'),
                fees: z.string().describe('Fee structure'),
                rate: z.string().describe('Rate quality'),
                locations: z.string().describe('Where to find'),
                openingHours: z
                  .string()
                  .describe('Opening hours including weekends'),
                notes: z.string().optional().describe('Additional notes'),
              }),
            ),
            atmInfo: z.object({
              networks: z.array(z.string()).describe('ATM networks'),
              fees: z.string().describe('ATM fees'),
              dailyLimits: z.string().describe('Daily limits'),
              locations: z.string().describe('ATM locations'),
              languageSupport: z
                .string()
                .describe('Language options available on ATM screens'),
              concreteExample: z
                .string()
                .describe('Real withdrawal example with exact fees'),
            }),
          }),

          paymentMethods: z.object({
            creditCards: z.object({
              acceptance: z
                .string()
                .describe('Card acceptance level and coverage'),
              preferredCards: z
                .array(z.string())
                .describe('Most widely accepted card brands'),
              fees: z.string().describe('Typical foreign transaction fees'),
              restrictions: z
                .string()
                .describe('Places where cards may not be accepted'),
              contactless: z
                .string()
                .describe('Contactless payment availability'),
            }),
            cash: z.object({
              importance: z
                .string()
                .describe('How important cash is in daily transactions'),
              recommendations: z
                .string()
                .describe('Recommended amounts and denominations'),
              usefulDenominations: z
                .string()
                .describe('Most useful bills and coins for travelers'),
              cashOnlyPlaces: z
                .string()
                .describe('Common places that only accept cash'),
            }),
            digitalPayments: z.object({
              localApps: z
                .array(z.string())
                .describe('Popular local mobile payment apps'),
              internationalAccess: z
                .string()
                .describe('Whether international travelers can use them'),
              accessibilityDetails: z
                .string()
                .describe(
                  'Specific requirements for tourists to access local apps',
                ),
              modernSolutions: z
                .array(z.string())
                .describe('Wise, Revolut, N26, prepaid cards acceptance'),
              qrCodePayments: z
                .string()
                .describe('QR code payment availability'),
            }),
            transportCards: z.object({
              available: z
                .array(z.string())
                .describe('Available transport cards'),
              touristFriendly: z
                .string()
                .describe('Tourist accessibility and purchase process'),
              depositRefund: z
                .string()
                .describe('Deposit amounts and refund process'),
              usageAreas: z
                .string()
                .describe('Where transport cards can be used'),
            }),
          }),

          tipping: z.object({
            culture: z.string().describe('Tipping culture'),
            restaurants: z.string().describe('Restaurant tipping'),
            taxis: z.string().describe('Taxi tipping'),
            hotels: z.string().describe('Hotel tipping'),
            other: z.string().describe('Other tipping'),
          }),

          typicalCosts: z.object({
            meals: z.object({
              budget: z.string().describe('Budget meals with specific prices'),
              midRange: z
                .string()
                .describe('Mid-range meals with specific prices'),
              upscale: z
                .string()
                .describe('Upscale meals with specific prices'),
            }),
            transport: z.object({
              publicTransport: z
                .string()
                .describe('Public transport costs and day passes'),
              taxi: z.string().describe('Taxi costs and typical fares'),
              rideshare: z
                .string()
                .describe('Rideshare costs and availability'),
            }),
            accommodation: z.object({
              budget: z.string().describe('Budget hotels and hostels'),
              midRange: z.string().describe('Mid-range hotels'),
              luxury: z.string().describe('Luxury hotels and resorts'),
            }),
          }),

          safetyAndPrecautions: z.object({
            avoidFees: z
              .string()
              .describe('How to avoid unnecessary fees and DCC'),
            cardActivation: z
              .string()
              .describe('International card activation requirements'),
            safetyTips: z.string().describe('Money safety and security tips'),
            backupPayments: z
              .string()
              .describe('Importance of multiple payment methods'),
            localSafety: z.object({
              atmSafety: z
                .string()
                .describe('ATM safety specific to this location'),
              cashLimits: z
                .string()
                .describe('Local regulations on cash carrying amounts'),
              cardSkimming: z
                .string()
                .describe('Card skimming protection measures'),
              emergencyAccess: z
                .string()
                .describe('Emergency money access if cards are lost'),
            }),
          }),

          practicalTips: z.object({
            bestStrategy: z
              .string()
              .describe('Best money exchange and withdrawal strategy'),
            avoidTouristTraps: z
              .string()
              .describe('How to avoid tourist trap exchange rates'),
            specialConsiderations: z
              .string()
              .describe('Special considerations for this destination'),
            costMinimization: z
              .string()
              .describe('Specific tips to minimize costs'),
          }),

          concreteExamples: z.object({
            atmWithdrawal: z
              .string()
              .describe(
                'Real ATM withdrawal example with exact amounts and fees',
              ),
            restaurantPayment: z
              .string()
              .describe('Restaurant payment example with tipping'),
            transportPayment: z
              .string()
              .describe('Public transport payment example'),
            shoppingPayment: z.string().describe('Shopping payment example'),
            hotelPayment: z
              .string()
              .describe('Hotel check-in and payment example'),
          }),
          denominations: z
            .object({
              bills: z
                .array(
                  z.object({
                    value: z.number().describe('Face value of the bill'),
                    type: z
                      .literal('bill')
                      .describe('Type indicator for bills'),
                    description: z
                      .string()
                      .optional()
                      .describe(
                        'Brief description of the bill (color, notable features)',
                      ),
                    color: z
                      .string()
                      .optional()
                      .describe('Primary color of the bill'),
                    material: z
                      .string()
                      .optional()
                      .describe('Material (e.g., paper, polymer)'),
                    size: z
                      .string()
                      .optional()
                      .describe(
                        'Size description (e.g., small, medium, large)',
                      ),
                    imageUrl: z
                      .string()
                      .optional()
                      .describe('URL of the bill image if available'),
                  }),
                )
                .describe('Array of all current banknotes in circulation'),
              coins: z
                .array(
                  z.object({
                    value: z.number().describe('Face value of the coin'),
                    type: z
                      .literal('coin')
                      .describe('Type indicator for coins'),
                    description: z
                      .string()
                      .optional()
                      .describe('Brief description of the coin'),
                    color: z
                      .string()
                      .optional()
                      .describe('Primary color/material of the coin'),
                    material: z
                      .string()
                      .optional()
                      .describe('Material (e.g., copper, silver, nickel)'),
                    size: z
                      .string()
                      .optional()
                      .describe(
                        'Size description (e.g., small, medium, large)',
                      ),
                    imageUrl: z
                      .string()
                      .optional()
                      .describe('URL of the coin image if available'),
                  }),
                )
                .describe('Array of all current coins in circulation'),
            })
            .describe('Complete denomination information for bills and coins'),
        }),
      });

      console.log('✅ [CurrencyAgent] Retrieved currency data:', currencyData);

      // Search for specific images for each denomination
      if (currencyData.denominations) {
        console.log(
          '🔍 [CurrencyAgent] Searching for specific denomination images...',
        );
        const updatedDenominations =
          await this.searchSpecificDenominationImages(
            destinationInfo,
            currencyData.code,
            currencyData.denominations,
          );

        currencyData.denominations = updatedDenominations;
        console.log(
          '✅ [CurrencyAgent] Updated currency data with specific images',
        );
      }

      // Validate that we have real data, not generic placeholders
      await this.validateCurrencyData(currencyData, destinationInfo);

      // Cache the result
      this.cache.set(cacheKey, currencyData);

      // Stream completion notification
      if (dataStream) {
        console.log(
          '🔄 [CurrencyAgent] Streaming: Currency data generation completed',
        );
      }

      return currencyData;
    } catch (error) {
      console.error('Error detecting currency with LLM:', error);
      throw new Error(
        'Failed to determine currency information. Please try again.',
      );
    }
  }

  /**
   * Validate currency data using intelligent LLM-based analysis
   */
  private async validateCurrencyData(
    currencyData: CurrencyInfo,
    destinationInfo: DestinationInfo,
  ): Promise<void> {
    try {
      // Use LLM to intelligently validate the currency data
      const { object: validation } = await generateObject({
        model: this.model,
        system: `You are a travel finance expert validator. Analyze the provided currency data for accuracy, completeness, and cultural appropriateness for the specific destination.

        Your task is to identify:
        1. Generic placeholder phrases that provide no real value
        2. Missing destination-specific information that travelers need
        3. Cultural inaccuracies (e.g., tipping practices, payment preferences)
        4. Unrealistic or outdated information

        Be intelligent and adaptive - don't use hardcoded rules, but apply your knowledge of global financial practices.`,
        prompt: `Validate this currency data for ${destinationInfo.destination}, ${destinationInfo.country}:

        CURRENCY DATA TO VALIDATE:
        ${JSON.stringify(currencyData, null, 2)}

        Analyze for:
        1. Generic phrases like "check with bank", "fees vary", "contact your bank"
        2. Missing local banks, ATM networks, or payment methods specific to this destination
        3. Incorrect cultural practices (e.g., tipping expectations)
        4. Unrealistic exchange rates or costs
        5. Missing practical information travelers would need

        Provide specific, actionable feedback for this destination.`,
        schema: z.object({
          isValid: z.boolean().describe('Overall data quality assessment'),
          genericPhrases: z
            .array(
              z.object({
                phrase: z.string().describe('Generic phrase found'),
                field: z.string().describe('Field where it was found'),
                suggestion: z.string().describe('What should be there instead'),
              }),
            )
            .describe('Generic placeholder phrases detected'),
          missingInformation: z
            .array(
              z.object({
                category: z.string().describe('Category of missing info'),
                description: z
                  .string()
                  .describe('What specific information is missing'),
                importance: z
                  .enum(['high', 'medium', 'low'])
                  .describe('How important this missing info is'),
              }),
            )
            .describe('Important information missing for this destination'),
          culturalIssues: z
            .array(
              z.object({
                issue: z.string().describe('Cultural inaccuracy identified'),
                correction: z
                  .string()
                  .describe('What the correct information should be'),
              }),
            )
            .describe('Cultural inaccuracies that need correction'),
          dataQualityScore: z
            .number()
            .min(0)
            .max(100)
            .describe('Overall data quality score (0-100)'),
          recommendations: z
            .array(z.string())
            .describe('Specific recommendations to improve the data'),
        }),
        temperature: 0.1,
      });

      // Log validation results
      console.log(
        `📊 [CurrencyAgent] Data quality score: ${validation.dataQualityScore}/100`,
      );

      // Report generic phrases
      if (validation.genericPhrases.length > 0) {
        console.warn('⚠️ [CurrencyAgent] Generic phrases detected:');
        validation.genericPhrases.forEach((item) => {
          console.warn(`   • "${item.phrase}" in ${item.field}`);
          console.warn(`     Suggestion: ${item.suggestion}`);
        });
      }

      // Report missing information
      if (validation.missingInformation.length > 0) {
        console.warn(
          '⚠️ [CurrencyAgent] Missing destination-specific information:',
        );
        validation.missingInformation.forEach((item) => {
          const priority =
            item.importance === 'high'
              ? '🔴'
              : item.importance === 'medium'
                ? '🟡'
                : '🟢';
          console.warn(`   ${priority} ${item.category}: ${item.description}`);
        });
      }

      // Report cultural issues
      if (validation.culturalIssues.length > 0) {
        console.warn('⚠️ [CurrencyAgent] Cultural inaccuracies detected:');
        validation.culturalIssues.forEach((item) => {
          console.warn(`   • Issue: ${item.issue}`);
          console.warn(`     Correction: ${item.correction}`);
        });
      }

      // Report recommendations
      if (validation.recommendations.length > 0) {
        console.log('💡 [CurrencyAgent] Recommendations for improvement:');
        validation.recommendations.forEach((rec, index) => {
          console.log(`   ${index + 1}. ${rec}`);
        });
      }

      // Overall assessment
      if (validation.dataQualityScore >= 80) {
        console.log('✅ [CurrencyAgent] Currency data validation: EXCELLENT');
      } else if (validation.dataQualityScore >= 60) {
        console.log(
          '⚠️ [CurrencyAgent] Currency data validation: GOOD (room for improvement)',
        );
      } else {
        console.log(
          '❌ [CurrencyAgent] Currency data validation: NEEDS IMPROVEMENT',
        );
      }
    } catch (error) {
      console.warn(
        '⚠️ [CurrencyAgent] Validation failed, using basic checks:',
        error,
      );

      // Fallback to basic generic phrase detection only
      const genericPhrases = [
        'check with bank',
        'fees vary',
        'contact your bank',
        'varies by location',
      ];

      const fieldsToCheck = [
        currencyData.exchangeInfo?.atmInfo?.fees,
        currencyData.exchangeInfo?.bestExchangeLocations?.[0]?.fees,
        currencyData.paymentMethods?.creditCards?.fees,
      ];

      let foundGeneric = false;
      for (const field of fieldsToCheck) {
        if (field) {
          const fieldLower = field.toLowerCase();
          for (const phrase of genericPhrases) {
            if (fieldLower.includes(phrase)) {
              console.warn(
                `⚠️ [CurrencyAgent] Generic phrase: "${phrase}" in "${field}"`,
              );
              foundGeneric = true;
            }
          }
        }
      }

      if (!foundGeneric) {
        console.log('✅ [CurrencyAgent] Basic validation completed');
      }
    }
  }

  /**
   * Comprehensive search for financial and currency information using web_search tool
   */
  private async searchComprehensiveFinancialInfo(
    destinationInfo: DestinationInfo,
    dataStream?: UIMessageStreamWriter<ChatMessage>,
  ): Promise<string> {
    try {
      // Create mock session and dataStream for web_search tool
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };

      const mockDataStream: UIMessageStreamWriter<ChatMessage> = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      } as UIMessageStreamWriter<ChatMessage>;

      const searchTool = web_search({
        session: mockSession,
        dataStream: mockDataStream,
      });

      const destination = destinationInfo.destination;
      const country = destinationInfo.country || destinationInfo.destination;

      // Get current date for search queries
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.toLocaleString('en', { month: 'long' });
      const currentDateString = `${currentMonth} ${currentYear}`;

      // Enhanced search queries with comprehensive financial information
      const queries = [
        // Exchange & Banks with detailed hours
        `${destination} ${country} best currency exchange locations banks offices rates fees margins ${currentDateString}`,
        `${destination} ${country} bank opening hours weekdays weekends holidays exchange services ${currentYear}`,
        `${destination} ${country} airport exchange rates vs bank rates comparison fees ${currentYear}`,
        `${destination} ${country} major banks Mizuho MUFG Sumitomo exchange rates hours locations ${currentYear}`,

        // ATMs with concrete examples and language support
        `${destination} ${country} ATM networks foreign cards Plus Cirrus withdrawal fees daily limits ${currentYear}`,
        `${destination} ${country} 24/7 ATM locations convenience stores banks airports ${currentYear}`,
        `${destination} ${country} ATM withdrawal example fees 10000 50000 local currency foreign card ${currentYear}`,
        `${destination} ${country} ATM reliability international cards Visa Mastercard success rates ${currentYear}`,
        `${destination} ${country} ATM language support English interface foreign tourists ${currentYear}`,
        `${destination} ${country} ATM screen language options international visitors ${currentYear}`,

        // Payment Methods and Transport Cards
        `${destination} ${country} credit card acceptance Visa Mastercard Amex foreign transaction fees ${currentYear}`,
        `${destination} ${country} cash only businesses small shops transport payment restrictions ${currentYear}`,
        `${destination} ${country} contactless payment tap card acceptance ${currentYear}`,
        `${destination} ${country} transport cards IC card Suica Pasmo tourist purchase refund ${currentYear}`,
        `${destination} ${country} public transport payment methods cards cash mobile apps ${currentYear}`,
        `${destination} ${country} transport card deposit refund tourist friendly options ${currentYear}`,

        // Cash Usage & Denominations
        `${destination} ${country} cash importance small businesses vending machines transport ${currentYear}`,
        `${destination} ${country} useful bill denominations coins small purchases tips ${currentYear}`,
        `${country} currency banknotes coins denominations current circulation official ${currentYear}`,

        // Mobile & Digital Payments with accessibility details
        `${destination} ${country} mobile payment apps PayPay Alipay Apple Pay Google Pay international travelers ${currentYear}`,
        `${destination} ${country} digital wallet acceptance QR code payments ${currentYear}`,
        `${destination} ${country} Wise Revolut N26 prepaid travel cards acceptance ${currentYear}`,
        `${destination} ${country} PayPay Alipay tourist registration foreign phone number setup guide ${currentYear}`,
        `${destination} ${country} mobile payment apps tourist access requirements verification process ${currentYear}`,

        // Safety & Precautions with local specifics
        `${destination} ${country} Dynamic Currency Conversion DCC avoid fees pay local currency ${currentYear}`,
        `${destination} ${country} money safety tips ATM security carrying cash precautions ${currentYear}`,
        `${destination} ${country} international card activation travel notification banks ${currentYear}`,
        `${destination} ${country} ATM safety locations avoid dangerous areas secure machines ${currentYear}`,
        `${destination} ${country} cash carrying limits police regulations tourist safety ${currentYear}`,
        `${destination} ${country} card skimming protection ATM security tourist areas ${currentYear}`,
        `${destination} ${country} emergency money access lost card cash embassy help ${currentYear}`,

        // Practical Tips & Costs with concrete examples
        `${destination} ${country} restaurant meal prices budget mid-range upscale dining costs ${currentDateString}`,
        `${destination} ${country} transport costs public transport taxi rideshare prices ${currentYear}`,
        `${destination} ${country} accommodation costs hotels budget luxury prices ${currentDateString}`,
        `${destination} ${country} tipping culture restaurants hotels taxis customs guide ${currentYear}`,
        `${destination} ${country} money exchange strategy minimize costs avoid tourist traps ${currentYear}`,

        // Concrete examples and real scenarios
        `${destination} ${country} ATM withdrawal example 100 USD 200 USD fees total cost foreign card ${currentYear}`,
        `${destination} ${country} restaurant bill payment example credit card cash tipping ${currentYear}`,
        `${destination} ${country} transport payment example train subway bus card cash mobile app ${currentYear}`,
        `${destination} ${country} shopping payment example department store convenience store cash card ${currentYear}`,
        `${destination} ${country} hotel payment example check-in deposit foreign card local currency ${currentYear}`,
      ];

      if (dataStream) {
        console.log(
          '🔄 [CurrencyAgent] Streaming: Executing enhanced web search queries',
        );
      }

      const searchPromises = queries.map((query) =>
        (searchTool.execute as any)({
          queries: [query],
          maxResults: [5], // More results for comprehensive information
          topics: ['general'],
          searchDepth: ['comprehensive'], // Deeper search for detailed info
          language: 'en', // English for better financial data
        }),
      );

      const results = await Promise.all(searchPromises);

      if (dataStream) {
        console.log(
          '🔄 [CurrencyAgent] Streaming: Web search completed successfully',
        );
      }

      // Combine results into a comprehensive string
      let combinedResults = '';
      results.forEach((result, index) => {
        combinedResults += `\n--- Search Query ${index + 1}: ${queries[index]} ---\n`;
        combinedResults += JSON.stringify(result, null, 2);
      });

      return combinedResults || 'No enhanced web search results available.';
    } catch (error) {
      console.error('Error in enhanced currency search:', error);
      if (dataStream) {
        console.log(
          '🔄 [CurrencyAgent] Streaming: Falling back to basic search',
        );
      }
      // Fallback to original method
      return this.searchCurrencyInformation(destinationInfo);
    }
  }

  /**
   * Search for currency information using Serper API (fallback method)
   */
  private async searchCurrencyInformation(
    destinationInfo: DestinationInfo,
  ): Promise<string> {
    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      console.warn(
        'Serper API key not found, falling back to LLM-only approach',
      );
      return 'No web search results available. Please use your knowledge to provide currency information.';
    }

    try {
      const country = destinationInfo.country || destinationInfo.destination;

      // Search for current banknotes and coins information
      const searchQueries = [
        `${country} currency banknotes bills current circulation 2024`,
        `${country} coins currency denominations current 2024`,
        `${country} money bills coins official currency guide`,
      ];

      let allResults = '';

      for (const query of searchQueries) {
        console.log(`🔍 [CurrencyAgent] Searching: ${query}`);

        const response = await fetch('https://google.serper.dev/search', {
          method: 'POST',
          headers: {
            'X-API-KEY': apiKey,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            q: query,
            num: 5, // Limit results to avoid too much data
          }),
        });

        if (!response.ok) {
          console.error(`Serper API error: ${response.status}`);
          continue;
        }

        const data = await response.json();

        if (data.organic) {
          const results = data.organic
            .slice(0, 3)
            .map((result: any) => {
              return `Title: ${result.title}\nSnippet: ${result.snippet}\nSource: ${result.link}`;
            })
            .join('\n\n');

          allResults += `\n--- Results for "${query}" ---\n${results}\n`;
        }

        // Add a small delay to respect rate limits
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      console.log('✅ [CurrencyAgent] Web search completed');
      return (
        allResults ||
        'No relevant web search results found. Please use your knowledge to provide currency information.'
      );
    } catch (error) {
      console.error('Error searching currency information:', error);
      return 'Web search failed. Please use your knowledge to provide currency information.';
    }
  }

  /**
   * Search for currency images using Serper API
   */
  private async searchCurrencyImages(
    destinationInfo: DestinationInfo,
  ): Promise<string> {
    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      return 'No currency images available.';
    }

    try {
      const country = destinationInfo.country || destinationInfo.destination;

      // Search for currency images
      const imageQuery = `${country} currency banknotes coins money bills official`;

      console.log(`🖼️ [CurrencyAgent] Searching images: ${imageQuery}`);

      const response = await fetch('https://google.serper.dev/images', {
        method: 'POST',
        headers: {
          'X-API-KEY': apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: imageQuery,
          num: 10, // Get more images to have options
        }),
      });

      if (!response.ok) {
        console.error(`Serper Images API error: ${response.status}`);
        return 'No currency images available.';
      }

      const data = await response.json();

      if (data.images && data.images.length > 0) {
        const imageResults = data.images
          .slice(0, 8)
          .map((image: any, index: number) => {
            return `Image ${index + 1}: ${image.imageUrl} (Title: ${image.title})`;
          })
          .join('\n');

        console.log('✅ [CurrencyAgent] Currency images found');
        return imageResults;
      }

      return 'No currency images found.';
    } catch (error) {
      console.error('Error searching currency images:', error);
      return 'Currency image search failed.';
    }
  }

  /**
   * Search for specific denomination images
   */
  private async searchSpecificDenominationImages(
    destinationInfo: DestinationInfo,
    currencyCode: string,
    denominations: { bills: any[]; coins: any[] },
  ): Promise<{ bills: any[]; coins: any[] }> {
    const apiKey = process.env.SERPER_API as string;
    if (!apiKey) {
      console.warn('Serper API key not found, skipping specific image search');
      return denominations;
    }

    const country = destinationInfo.country || destinationInfo.destination;

    try {
      // Search for specific bill images
      const updatedBills = await Promise.all(
        denominations.bills.map(async (bill) => {
          try {
            const billQuery = `${country} ${currencyCode} ${bill.value} banknote bill currency official`;
            console.log(
              `🔍 [CurrencyAgent] Searching for specific bill: ${billQuery}`,
            );

            const response = await fetch('https://google.serper.dev/images', {
              method: 'POST',
              headers: {
                'X-API-KEY': apiKey,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                q: billQuery,
                num: 3, // Get fewer but more specific results
              }),
            });

            if (response.ok) {
              const data = await response.json();
              if (data.images && data.images.length > 0) {
                // Find the most relevant image based on title and URL matching
                const relevantImage =
                  data.images.find((img: any) => {
                    const title = (img.title || '').toLowerCase();
                    const url = (img.imageUrl || '').toLowerCase();
                    const valueStr = bill.value.toString();

                    return (
                      // Check title for exact value match
                      title.includes(valueStr) ||
                      title.includes(
                        `${valueStr} ${currencyCode.toLowerCase()}`,
                      ) ||
                      title.includes(
                        `${currencyCode.toLowerCase()} ${valueStr}`,
                      ) ||
                      title.includes(
                        `${valueStr}${currencyCode.toLowerCase()}`,
                      ) ||
                      // Check URL for value patterns
                      url.includes(valueStr) ||
                      url.includes(
                        `${valueStr}${currencyCode.toLowerCase()}`,
                      ) ||
                      // Check for banknote/bill keywords with value
                      (title.includes('banknote') &&
                        title.includes(valueStr)) ||
                      (title.includes('bill') && title.includes(valueStr))
                    );
                  }) || data.images[0]; // Fallback to first image

                // Validate image URL before assigning
                const validatedImageUrl = await this.validateImageUrl(
                  relevantImage.imageUrl,
                );

                return {
                  ...bill,
                  imageUrl: validatedImageUrl,
                };
              }
            }

            // Add small delay to respect rate limits
            await new Promise((resolve) => setTimeout(resolve, 200));

            // Log that no specific image was found for this bill
            console.log(
              `ℹ️ [CurrencyAgent] No specific image found for ${currencyCode} ${bill.value} bill`,
            );
            return bill;
          } catch (error) {
            console.error(`Error searching for bill ${bill.value}:`, error);
            return bill;
          }
        }),
      );

      // Search for specific coin images
      const updatedCoins = await Promise.all(
        denominations.coins.map(async (coin) => {
          try {
            const coinQuery = `${country} ${currencyCode} ${coin.value} coin currency official`;
            console.log(
              `🔍 [CurrencyAgent] Searching for specific coin: ${coinQuery}`,
            );

            const response = await fetch('https://google.serper.dev/images', {
              method: 'POST',
              headers: {
                'X-API-KEY': apiKey,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                q: coinQuery,
                num: 3, // Get fewer but more specific results
              }),
            });

            if (response.ok) {
              const data = await response.json();
              if (data.images && data.images.length > 0) {
                // Find the most relevant image based on title and URL matching
                const relevantImage =
                  data.images.find((img: any) => {
                    const title = (img.title || '').toLowerCase();
                    const url = (img.imageUrl || '').toLowerCase();
                    const valueStr = coin.value.toString();
                    const centValue =
                      coin.value < 1 ? (coin.value * 100).toString() : null;

                    return (
                      // Check title for exact value match
                      title.includes(valueStr) ||
                      title.includes(
                        `${valueStr} ${currencyCode.toLowerCase()}`,
                      ) ||
                      title.includes(
                        `${currencyCode.toLowerCase()} ${valueStr}`,
                      ) ||
                      title.includes(
                        `${valueStr}${currencyCode.toLowerCase()}`,
                      ) ||
                      // Check for cent values if applicable
                      (centValue &&
                        (title.includes(centValue) ||
                          title.includes(`${centValue}c`))) ||
                      // Check URL for value patterns
                      url.includes(valueStr) ||
                      url.includes(
                        `${valueStr}${currencyCode.toLowerCase()}`,
                      ) ||
                      // Check for coin keywords with value
                      (title.includes('coin') && title.includes(valueStr)) ||
                      (centValue &&
                        title.includes('coin') &&
                        title.includes(centValue))
                    );
                  }) || data.images[0]; // Fallback to first image

                // Validate image URL before assigning
                const validatedImageUrl = await this.validateImageUrl(
                  relevantImage.imageUrl,
                );

                return {
                  ...coin,
                  imageUrl: validatedImageUrl,
                };
              }
            }

            // Add small delay to respect rate limits
            await new Promise((resolve) => setTimeout(resolve, 200));

            // Log that no specific image was found for this coin
            console.log(
              `ℹ️ [CurrencyAgent] No specific image found for ${currencyCode} ${coin.value} coin`,
            );
            return coin;
          } catch (error) {
            console.error(`Error searching for coin ${coin.value}:`, error);
            return coin;
          }
        }),
      );

      console.log(
        '✅ [CurrencyAgent] Specific denomination images search completed',
      );
      return {
        bills: updatedBills,
        coins: updatedCoins,
      };
    } catch (error) {
      console.error('Error in specific denomination image search:', error);
      return denominations;
    }
  }

  /**
   * Validate if an image URL is accessible and returns a valid image
   */
  private async validateImageUrl(
    imageUrl: string,
  ): Promise<string | undefined> {
    if (!imageUrl) return undefined;

    try {
      // Check if URL is valid
      const url = new URL(imageUrl);

      // Only allow https URLs for security
      if (url.protocol !== 'https:') {
        console.warn(`[CurrencyAgent] Skipping non-HTTPS image: ${imageUrl}`);
        return undefined;
      }

      // Check if the URL points to a valid image format
      const validExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
      const hasValidExtension = validExtensions.some((ext) =>
        url.pathname.toLowerCase().includes(ext),
      );

      // If no extension in path, check if it's from a known image service
      const knownImageServices = [
        'images.unsplash.com',
        'cdn.pixabay.com',
        'images.pexels.com',
        'upload.wikimedia.org',
        'commons.wikimedia.org',
        'cdn.britannica.com',
        'www.worldatlas.com',
      ];

      const isFromKnownService = knownImageServices.some((service) =>
        url.hostname.includes(service),
      );

      if (!hasValidExtension && !isFromKnownService) {
        console.warn(
          `[CurrencyAgent] Skipping potentially invalid image URL: ${imageUrl}`,
        );
        return undefined;
      }

      // Try to fetch the image with a HEAD request to check if it exists
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(imageUrl, {
        method: 'HEAD',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const contentType = response.headers.get('content-type');
        if (contentType?.startsWith('image/')) {
          console.log(`✅ [CurrencyAgent] Validated image URL: ${imageUrl}`);
          return imageUrl;
        } else {
          console.warn(
            `[CurrencyAgent] URL does not return an image: ${imageUrl}`,
          );
          return undefined;
        }
      } else {
        console.warn(
          `[CurrencyAgent] Image URL not accessible (${response.status}): ${imageUrl}`,
        );
        return undefined;
      }
    } catch (error) {
      console.warn(
        `[CurrencyAgent] Error validating image URL ${imageUrl}:`,
        error,
      );
      return undefined;
    }
  }
}

// Currency detection now uses web search combined with LLM analysis
