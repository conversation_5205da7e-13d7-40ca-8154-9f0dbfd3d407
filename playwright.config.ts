import { defineConfig, devices } from '@playwright/test';

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
import { config } from 'dotenv';

// Load environment variables from .env.test if NODE_ENV is test
// Otherwise, load from .env
const envFile = process.env.NODE_ENV === 'test' ? '.env.test' : '.env';
console.log(`Loading environment variables from ${envFile}`);

config({
  path: envFile,
});

/* Use process.env.PORT by default and fallback to port 3000 */
const PORT = process.env.PORT || 3000;

/**
 * Set webServer.url and use.baseURL with the location
 * of the WebServer respecting the correct set port
 */
const baseURL = `http://localhost:${PORT}`;

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
  testDir: './tests',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 2 : 8,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: 'html',
  /* Global setup to initialize mocks */
  globalSetup: require.resolve('./tests/global-setup.ts'),
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL,

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'retain-on-failure',
  },

  // Increase timeouts for more stability
  timeout: 240 * 1000, // 2 minutes
  expect: {
    timeout: 240 * 1000,
  },

  /* Configure projects */
  projects: [
    {
      name: 'e2e',
      testMatch: /e2e\/.*.test.ts/,
      use: {
        ...devices['Desktop Chrome'],
        // Set a specific viewport size for e2e tests to ensure consistent UI testing
        viewport: { width: 1280, height: 720 },
        launchOptions: {
          args: ['--disable-web-security'], // Allow cross-origin requests for testing
        },
      },
    },
    {
      name: 'routes',
      testMatch: /routes\/.*.test.ts/,
      use: {
        ...devices['Desktop Chrome'],
        // API tests don't need a specific viewport
      },
    },

    // Uncomment these projects when needed for cross-browser testing
    // {
    //   name: 'firefox',
    //   use: { ...devices['Desktop Firefox'] },
    // },
    // {
    //   name: 'webkit',
    //   use: { ...devices['Desktop Safari'] },
    // },

    /* Test against mobile viewports - uncomment when needed */
    // {
    //   name: 'Mobile Chrome',
    //   use: { ...devices['Pixel 5'] },
    // },
    // {
    //   name: 'Mobile Safari',
    //   use: { ...devices['iPhone 12'] },
    // },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command:
      process.env.NODE_ENV === 'test' ? 'NODE_ENV=test pnpm dev' : 'pnpm dev',
    url: `${baseURL}/ping`,
    timeout: 120 * 1000,
    reuseExistingServer: !process.env.CI,
    env: {
      // Pass environment variables to the web server
      NODE_ENV: process.env.NODE_ENV || 'development',
      USE_MOCK_AI: 'true',
      USE_MOCK_WEATHER: 'true',
      USE_MOCK_AUTH: 'true',
      USE_MOCK_DATABASE: process.env.NODE_ENV === 'test' ? 'true' : 'false',
      MOCK_LATENCY: '500',
      // Add any other environment variables needed for testing
      RATE_LIMIT_DISABLED: 'true',
    },
  },
});
