import { smoothStream, streamText } from 'ai';
import { myProvider } from '@/lib/ai/providers';
import { createDocumentHandler } from '@/lib/artifacts/server';
import { updateDocumentPrompt } from '@/lib/ai/prompts';
import { storyPromptEnhanced } from '@/lib/ai/prompts.custom';

// Local alias to align with SDK runtime events while keeping TS happy across versions
type FullStreamChunk = { type: string; text?: string };

export const textDocumentHandler = createDocumentHandler<'text'>({
  kind: 'text',
  onCreateDocument: async ({ title, dataStream }) => {
    let draftContent = '';

    const { fullStream } = streamText({
      model: myProvider.languageModel('artifact-model'),
      system:
        title.toLowerCase().includes('story') ||
        title.toLowerCase().includes('tale')
          ? storyPromptEnhanced
          : 'Write about the given topic. Markdown is supported. Use headings wherever appropriate.',
      experimental_transform: smoothStream({ chunking: 'word' }),
      prompt: title,
    });

    for await (const delta of fullStream as AsyncIterable<FullStreamChunk>) {
      const { type } = delta;

      if (type === 'text-delta') {
        const text = delta.text ?? '';
        draftContent += text;
        dataStream.write({
          type: 'data-textDelta',
          data: text,
          transient: false,
        });
      }
    }

    return draftContent;
  },
  onUpdateDocument: async ({ document, description, dataStream }) => {
    let draftContent = '';

    // Gate to prevent meta commentary from entering the artifact during updates
    // We buffer the beginning of the stream and start emitting only when we detect
    // likely start of the actual revised document (e.g., markdown heading or substantive text).
    let gateOpen = false;
    let buffer = '';

    const tryOpenGate = () => {
      // Heuristics: skip common prefaces and change logs. Open gate when we detect document start.
      const lower = buffer.toLowerCase();

      // If buffer contains explicit markers, trim up to the start after them
      const markers = [
        'revised document',
        'document updated',
        'version mise à jour',
        'document révisé',
      ];

      for (const m of markers) {
        const idx = lower.indexOf(m);
        if (idx >= 0) {
          // Start from the end of the line containing the marker
          const afterMarker = lower.indexOf('\n', idx);
          const startIdx = afterMarker >= 0 ? afterMarker + 1 : idx + m.length;
          buffer = buffer.slice(startIdx);
          break;
        }
      }

      // Remove known preface sections like "Areas for Improvement"
      const prefaceHeads = [
        'areas for improvement',
        'changelog',
        'changes made',
        'améliorations effectuées',
      ];
      for (const h of prefaceHeads) {
        const hIdx = lower.indexOf(h);
        if (hIdx === 0) {
          // Drop until a blank line followed by a heading or text
          const blankThen = buffer.search(/\n\s*\n/);
          if (blankThen >= 0) buffer = buffer.slice(blankThen + 2);
        }
      }

      // Open when we see a heading or sufficient substantive text
      const hasHeading = /^(#+\s+.+|\w[^\n]{20,})/m.test(buffer);
      if (hasHeading || buffer.length > 2000) {
        gateOpen = true;
      }
    };

    const { fullStream } = streamText({
      model: myProvider.languageModel('artifact-model'),
      system: updateDocumentPrompt(document.content, 'text'),
      experimental_transform: smoothStream({ chunking: 'word' }),
      prompt: description,
      providerOptions: {
        openai: {
          prediction: {
            type: 'content',
            content: document.content,
          },
        },
      },
    });

    for await (const delta of fullStream as AsyncIterable<FullStreamChunk>) {
      const { type } = delta;

      if (type === 'text-delta') {
        const text = delta.text ?? '';

        if (!gateOpen) {
          buffer += text;
          tryOpenGate();
          if (gateOpen) {
            if (buffer) {
              draftContent += buffer;
              dataStream.write({
                type: 'data-textDelta',
                data: buffer,
                transient: false,
              });
              buffer = '';
            }
          }
        } else {
          draftContent += text;

          dataStream.write({
            type: 'data-textDelta',
            data: text,
            transient: false,
          });
        }
      }
    }

    return draftContent;
  },
});
