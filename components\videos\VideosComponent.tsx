import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Play } from '@phosphor-icons/react';
import { motion } from 'framer-motion';
import './videos.css';

// 1. Define the 'Video' interface to represent a video object
interface Video {
  link: string;
  title?: string;
  thumbnail?: string;
}

// 2. Define the 'VideosComponentProps' interface to specify the props for the 'VideosComponent'
interface VideosComponentProps {
  videos: Video[];
}

// 3. Define the 'VideosComponent' functional component that accepts 'VideosComponentProps'
const VideosComponent: React.FC<VideosComponentProps> = ({ videos }) => {
  // 4. Declare state variables using the 'useState' hook
  const [loadedImages, setLoadedImages] = useState<boolean[]>([]);
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);
  // Position du lecteur
  const [playerPosition, setPlayerPosition] = useState({ x: 0, y: 0 });

  // Fonction pour contraindre le lecteur à l'écran
  const constrainToViewport = (x: number, y: number) => {
    if (typeof window === 'undefined') return { x, y };

    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const playerWidth = 256; // w-64 = 16rem = 256px
    const playerHeight = 144; // h-36 = 9rem = 144px

    // Limiter x pour que le lecteur reste visible horizontalement
    const constrainedX = Math.min(
      Math.max(x, -viewportWidth + playerWidth),
      viewportWidth - 100,
    );

    // Limiter y pour que le lecteur reste visible verticalement
    const constrainedY = Math.min(
      Math.max(y, -viewportHeight + playerHeight),
      viewportHeight - 100,
    );

    return { x: constrainedX, y: constrainedY };
  };

  // 5. Use the 'useEffect' hook to initialize the 'loadedImages' state based on the number of videos
  useEffect(() => {
    setLoadedImages(Array(videos.length).fill(false));
  }, [videos]);

  // 6. Define the 'handleImageLoad' function to update the 'loadedImages' state when an image is loaded
  const handleImageLoad = (index: number) => {
    setLoadedImages((prevLoadedImages) => {
      const updatedLoadedImages = [...prevLoadedImages];
      updatedLoadedImages[index] = true;
      return updatedLoadedImages;
    });
  };

  // Handle image loading errors
  const handleImageError = (index: number, videoId: string) => {
    // Try to load a different thumbnail format if the maxresdefault fails
    const img = new window.Image();
    img.onload = () => handleImageLoad(index);
    img.onerror = () => {
      // If both formats fail, mark as loaded anyway to show fallback
      setLoadedImages((prevLoadedImages) => {
        const updatedLoadedImages = [...prevLoadedImages];
        updatedLoadedImages[index] = true;
        return updatedLoadedImages;
      });
    };
    img.src = `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`;
  };

  // 7. Define the 'handleVideoSelect' function to set the 'selectedVideo' state when a thumbnail is clicked
  const handleVideoSelect = (link: string) => {
    setSelectedVideo(link);
  };

  // 8. Define the 'VideosSkeleton' component to display a loading skeleton while videos are loading
  const VideosSkeleton = () => (
    <div className="w-full p-1">
      <div className="w-full overflow-hidden aspect-video rounded-lg">
        <div className="size-full bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse flex flex-col">
          <div className="h-3/4" />
          <div className="h-1/4 bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
            <div className="w-3/4 h-2 bg-gray-400 dark:bg-gray-500 rounded-full" />
          </div>
        </div>
      </div>
    </div>
  );

  // 9. Render the 'VideosComponent' JSX
  return (
    <div className="relative">
      {/* Main grid of videos */}
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full p-2 videos-grid-container">
        <div className="grid grid-cols-3 gap-2">
          {videos.length === 0 ? (
            <div className="col-span-3">
              <VideosSkeleton />
            </div>
          ) : (
            videos.map((video, index) => {
              const videoId = getYouTubeVideoId(video.link);
              const imageUrl =
                video.thumbnail ||
                `https://i.ytimg.com/vi/${videoId}/maxresdefault.jpg`;

              return (
                <div key={`video-${video.link}`} className="video-item">
                  <Card className="border-0 shadow-none video-card">
                    <CardContent className="flex items-center justify-center p-0 h-[80px]">
                      <button
                        type="button"
                        className="rounded-lg size-full overflow-hidden aspect-video transition-all duration-200 cursor-pointer border-0 p-0 bg-transparent"
                        onClick={() => handleVideoSelect(video.link)}
                        aria-label={`Play video: ${video.title || `YouTube Video ${index}`}`}
                      >
                        <div className="yt-thumbnail-container relative bg-gray-200 dark:bg-gray-700 rounded-lg">
                          <Image
                            src={imageUrl}
                            alt={video.title || `Video ${index}`}
                            fill
                            className="clip-yt-img rounded-lg object-cover"
                            onLoad={() => handleImageLoad(index)}
                            onError={() => handleImageError(index, videoId)}
                            priority={index < 3}
                            sizes="(max-width: 768px) 33vw, 25vw"
                          />
                          {/* Bouton play - apparaît avec une transition douce */}
                          <div
                            className={`yt-play-button transition-opacity duration-500 ${
                              loadedImages[index] ? 'opacity-100' : 'opacity-0'
                            }`}
                          >
                            <Play size={16} weight="fill" />
                          </div>

                          {/* Titre de la vidéo */}
                          {video.title && (
                            <div className="yt-title">{video.title}</div>
                          )}
                        </div>
                      </button>
                    </CardContent>
                  </Card>
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* Video player draggable */}
      {selectedVideo && (
        <motion.div
          drag
          dragMomentum={false}
          dragElastic={0}
          className="fixed bottom-20 right-4 z-10 w-64 h-36 shadow-lg rounded-lg overflow-hidden draggable-video-player"
          style={{
            x: playerPosition.x,
            y: playerPosition.y,
          }}
          onDrag={(_, info) => {
            // Appliquer les contraintes pendant le déplacement
            const constrained = constrainToViewport(info.point.x, info.point.y);
            setPlayerPosition(constrained);
          }}
          onDragEnd={(_, info) => {
            // Appliquer les contraintes pour garder le lecteur visible
            const constrained = constrainToViewport(info.point.x, info.point.y);
            setPlayerPosition(constrained);
          }}
        >
          <div className="relative size-full">
            <div className="absolute top-0 inset-x-0 h-8 bg-black/70 cursor-move drag-handle flex items-center justify-between px-2 z-10">
              <span className="text-white text-xs truncate">
                YouTube Player
              </span>
              <button
                type="button"
                className="text-white hover:text-gray-300 text-sm font-bold ml-2 p-1"
                onClick={() => {
                  setSelectedVideo(null);
                  // Réinitialiser la position pour la prochaine ouverture
                  setPlayerPosition({ x: 0, y: 0 });
                }}
                aria-label="Close video"
              >
                ×
              </button>
            </div>
            <iframe
              src={`https://www.youtube.com/embed/${getYouTubeVideoId(selectedVideo)}?autoplay=1`}
              title="YouTube Video Player"
              allowFullScreen
              className="size-full pt-8"
              allow="autoplay"
            />
          </div>
        </motion.div>
      )}
    </div>
  );
};

// 11. Define the 'getYouTubeVideoId' function to extract the YouTube video ID from a URL
const getYouTubeVideoId = (url: string) => {
  const match = url.match(
    /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/,
  );
  return match ? match[1] : '';
};

export default VideosComponent;
