import { tool } from 'ai';
import { z } from 'zod';

// Fonction pour géocoder un lieu en coordonnées
async function geocodeLocation(location: string) {
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(location)}&format=json&limit=1`,
    );
    const data = await response.json();

    if (data && data.length > 0) {
      return {
        latitude: Number.parseFloat(data[0].lat),
        longitude: Number.parseFloat(data[0].lon),
        displayName: data[0].display_name,
      };
    }
    return null;
  } catch (error) {
    console.error('Geocoding error:', error);
    return null;
  }
}

export const getWeather = tool({
  description: 'Get the current weather for a city or location',
  inputSchema: z.object({
    city: z
      .string()
      .describe('City name or location (e.g. "Paris", "New York")'),
  }),
  execute: async ({ city }) => {
    // Géocoder le nom de la ville en coordonnées
    const geoData = await geocodeLocation(city);

    if (!geoData) {
      return { error: `Could not find coordinates for location: ${city}` };
    }

    const { latitude, longitude, displayName } = geoData;

    // Vérifier si la clé API OpenWeatherMap est disponible
    const apiKey = process.env.OPENWEATHERMAP_API_KEY;
    if (!apiKey) {
      console.error('OpenWeatherMap API key not found');
      return { error: 'Weather service temporarily unavailable' };
    }

    try {
      // Appels parallèles pour récupérer toutes les données météo
      const [
        forecastResponse,
        airPollutionResponse,
        airPollutionForecastResponse,
        onecallResponse,
      ] = await Promise.all([
        // 5-day forecast with 3-hour intervals
        fetch(
          `https://api.openweathermap.org/data/2.5/forecast?lat=${latitude}&lon=${longitude}&appid=${apiKey}`,
        ),
        // Current air pollution
        fetch(
          `https://api.openweathermap.org/data/2.5/air_pollution?lat=${latitude}&lon=${longitude}&appid=${apiKey}`,
        ),
        // Air pollution forecast
        fetch(
          `https://api.openweathermap.org/data/2.5/air_pollution/forecast?lat=${latitude}&lon=${longitude}&appid=${apiKey}`,
        ),
        // OneCall API 3.0 for daily forecast (requires subscription but try anyway)
        fetch(
          `https://api.openweathermap.org/data/3.0/onecall?lat=${latitude}&lon=${longitude}&exclude=minutely,alerts&appid=${apiKey}`,
        ).catch(() => null), // Don't fail if OneCall is not available
      ]);

      const [
        forecastData,
        airPollutionData,
        airPollutionForecastData,
        onecallData,
      ] = await Promise.all([
        forecastResponse.json(),
        airPollutionResponse.json(),
        airPollutionForecastResponse.json(),
        onecallResponse ? onecallResponse.json().catch(() => null) : null,
      ]);

      // Structure the response with better organization
      const result = {
        ...forecastData,
        air_pollution: airPollutionData,
        air_pollution_forecast: airPollutionForecastData,
        geocoding: {
          name: city,
          country: forecastData.city?.country || '',
          displayName: displayName,
          latitude: latitude,
          longitude: longitude,
        },
      };

      // Add OneCall data if available (for extended daily forecasts)
      if (onecallData && !onecallData.cod) {
        result.daily_forecast = onecallData;
      }

      // Ensure we have proper error handling for individual API failures
      if (forecastData.cod && forecastData.cod !== '200') {
        return { error: `Weather API error: ${forecastData.message || 'Unknown error'}` };
      }

      return result;
    } catch (error) {
      console.error('Weather API error:', error);
      return { error: 'Failed to fetch weather data' };
    }
  },
});