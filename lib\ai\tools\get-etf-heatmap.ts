import { z } from 'zod';
import { tool } from 'ai';

// Define and export the tool configuration
export const getETFHeatmap = tool({
  description:
    'Display an ETF (Exchange-Traded Fund) heatmap with asset class visualization and performance',
  inputSchema: z.object({
    // No parameters needed for the heatmap as it shows a predefined view
  }),
  execute: async () => {
    // Return a simple object that will be used by the frontend to render the component
    return {
      type: 'etf_heatmap',
      data: {},
    };
  },
});
