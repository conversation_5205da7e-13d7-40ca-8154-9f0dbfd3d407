import {
  generateObject,
  type LanguageModel,
  type UIMessageStreamWriter,
} from 'ai';
import { z } from 'zod';
import type { DestinationInfo } from '../types';
import type { UserPreferences } from './preference-agent';
import { web_search } from '../../tools/web-search';
import { withTimeout, TIMEOUT_CONFIG } from '../timeout-config';
import {
  withAIModelResilience,
  createMockGenerateObjectResult,
} from '../api-resilience';
import type { ChatMessage } from '@/lib/types';

/**
 * LogisticsAgent that uses web search for comprehensive practical information
 */
export class LogisticsAgent {
  constructor(private model: LanguageModel) {}

  /**
   * Get practical information for the destination using web search
   */
  async getPracticalInfo(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
  ): Promise<PracticalInfo> {
    try {
      console.log(
        `🔍 Recherche d'informations pratiques pour ${destinationInfo.destination}...`,
      );

      // Try web search first with timeout
      try {
        const searchResults = await withTimeout(
          this.searchPracticalInfo(destinationInfo),
          TIMEOUT_CONFIG.OPERATIONS.LOGISTICS_AGENT,
          'logistics web search',
        );

        const practicalInfo = await this.generatePracticalInfoFromSearch(
          destinationInfo,
          userPreferences,
          searchResults,
        );

        console.log(
          `✅ Informations pratiques générées avec recherche web pour ${destinationInfo.destination}`,
        );
        return practicalInfo;
      } catch (searchError) {
        console.warn(
          `⚠️ Recherche web échouée pour ${destinationInfo.destination}, utilisation des données de secours:`,
          searchError,
        );
        return this.getFallbackPracticalInfo(destinationInfo);
      }
    } catch (error) {
      console.error(
        '❌ Erreur lors de la génération des informations pratiques:',
        error,
      );
      return this.getFallbackPracticalInfo(destinationInfo);
    }
  }

  /**
   * Search for practical information with optimized queries
   */
  private async searchPracticalInfo(
    destinationInfo: DestinationInfo,
  ): Promise<any> {
    try {
      // Create mock session and dataStream
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
      const mockDataStream: UIMessageStreamWriter<ChatMessage> = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      } as UIMessageStreamWriter<ChatMessage>;

      const searchTool = web_search({
        session: mockSession,
        dataStream: mockDataStream,
      });

      // Optimized queries - fewer but more targeted for speed
      const queries = [
        `${destinationInfo.destination} ${destinationInfo.country} travel guide practical information transportation currency weather 2024`,
        `${destinationInfo.destination} emergency numbers visa requirements electricity plug type`,
        `${destinationInfo.destination} cost of living budget travel expenses local transportation`,
        `${destinationInfo.destination} ${destinationInfo.country} banks ATM currency exchange bureaux de change addresses phone numbers contact details`,
        `${destinationInfo.destination} ${destinationInfo.country} current exchange rates USD EUR GBP tipping guide payment methods mobile apps`,
      ];

      // Execute searches with reduced results for speed but comprehensive coverage
      const searchPromises = queries.map((query) =>
        (searchTool.execute as any)({
          queries: [query],
          maxResults: [4], // Slightly more results for better information
          topics: ['general'],
          searchDepth: ['basic'], // Basic for good balance of speed and quality
          language: 'fr',
        }),
      );

      const results = await Promise.all(searchPromises);
      console.log(
        `✅ Recherche web terminée pour ${destinationInfo.destination}`,
      );

      return { results, destination: destinationInfo.destination };
    } catch (error) {
      console.error('❌ Erreur lors de la recherche web:', error);
      throw error;
    }
  }

  /**
   * Generate practical info from search results using optimized schema
   */
  private async generatePracticalInfoFromSearch(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
    searchResults: any,
  ): Promise<PracticalInfo> {
    try {
      const generateObjectResult = await withAIModelResilience(
        () =>
          generateObject({
            model: this.model,
            system: `Tu es un expert en logistique de voyage avec 20 ans d'expérience. Génère des informations ULTRA-DÉTAILLÉES et PRATIQUES basées sur les résultats de recherche web fournis.

OBJECTIF: Créer le guide de voyage le plus complet et détaillé possible. Chaque section doit contenir un maximum d'informations pratiques, de conseils spécifiques, de prix exacts, d'horaires précis, et de détails que seul un expert local connaîtrait.

STYLE: Informations exhaustives avec tous les détails pratiques possibles - prix, horaires, adresses, numéros de téléphone, sites web, applications, cartes de transport, codes de réduction, astuces d'initiés, etc.

IMPORTANT: Utilise les informations trouvées dans les résultats de recherche ET enrichis avec des détails pratiques spécifiques à la destination.`,
            prompt: `Génère des informations pratiques ULTRA-DÉTAILLÉES pour ${destinationInfo.destination}, ${destinationInfo.country} basées sur ces résultats de recherche:

${JSON.stringify(searchResults, null, 2)}

Préférences utilisateur: ${JSON.stringify(userPreferences, null, 2)}

EXIGENCES SPÉCIFIQUES - MAXIMUM DE DÉTAILS REQUIS:

🚌 TRANSPORTATION:
- Prix exacts pour chaque mode de transport (billets simples, passes journaliers, hebdomadaires, mensuels)
- Horaires précis (premier/dernier train, fréquence)
- Stations/arrêts principaux avec noms exacts
- Applications mobiles spécifiques avec liens de téléchargement
- Cartes de transport (noms exacts, où les acheter, prix, validité)
- Codes de réduction et promotions
- Accessibilité détaillée (ascenseurs, rampes, assistance)
- Conseils d'initiés (meilleures places, éviter les heures de pointe)

💰 PAYMENT METHODS - ESSENTIAL INFORMATION FOR TRAVELERS

🏧 CASH WITHDRAWAL (ATMs)
• Network Coverage: Plus, Cirrus, Maestro, Visa/Plus, Mastercard/Cirrus
• Typical Fees: 2-5€ per withdrawal (varies by bank)
• Daily Limits: Usually 300-500€ (adjustable via your bank)
• Best Practices: 
  - Use ATMs inside banks for better security
  - Avoid standalone ATMs in tourist areas
  - Always decline dynamic currency conversion
  - Inform your bank of travel dates to avoid blocks

💳 CREDIT/DEBIT CARDS
• Widely Accepted: Visa, Mastercard
• Sometimes Accepted: American Express (check with merchant)
• Less Common: Discover, Diners Club
• Contactless Payments: Common up to 50€ (no PIN required)
• Chip & PIN: Standard for most transactions
• Foreign Transaction Fees: Typically 1-3% (check with your bank)
• Recommended: Get a travel-friendly card with no foreign transaction fees

💵 CASH HANDLING TIPS
• Recommended Amount: 100-200€ in small denominations (5€, 10€, 20€)
• Where to Exchange: 
  - Avoid airport exchanges (high fees)
  - Use bank ATMs for best rates
  - Consider Revolut/Wise for better exchange rates
• Security: 
  - Split cash between bags
  - Use hotel safes when available
  - Keep emergency cash separate

📱 MOBILE PAYMENT SOLUTIONS
• Popular Local Apps: Paylib, Lydia, Payconiq (varies by country)
• International Options: 
  - Apple Pay (widely accepted)
  - Google Pay (growing acceptance)
  - Samsung Pay (limited acceptance)
• Digital Wallets: 
  - Revolut (excellent exchange rates)
  - Wise (low international fees)
  - N26 (free ATM withdrawals)

💡 SMART PAYMENT TIPS
• Always have multiple payment methods
• Set up mobile banking alerts
• Know emergency contact numbers for lost/stolen cards
• Keep digital copies of important cards
• Use credit cards for better fraud protection

🌤️ WEATHER:
- Températures moyennes par mois avec variations jour/nuit
- Précipitations détaillées (mm, jours de pluie, type de pluie)
- Vêtements spécifiques recommandés par saison
- Accessoires essentiels (parapluie, crème solaire, lunettes)
- Phénomènes météo locaux (mousson, typhons, canicule)

📱 CONNECTIVITY:
- Opérateurs mobiles avec plans tarifaires exacts
- Où acheter les SIM (aéroport, magasins) avec adresses
- Wi-Fi gratuit: lieux exacts, mots de passe, limitations
- Applications VPN recommandées si nécessaire
- Vitesses internet typiques par zone

Fournis le MAXIMUM de détails pratiques possibles pour chaque section.`,
            schema: z.object({
              transportation: z.object({
                gettingThere: z.array(
                  z.object({
                    type: z.string(),
                    description: z.string(),
                    cost: z.string(),
                    detailedPricing: z.object({
                      economy: z.string(),
                      business: z.string().optional(),
                      firstClass: z.string().optional(),
                      seasonalVariations: z.array(z.string()),
                      advanceBookingDiscounts: z.array(z.string()),
                      lastMinuteDeals: z.array(z.string()),
                    }),
                    schedules: z.object({
                      frequency: z.string(),
                      firstDeparture: z.string(),
                      lastDeparture: z.string(),
                      peakHours: z.array(z.string()),
                      offPeakHours: z.array(z.string()),
                      weekendSchedule: z.string(),
                    }),
                    bookingPlatforms: z.array(
                      z.object({
                        name: z.string(),
                        website: z.string(),
                        mobileApp: z.string().optional(),
                        fees: z.string(),
                        cancellationPolicy: z.string(),
                      }),
                    ),
                    pros: z.array(z.string()),
                    cons: z.array(z.string()),
                    bestFor: z.array(z.string()),
                    accessibility: z.string(),
                    insiderTips: z.array(z.string()),
                    luggagePolicy: z.string(),
                    checkInProcess: z.string(),
                  }),
                ),
                localTransportation: z.array(
                  z.object({
                    type: z.string(),
                    description: z.string(),
                    cost: z.string(),
                    ticketTypes: z.array(
                      z.object({
                        name: z.string(),
                        price: z.string(),
                        validity: z.string(),
                        coverage: z.string(),
                        whereToBuy: z.array(z.string()),
                        discounts: z.array(z.string()),
                      }),
                    ),
                    routes: z.array(
                      z.object({
                        name: z.string(),
                        description: z.string(),
                        majorStops: z.array(z.string()),
                        frequency: z.string(),
                        operatingHours: z.string(),
                        specialNotes: z.string().optional(),
                      }),
                    ),
                    mobileApps: z.array(
                      z.object({
                        name: z.string(),
                        downloadLink: z.string(),
                        features: z.array(z.string()),
                        cost: z.string(),
                        languages: z.array(z.string()),
                      }),
                    ),
                    paymentMethods: z.array(z.string()),
                    pros: z.array(z.string()),
                    cons: z.array(z.string()),
                    bestFor: z.array(z.string()),
                    accessibility: z.object({
                      wheelchairAccess: z.string(),
                      elevators: z.string(),
                      audioAnnouncements: z.string(),
                      visualAids: z.string(),
                      assistanceServices: z.array(z.string()),
                    }),
                    insiderTips: z.array(z.string()),
                    rushHourAdvice: z.array(z.string()),
                    safetyTips: z.array(z.string()),
                    etiquette: z.array(z.string()),
                  }),
                ),
              }),
              weather: z.object({
                currentSeason: z.string(),
                monthlyBreakdown: z.array(
                  z.object({
                    month: z.string(),
                    avgHighTemp: z.string(),
                    avgLowTemp: z.string(),
                    precipitation: z.string(),
                    rainyDays: z.string(),
                    humidity: z.string(),
                    sunlightHours: z.string(),
                    weatherDescription: z.string(),
                  }),
                ),
                seasonalPatterns: z.array(
                  z.object({
                    season: z.string(),
                    months: z.array(z.string()),
                    characteristics: z.array(z.string()),
                    clothingRecommendations: z.array(z.string()),
                    activities: z.array(z.string()),
                    pros: z.array(z.string()),
                    cons: z.array(z.string()),
                  }),
                ),
                packingGuide: z.object({
                  essentials: z.array(z.string()),
                  clothing: z.object({
                    tops: z.array(z.string()),
                    bottoms: z.array(z.string()),
                    outerwear: z.array(z.string()),
                    footwear: z.array(z.string()),
                    accessories: z.array(z.string()),
                  }),
                  weatherSpecific: z.array(
                    z.object({
                      condition: z.string(),
                      items: z.array(z.string()),
                      brands: z.array(z.string()).optional(),
                    }),
                  ),
                }),
                localWeatherPhenomena: z.array(
                  z.object({
                    phenomenon: z.string(),
                    description: z.string(),
                    whenItOccurs: z.string(),
                    howToPrepare: z.array(z.string()),
                    safetyTips: z.array(z.string()),
                  }),
                ),
                weatherApps: z.array(
                  z.object({
                    name: z.string(),
                    accuracy: z.string(),
                    features: z.array(z.string()),
                    downloadLink: z.string(),
                  }),
                ),
                bestTimeToVisit: z.object({
                  overall: z.string(),
                  forWeather: z.string(),
                  forCrowds: z.string(),
                  forPrices: z.string(),
                  forActivities: z.string(),
                }),
              }),
              practicalTips: z.array(
                z.object({
                  category: z.string(),
                  tips: z.array(z.string()),
                }),
              ),
              emergencyInfo: z.object({
                emergencyNumbers: z.object({
                  police: z.string(),
                  ambulance: z.string(),
                  fireService: z.string(),
                }),
                hospitals: z.array(
                  z.object({
                    name: z.string(),
                    address: z.string(),
                    phone: z.string(),
                    services: z.array(z.string()),
                  }),
                ),
                embassies: z.array(
                  z.object({
                    country: z.string(),
                    name: z.string(),
                    address: z.string(),
                    phone: z.string(),
                    website: z.string(),
                  }),
                ),
                medicalServices: z.array(
                  z.object({
                    type: z.string(),
                    name: z.string(),
                    description: z.string(),
                    contact: z.string(),
                  }),
                ),
                travelInsuranceRecommendations: z.array(z.string()),
              }),
              visaRequirements: z.string(),
              currencyInfo: z.object({
                currency: z.object({
                  name: z.string(),
                  code: z.string(),
                  symbol: z.string(),
                  subunit: z.string(),
                }),
                exchangeRates: z.object({
                  usd: z.number(),
                  eur: z.number(),
                  gbp: z.number(),
                  lastUpdated: z.string(),
                  trend: z.string(),
                  monthlyTrend: z.string(),
                  bestExchangeLocations: z.array(
                    z.object({
                      name: z.string(),
                      type: z.string(), // bank, exchange bureau, airport, etc.
                      address: z.string(),
                      phone: z.string().optional(),
                      website: z.string().optional(),
                      hours: z.string(),
                      exchangeRate: z.string(),
                      fees: z.string(),
                      minimumAmount: z.string().optional(),
                      acceptedCurrencies: z.array(z.string()),
                      notes: z.string().optional(),
                    }),
                  ),
                }),
                costs: z.object({
                  budget: z.object({
                    dailyBudget: z.string(),
                    accommodation: z.string(),
                    meals: z.string(),
                    transport: z.string(),
                  }),
                  midRange: z.object({
                    dailyBudget: z.string(),
                    accommodation: z.string(),
                    meals: z.string(),
                    transport: z.string(),
                  }),
                  luxury: z.object({
                    dailyBudget: z.string(),
                    accommodation: z.string(),
                    meals: z.string(),
                    transport: z.string(),
                  }),
                  specificCosts: z.array(
                    z.object({
                      item: z.string(),
                      cost: z.string(),
                      notes: z.string(),
                    }),
                  ),
                }),
                paymentMethods: z.object({
                  creditCards: z.object({
                    acceptance: z.string(),
                    preferredCards: z.array(z.string()),
                    fees: z.string(),
                    tips: z.array(z.string()),
                  }),
                  cash: z.object({
                    importance: z.string(),
                    denominations: z.array(z.string()),
                    tips: z.array(z.string()),
                  }),
                  digitalPayments: z.array(
                    z.object({
                      name: z.string(),
                      availability: z.string(),
                      description: z.string(),
                    }),
                  ),
                }),
                banking: z.object({
                  bankingHours: z.object({
                    weekdays: z.string(),
                    saturday: z.string(),
                    sunday: z.string(),
                    holidays: z.string(),
                  }),
                  atmInfo: z.object({
                    availability: z.string(),
                    networks: z.array(z.string()),
                    fees: z.string(),
                    dailyLimits: z.string(),
                    locations: z.array(
                      z.object({
                        name: z.string(),
                        address: z.string(),
                        network: z.string(),
                        fees: z.string(),
                        languages: z.array(z.string()),
                        accessibility: z.string(),
                        operatingHours: z.string(),
                      }),
                    ),
                  }),
                  bankBranches: z.array(
                    z.object({
                      name: z.string(),
                      address: z.string(),
                      phone: z.string(),
                      website: z.string().optional(),
                      hours: z.string(),
                      services: z.array(z.string()),
                      exchangeServices: z.boolean(),
                      englishSupport: z.boolean(),
                      fees: z.object({
                        currencyExchange: z.string(),
                        wireTransfer: z.string(),
                        accountOpening: z.string().optional(),
                      }),
                    }),
                  ),
                }),
                tipping: z.object({
                  culture: z.string(),
                  generalGuidelines: z.string(),
                  byService: z.object({
                    restaurants: z.object({
                      amount: z.string(),
                      method: z.string(), // cash, card, included
                      timing: z.string(), // when to tip
                      notes: z.string(),
                    }),
                    taxis: z.object({
                      amount: z.string(),
                      method: z.string(),
                      timing: z.string(),
                      notes: z.string(),
                    }),
                    hotels: z.object({
                      bellhop: z.string(),
                      housekeeping: z.string(),
                      concierge: z.string(),
                      roomService: z.string(),
                      method: z.string(),
                      notes: z.string(),
                    }),
                    tours: z.object({
                      amount: z.string(),
                      method: z.string(),
                      timing: z.string(),
                      notes: z.string(),
                    }),
                    other: z.array(
                      z.object({
                        service: z.string(),
                        amount: z.string(),
                        method: z.string(),
                        timing: z.string(),
                        notes: z.string(),
                      }),
                    ),
                  }),
                  commonMistakes: z.array(z.string()),
                  culturalContext: z.string(),
                }),
                moneyApps: z.array(
                  z.object({
                    name: z.string(),
                    description: z.string(),
                    availability: z.string(),
                    downloadLink: z.string(),
                    setupInstructions: z.array(z.string()),
                    acceptedAt: z.array(z.string()),
                    fees: z.string(),
                    features: z.array(z.string()),
                    requirements: z.array(z.string()),
                    languages: z.array(z.string()),
                    customerSupport: z.string(),
                  }),
                ),
              }),
              internetConnectivity: z.object({
                overview: z.string(),
                mobileOperators: z.array(
                  z.object({
                    name: z.string(),
                    coverage: z.string(),
                    dataPlans: z.array(
                      z.object({
                        name: z.string(),
                        data: z.string(),
                        validity: z.string(),
                        price: z.string(),
                        features: z.array(z.string()),
                      }),
                    ),
                    simCardLocations: z.array(
                      z.object({
                        location: z.string(),
                        address: z.string(),
                        hours: z.string(),
                        languages: z.array(z.string()),
                      }),
                    ),
                    activationProcess: z.array(z.string()),
                    customerService: z.string(),
                  }),
                ),
                wifiOptions: z.object({
                  publicWifi: z.array(
                    z.object({
                      location: z.string(),
                      availability: z.string(),
                      speed: z.string(),
                      cost: z.string(),
                      accessMethod: z.string(),
                      limitations: z.array(z.string()),
                    }),
                  ),
                  pocketWifi: z.array(
                    z.object({
                      provider: z.string(),
                      dailyRate: z.string(),
                      dataLimit: z.string(),
                      batteryLife: z.string(),
                      coverage: z.string(),
                      rentalLocations: z.array(z.string()),
                      bookingWebsite: z.string(),
                    }),
                  ),
                  hotelWifi: z.object({
                    availability: z.string(),
                    speed: z.string(),
                    cost: z.string(),
                    reliability: z.string(),
                    tips: z.array(z.string()),
                  }),
                }),
                internetCafes: z.array(
                  z.object({
                    name: z.string(),
                    address: z.string(),
                    hours: z.string(),
                    pricePerHour: z.string(),
                    services: z.array(z.string()),
                    equipment: z.array(z.string()),
                  }),
                ),
                speedTests: z.object({
                  averageDownload: z.string(),
                  averageUpload: z.string(),
                  byArea: z.array(
                    z.object({
                      area: z.string(),
                      downloadSpeed: z.string(),
                      uploadSpeed: z.string(),
                      reliability: z.string(),
                    }),
                  ),
                }),
                connectivityApps: z.array(
                  z.object({
                    name: z.string(),
                    purpose: z.string(),
                    downloadLink: z.string(),
                    cost: z.string(),
                    features: z.array(z.string()),
                    userRating: z.string(),
                  }),
                ),
                troubleshooting: z.array(
                  z.object({
                    problem: z.string(),
                    solutions: z.array(z.string()),
                    contacts: z.array(z.string()),
                  }),
                ),
              }),
              languageInfo: z.object({
                officialLanguage: z.string(),
                languageLevel: z.string(),
                usefulPhrases: z.array(
                  z.object({
                    phrase: z.string(),
                    translation: z.string(),
                    pronunciation: z.string(),
                    context: z.string(),
                  }),
                ),
                languageApps: z.array(
                  z.object({
                    name: z.string(),
                    description: z.string(),
                    platform: z.string(),
                    cost: z.string(),
                    features: z.array(z.string()),
                  }),
                ),
                englishAvailability: z.string(),
              }),
              electricityInfo: z.object({
                voltage: z.string(),
                frequency: z.string(),
                plugType: z.string(),
                adapterNeeded: z.boolean(),
                adapterType: z.string(),
                images: z.array(
                  z.object({
                    url: z.string(),
                    description: z.string(),
                    type: z.string(),
                  }),
                ),
              }),
              businessHours: z.array(
                z.object({
                  category: z.string(),
                  hours: z.object({
                    monday: z.string(),
                    tuesday: z.string(),
                    wednesday: z.string(),
                    thursday: z.string(),
                    friday: z.string(),
                    saturday: z.string(),
                    sunday: z.string(),
                  }),
                  notes: z.string(),
                }),
              ),
              packingChecklist: z.array(z.string()),
              customsRegulations: z.array(z.string()),
              travelInsurance: z.string(),
              healthInfo: z.object({
                vaccinations: z.array(z.string()),
                healthPrecautions: z.array(z.string()),
                covid19Info: z.string(),
                localMedication: z.array(z.string()),
                waterSafety: z.string(),
                foodSafety: z.string(),
              }),
              accessibilityInfo: z.object({
                overview: z.string(),
                publicTransport: z.string(),
                attractions: z.string(),
                hotels: z.string(),
                restaurants: z.string(),
                specialServices: z.array(z.string()),
              }),
              familyInfo: z.object({
                overview: z.string(),
                kidFriendlyAttractions: z.array(z.string()),
                childcareServices: z.array(z.string()),
                familyFriendlyRestaurants: z.array(z.string()),
                tips: z.array(z.string()),
              }),
              sustainabilityInfo: z.object({
                overview: z.string(),
                ecoFriendlyTransport: z.array(z.string()),
                sustainableAccommodations: z.array(z.string()),
                localInitiatives: z.array(z.string()),
                responsibleTourismTips: z.array(z.string()),
              }),
              resources: z.object({
                usefulApps: z.array(
                  z.object({
                    name: z.string(),
                    description: z.string(),
                    platform: z.string(),
                    cost: z.string(),
                  }),
                ),
                recommendedBooks: z.array(
                  z.object({
                    title: z.string(),
                    author: z.string(),
                    description: z.string(),
                    isbn: z.string(),
                  }),
                ),
                officialWebsites: z.array(
                  z.object({
                    name: z.string(),
                    url: z.string(),
                    description: z.string(),
                    category: z.string(),
                  }),
                ),
              }),
            }),
            temperature: 0.3,
          }),
        'gemini-2.0-flash',
        'practical-info-generation',
        () =>
          Promise.resolve(
            createMockGenerateObjectResult(
              this.getFallbackPracticalInfo(destinationInfo),
            ),
          ),
      );

      return generateObjectResult.object as PracticalInfo;
    } catch (error) {
      console.error(
        '❌ Erreur lors de la génération à partir des résultats de recherche:',
        error,
      );
      throw error;
    }
  }

  /**
   * Get fallback practical information
   */
  private getFallbackPracticalInfo(
    destinationInfo: DestinationInfo,
  ): PracticalInfo {
    return {
      transportation: {
        gettingThere: [
          {
            type: 'Flight',
            description: `Fly to ${destinationInfo.destination} via major international airports`,
            cost: 'Varies by season and booking time',
            pros: ['Fast', 'Convenient', 'Multiple airlines'],
            cons: ['Can be expensive', 'Airport transfers needed'],
            bestFor: ['Long distances', 'Time-sensitive travel'],
            accessibility: 'Most airports have accessibility services',
            bookingInfo: 'Book in advance for better prices',
          },
          {
            type: 'Train',
            description: 'Train connections to major cities',
            cost: 'Moderate, varies by class',
            pros: ['Scenic', 'City center to city center', 'Comfortable'],
            cons: ['Slower than flying', 'Limited routes'],
            bestFor: ['Regional travel', 'Scenic journeys'],
            accessibility: 'Most modern trains are accessible',
          },
        ],
        localTransportation: [
          {
            type: 'Public Transport',
            description: 'Buses, metro, and local trains',
            cost: 'Budget-friendly',
            pros: ['Cheap', 'Extensive network', 'Local experience'],
            cons: ['Can be crowded', 'Language barriers'],
            bestFor: ['Budget travelers', 'Short distances'],
            accessibility: 'Varies by city and system',
          },
          {
            type: 'Taxi/Rideshare',
            description: 'Taxis and ride-sharing services',
            cost: 'Moderate to expensive',
            pros: ['Convenient', 'Door-to-door', 'Available 24/7'],
            cons: ['More expensive', 'Traffic delays'],
            bestFor: ['Convenience', 'Late night travel'],
            accessibility: 'Some vehicles available for wheelchairs',
          },
        ],
        dayTrips: [
          {
            type: 'Organized Tours',
            description: 'Day trips to nearby attractions',
            cost: 'Moderate',
            pros: ['Guided', 'Transportation included', 'No planning needed'],
            cons: ['Less flexible', 'Group pace'],
            bestFor: ['First-time visitors', 'Popular attractions'],
            accessibility: 'Many tours offer accessible options',
          },
        ],
      },
      weather: {
        season: 'Check current season for destination',
        averageTemperature: 'Varies by season and location',
        precipitation: 'Check local weather patterns',
        whatToPack: [
          'Comfortable walking shoes',
          'Weather-appropriate clothing',
          'Rain jacket or umbrella',
          'Sunscreen and hat',
          'Layers for temperature changes',
        ],
        bestTimeToVisit: 'Research seasonal weather and tourist patterns',
        seasonalInfo: [
          {
            season: 'Spring',
            description: 'Mild temperatures, blooming flowers',
            averageTemperature: 'Moderate',
            precipitation: 'Variable',
            crowdLevel: 'Moderate',
            recommendation: 'Good for outdoor activities',
          },
          {
            season: 'Summer',
            description: 'Warm weather, peak tourist season',
            averageTemperature: 'Warm to hot',
            precipitation: 'Varies by region',
            crowdLevel: 'High',
            recommendation: 'Book accommodations early',
          },
        ],
      },
      practicalTips: [
        {
          category: 'Safety',
          tips: [
            'Keep copies of important documents',
            'Be aware of your surroundings',
            'Use hotel safes for valuables',
          ],
        },
        {
          category: 'Communication',
          tips: [
            'Learn basic local phrases',
            'Download translation apps',
            'Keep emergency contacts handy',
          ],
        },
      ],
      emergencyInfo: {
        emergencyNumbers: {
          police: 'Check local emergency numbers',
          ambulance: 'Check local emergency numbers',
          fireService: 'Check local emergency numbers',
        },
        hospitals: [],
        embassies: [],
        medicalServices: [],
        travelInsuranceRecommendations: [
          'Purchase comprehensive travel insurance',
          'Ensure medical coverage abroad',
          'Keep insurance documents accessible',
        ],
      },
      visaRequirements: 'Check embassy website for current visa requirements',
      currencyInfo: {
        currency: {
          name: 'Local Currency',
          code: 'XXX',
          symbol: '$',
          subunit: 'cents',
        },
        exchangeRates: {
          usd: 1.0,
          eur: 0.85,
          gbp: 0.75,
          lastUpdated: 'Check current rates',
          trend: 'Stable',
          bestExchangeLocations: ['Banks', 'ATMs', 'Exchange offices'],
        },
        costs: {
          budget: {
            dailyBudget: '$30-50',
            accommodation: '$15-25',
            meals: '$10-15',
            transport: '$5-10',
          },
          midRange: {
            dailyBudget: '$50-100',
            accommodation: '$25-60',
            meals: '$15-30',
            transport: '$10-20',
          },
          luxury: {
            dailyBudget: '$100+',
            accommodation: '$60+',
            meals: '$30+',
            transport: '$20+',
          },
          specificCosts: [],
        },
        paymentMethods: {
          creditCards: {
            acceptance: 'Widely accepted',
            preferredCards: ['Visa', 'Mastercard'],
            fees: 'Check with bank',
            tips: ['Notify bank of travel'],
          },
          cash: {
            importance: 'Essential for small vendors',
            denominations: ['Small bills useful'],
            tips: ['Keep cash secure'],
          },
          digitalPayments: [],
        },
        banking: {
          bankingHours: {
            weekdays: '9:00-17:00',
            saturday: '9:00-13:00',
            sunday: 'Closed',
            holidays: 'Closed',
          },
          atmInfo: {
            availability: 'Widely available',
            networks: ['Plus', 'Cirrus'],
            fees: 'Check with bank',
            dailyLimits: 'Varies',
            locations: ['Banks', 'Shopping centers'],
          },
          bankBranches: [],
        },
        tipping: {
          culture: 'Varies by country',
          restaurants: '10-15%',
          taxis: '10%',
          hotels: '$1-2 per service',
          tours: '$5-10',
          other: [],
        },
        moneyApps: [],
      },
      internetConnectivity: {
        wifiAvailability: 'Available in hotels and cafes',
        simCardInfo: 'Available at airport and mobile shops',
        internetCafes: [],
        publicWifiSpots: ['Hotels', 'Cafes', 'Public areas'],
        typicalSpeeds: 'Moderate to fast in urban areas',
        recommendedApps: [],
      },
      languageInfo: {
        officialLanguage: `Local language of ${destinationInfo.country}`,
        languageLevel: 'Basic phrases helpful',
        usefulPhrases: [],
        languageApps: [],
        englishAvailability: 'Widely spoken in tourist areas',
      },
      electricityInfo: {
        voltage: 'Check local standard',
        frequency: 'Check local standard',
        plugType: 'Check local standard',
        adapterNeeded: true,
        adapterType: 'Universal adapter recommended',
        images: [],
      },
      businessHours: [],
      packingChecklist: [
        'Passport and documents',
        'Appropriate clothing',
        'Medications',
        'Travel adapter',
        'Comfortable shoes',
      ],
      customsRegulations: [
        'Check customs regulations',
        'Declare items as required',
        'Know duty-free allowances',
      ],
      travelInsurance: 'Travel insurance recommended',
      healthInfo: {
        vaccinations: ['Check with doctor'],
        healthPrecautions: ['Drink bottled water', 'Use sunscreen'],
        covid19Info: 'Check current requirements',
        localMedication: [],
        waterSafety: 'Bottled water recommended',
        foodSafety: 'Eat at reputable establishments',
      },
      accessibilityInfo: {
        overview: 'Accessibility varies',
        publicTransport: 'Some accessible options',
        attractions: 'Major sites often accessible',
        hotels: 'International chains typically accessible',
        restaurants: 'Varies by establishment',
        specialServices: [],
      },
      familyInfo: {
        overview: 'Family-friendly destination',
        kidFriendlyAttractions: [],
        childcareServices: [],
        familyFriendlyRestaurants: [],
        tips: [],
      },
      sustainabilityInfo: {
        overview: 'Growing focus on sustainable tourism',
        ecoFriendlyTransport: [],
        sustainableAccommodations: [],
        localInitiatives: [],
        responsibleTourismTips: [],
      },
      resources: {
        usefulApps: [],
        recommendedBooks: [],
        officialWebsites: [],
      },
    };
  }
}

// Type definitions (simplified)
export interface PracticalInfo {
  transportation: any;
  weather: any;
  practicalTips: any;
  emergencyInfo: any;
  visaRequirements: string;
  currencyInfo: any;
  internetConnectivity: any;
  languageInfo: any;
  electricityInfo: any;
  businessHours: any;
  packingChecklist: string[];
  customsRegulations: string[];
  travelInsurance: string;
  healthInfo: any;
  accessibilityInfo: any;
  familyInfo: any;
  sustainabilityInfo: any;
  resources: any;
}
